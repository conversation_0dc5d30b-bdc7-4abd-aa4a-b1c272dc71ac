hoistPattern:
  - '*'
hoistedDependencies:
  '@ai-sdk/provider-utils@2.2.8(zod@3.25.76)':
    '@ai-sdk/provider-utils': private
  '@ai-sdk/provider@1.1.3':
    '@ai-sdk/provider': private
  '@ai-sdk/react@1.2.12(react@19.1.0)(zod@3.25.76)':
    '@ai-sdk/react': private
  '@ai-sdk/ui-utils@1.2.11(zod@3.25.76)':
    '@ai-sdk/ui-utils': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@aws-crypto/crc32@5.2.0':
    '@aws-crypto/crc32': private
  '@aws-crypto/crc32c@5.2.0':
    '@aws-crypto/crc32c': private
  '@aws-crypto/sha1-browser@5.2.0':
    '@aws-crypto/sha1-browser': private
  '@aws-crypto/sha256-browser@5.2.0':
    '@aws-crypto/sha256-browser': private
  '@aws-crypto/sha256-js@5.2.0':
    '@aws-crypto/sha256-js': private
  '@aws-crypto/supports-web-crypto@5.2.0':
    '@aws-crypto/supports-web-crypto': private
  '@aws-crypto/util@5.2.0':
    '@aws-crypto/util': private
  '@aws-sdk/client-sso@3.848.0':
    '@aws-sdk/client-sso': private
  '@aws-sdk/core@3.846.0':
    '@aws-sdk/core': private
  '@aws-sdk/credential-provider-env@3.846.0':
    '@aws-sdk/credential-provider-env': private
  '@aws-sdk/credential-provider-http@3.846.0':
    '@aws-sdk/credential-provider-http': private
  '@aws-sdk/credential-provider-ini@3.848.0':
    '@aws-sdk/credential-provider-ini': private
  '@aws-sdk/credential-provider-node@3.848.0':
    '@aws-sdk/credential-provider-node': private
  '@aws-sdk/credential-provider-process@3.846.0':
    '@aws-sdk/credential-provider-process': private
  '@aws-sdk/credential-provider-sso@3.848.0':
    '@aws-sdk/credential-provider-sso': private
  '@aws-sdk/credential-provider-web-identity@3.848.0':
    '@aws-sdk/credential-provider-web-identity': private
  '@aws-sdk/middleware-bucket-endpoint@3.840.0':
    '@aws-sdk/middleware-bucket-endpoint': private
  '@aws-sdk/middleware-expect-continue@3.840.0':
    '@aws-sdk/middleware-expect-continue': private
  '@aws-sdk/middleware-flexible-checksums@3.846.0':
    '@aws-sdk/middleware-flexible-checksums': private
  '@aws-sdk/middleware-host-header@3.840.0':
    '@aws-sdk/middleware-host-header': private
  '@aws-sdk/middleware-location-constraint@3.840.0':
    '@aws-sdk/middleware-location-constraint': private
  '@aws-sdk/middleware-logger@3.840.0':
    '@aws-sdk/middleware-logger': private
  '@aws-sdk/middleware-recursion-detection@3.840.0':
    '@aws-sdk/middleware-recursion-detection': private
  '@aws-sdk/middleware-sdk-s3@3.846.0':
    '@aws-sdk/middleware-sdk-s3': private
  '@aws-sdk/middleware-ssec@3.840.0':
    '@aws-sdk/middleware-ssec': private
  '@aws-sdk/middleware-user-agent@3.848.0':
    '@aws-sdk/middleware-user-agent': private
  '@aws-sdk/nested-clients@3.848.0':
    '@aws-sdk/nested-clients': private
  '@aws-sdk/region-config-resolver@3.840.0':
    '@aws-sdk/region-config-resolver': private
  '@aws-sdk/signature-v4-multi-region@3.846.0':
    '@aws-sdk/signature-v4-multi-region': private
  '@aws-sdk/token-providers@3.848.0':
    '@aws-sdk/token-providers': private
  '@aws-sdk/types@3.840.0':
    '@aws-sdk/types': private
  '@aws-sdk/util-arn-parser@3.804.0':
    '@aws-sdk/util-arn-parser': private
  '@aws-sdk/util-endpoints@3.848.0':
    '@aws-sdk/util-endpoints': private
  '@aws-sdk/util-locate-window@3.804.0':
    '@aws-sdk/util-locate-window': private
  '@aws-sdk/util-user-agent-browser@3.840.0':
    '@aws-sdk/util-user-agent-browser': private
  '@aws-sdk/util-user-agent-node@3.848.0':
    '@aws-sdk/util-user-agent-node': private
  '@aws-sdk/xml-builder@3.821.0':
    '@aws-sdk/xml-builder': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.31.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.3.0':
    '@eslint/config-helpers': public
  '@eslint/core@0.15.1':
    '@eslint/core': public
  '@eslint/js@9.31.0':
    '@eslint/js': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.3.3':
    '@eslint/plugin-kit': public
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-arm64@0.34.3':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.3':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.2.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.2.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.2.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.2.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.2.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.2.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.2.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.2.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.2.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.3':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.3':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-ppc64@0.34.3':
    '@img/sharp-linux-ppc64': private
  '@img/sharp-linux-s390x@0.34.3':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.3':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.3':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.3':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.3':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-arm64@0.34.3':
    '@img/sharp-win32-arm64': private
  '@img/sharp-win32-ia32@0.34.3':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.3':
    '@img/sharp-win32-x64': private
  '@ioredis/commands@1.2.0':
    '@ioredis/commands': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@next/env@15.4.1':
    '@next/env': private
  '@next/eslint-plugin-next@15.4.1':
    '@next/eslint-plugin-next': public
  '@next/swc-darwin-arm64@15.4.1':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.4.1':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.4.1':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.4.1':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.4.1':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.4.1':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.4.1':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.4.1':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.8)(react@19.1.0)':
    '@radix-ui/react-compose-refs': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.12.0':
    '@rushstack/eslint-patch': public
  '@smithy/abort-controller@4.0.4':
    '@smithy/abort-controller': private
  '@smithy/chunked-blob-reader-native@4.0.0':
    '@smithy/chunked-blob-reader-native': private
  '@smithy/chunked-blob-reader@5.0.0':
    '@smithy/chunked-blob-reader': private
  '@smithy/config-resolver@4.1.4':
    '@smithy/config-resolver': private
  '@smithy/core@3.7.1':
    '@smithy/core': private
  '@smithy/credential-provider-imds@4.0.6':
    '@smithy/credential-provider-imds': private
  '@smithy/eventstream-codec@4.0.4':
    '@smithy/eventstream-codec': private
  '@smithy/eventstream-serde-browser@4.0.4':
    '@smithy/eventstream-serde-browser': private
  '@smithy/eventstream-serde-config-resolver@4.1.2':
    '@smithy/eventstream-serde-config-resolver': private
  '@smithy/eventstream-serde-node@4.0.4':
    '@smithy/eventstream-serde-node': private
  '@smithy/eventstream-serde-universal@4.0.4':
    '@smithy/eventstream-serde-universal': private
  '@smithy/fetch-http-handler@5.1.0':
    '@smithy/fetch-http-handler': private
  '@smithy/hash-blob-browser@4.0.4':
    '@smithy/hash-blob-browser': private
  '@smithy/hash-node@4.0.4':
    '@smithy/hash-node': private
  '@smithy/hash-stream-node@4.0.4':
    '@smithy/hash-stream-node': private
  '@smithy/invalid-dependency@4.0.4':
    '@smithy/invalid-dependency': private
  '@smithy/is-array-buffer@4.0.0':
    '@smithy/is-array-buffer': private
  '@smithy/md5-js@4.0.4':
    '@smithy/md5-js': private
  '@smithy/middleware-content-length@4.0.4':
    '@smithy/middleware-content-length': private
  '@smithy/middleware-endpoint@4.1.16':
    '@smithy/middleware-endpoint': private
  '@smithy/middleware-retry@4.1.17':
    '@smithy/middleware-retry': private
  '@smithy/middleware-serde@4.0.8':
    '@smithy/middleware-serde': private
  '@smithy/middleware-stack@4.0.4':
    '@smithy/middleware-stack': private
  '@smithy/node-config-provider@4.1.3':
    '@smithy/node-config-provider': private
  '@smithy/node-http-handler@4.1.0':
    '@smithy/node-http-handler': private
  '@smithy/property-provider@4.0.4':
    '@smithy/property-provider': private
  '@smithy/protocol-http@5.1.2':
    '@smithy/protocol-http': private
  '@smithy/querystring-builder@4.0.4':
    '@smithy/querystring-builder': private
  '@smithy/querystring-parser@4.0.4':
    '@smithy/querystring-parser': private
  '@smithy/service-error-classification@4.0.6':
    '@smithy/service-error-classification': private
  '@smithy/shared-ini-file-loader@4.0.4':
    '@smithy/shared-ini-file-loader': private
  '@smithy/signature-v4@5.1.2':
    '@smithy/signature-v4': private
  '@smithy/smithy-client@4.4.8':
    '@smithy/smithy-client': private
  '@smithy/types@4.3.1':
    '@smithy/types': private
  '@smithy/url-parser@4.0.4':
    '@smithy/url-parser': private
  '@smithy/util-base64@4.0.0':
    '@smithy/util-base64': private
  '@smithy/util-body-length-browser@4.0.0':
    '@smithy/util-body-length-browser': private
  '@smithy/util-body-length-node@4.0.0':
    '@smithy/util-body-length-node': private
  '@smithy/util-buffer-from@4.0.0':
    '@smithy/util-buffer-from': private
  '@smithy/util-config-provider@4.0.0':
    '@smithy/util-config-provider': private
  '@smithy/util-defaults-mode-browser@4.0.24':
    '@smithy/util-defaults-mode-browser': private
  '@smithy/util-defaults-mode-node@4.0.24':
    '@smithy/util-defaults-mode-node': private
  '@smithy/util-endpoints@3.0.6':
    '@smithy/util-endpoints': private
  '@smithy/util-hex-encoding@4.0.0':
    '@smithy/util-hex-encoding': private
  '@smithy/util-middleware@4.0.4':
    '@smithy/util-middleware': private
  '@smithy/util-retry@4.0.6':
    '@smithy/util-retry': private
  '@smithy/util-stream@4.2.3':
    '@smithy/util-stream': private
  '@smithy/util-uri-escape@4.0.0':
    '@smithy/util-uri-escape': private
  '@smithy/util-utf8@4.0.0':
    '@smithy/util-utf8': private
  '@smithy/util-waiter@4.0.6':
    '@smithy/util-waiter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.11':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.11':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.11':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.11':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.11':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.11':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.11':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.11':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.11':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.11':
    '@tailwindcss/oxide': private
  '@types/diff-match-patch@1.0.36':
    '@types/diff-match-patch': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/uuid@9.0.8':
    '@types/uuid': private
  '@typescript-eslint/eslint-plugin@8.37.0(@typescript-eslint/parser@8.37.0(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@8.37.0(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/project-service@8.37.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': public
  '@typescript-eslint/scope-manager@8.37.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/tsconfig-utils@8.37.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': public
  '@typescript-eslint/type-utils@8.37.0(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.37.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@8.37.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@8.37.0(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@8.37.0':
    '@typescript-eslint/visitor-keys': public
  '@unrs/resolver-binding-android-arm-eabi@1.11.1':
    '@unrs/resolver-binding-android-arm-eabi': private
  '@unrs/resolver-binding-android-arm64@1.11.1':
    '@unrs/resolver-binding-android-arm64': private
  '@unrs/resolver-binding-darwin-arm64@1.11.1':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.11.1':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.11.1':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.11.1':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.11.1':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.11.1':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.11.1':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.11.1':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.11.1':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.11.1':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.11.1':
    '@unrs/resolver-binding-win32-x64-msvc': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.9:
    array-includes: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  async-function@1.0.0:
    async-function: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  balanced-match@1.0.2:
    balanced-match: private
  bowser@2.11.0:
    bowser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chownr@3.0.0:
    chownr: private
  client-only@0.0.1:
    client-only: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  concat-map@0.0.1:
    concat-map: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  deep-is@0.1.4:
    deep-is: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  denque@2.1.0:
    denque: private
  dequal@2.0.3:
    dequal: private
  detect-libc@2.0.4:
    detect-libc: private
  diff-match-patch@1.0.5:
    diff-match-patch: private
  doctrine@2.1.0:
    doctrine: private
  dunder-proto@1.0.1:
    dunder-proto: private
  emoji-regex@9.2.2:
    emoji-regex: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: public
  eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0)(eslint@9.31.0(jiti@2.4.2)):
    eslint-import-resolver-typescript: public
  eslint-module-utils@2.12.1(@typescript-eslint/parser@8.37.0(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1)(eslint@9.31.0(jiti@2.4.2)):
    eslint-module-utils: public
  eslint-plugin-import@2.32.0(@typescript-eslint/parser@8.37.0(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3))(eslint-import-resolver-typescript@3.10.1)(eslint@9.31.0(jiti@2.4.2)):
    eslint-plugin-import: public
  eslint-plugin-jsx-a11y@6.10.2(eslint@9.31.0(jiti@2.4.2)):
    eslint-plugin-jsx-a11y: public
  eslint-plugin-react-hooks@5.2.0(eslint@9.31.0(jiti@2.4.2)):
    eslint-plugin-react-hooks: public
  eslint-plugin-react@7.37.5(eslint@9.31.0(jiti@2.4.2)):
    eslint-plugin-react: public
  eslint-scope@8.4.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: public
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.1:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-xml-parser@5.2.5:
    fast-xml-parser: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  for-each@0.3.5:
    for-each: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  globals@14.0.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  internal-slot@1.1.0:
    internal-slot: private
  ioredis@5.6.1:
    ioredis: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-bun-module@2.0.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jiti@2.4.2:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@1.0.2:
    json5: private
  jsondiffpatch@0.6.0:
    jsondiffpatch: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  locate-path@6.0.0:
    locate-path: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.isarguments@3.1.0:
    lodash.isarguments: private
  lodash.merge@4.6.2:
    lodash.merge: private
  loose-envify@1.4.0:
    loose-envify: private
  magic-string@0.30.17:
    magic-string: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  napi-postinstall@0.3.0:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  optionator@0.9.4:
    optionator: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prop-types@15.8.1:
    prop-types: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  react-is@16.13.1:
    react-is: private
  redis-errors@1.2.0:
    redis-errors: private
  redis-parser@3.0.0:
    redis-parser: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  scheduler@0.26.0:
    scheduler: private
  secure-json-parse@2.7.0:
    secure-json-parse: private
  semver@6.3.1:
    semver: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  sharp@0.34.3:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  source-map-js@1.2.1:
    source-map-js: private
  stable-hash@0.0.5:
    stable-hash: private
  standard-as-callback@2.1.0:
    standard-as-callback: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strnum@2.1.1:
    strnum: private
  styled-jsx@5.1.6(react@19.1.0):
    styled-jsx: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swr@2.3.4(react@19.1.0):
    swr: private
  tapable@2.2.2:
    tapable: private
  tar@7.4.3:
    tar: private
  throttleit@2.1.0:
    throttleit: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  uncrypto@0.1.3:
    uncrypto: private
  undici-types@6.21.0:
    undici-types: private
  unrs-resolver@1.11.1:
    unrs-resolver: private
  uri-js@4.4.1:
    uri-js: private
  use-sync-external-store@1.5.0(react@19.1.0):
    use-sync-external-store: private
  uuid@9.0.1:
    uuid: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  yallist@5.0.0:
    yallist: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zod-to-json-schema@3.24.6(zod@3.25.76):
    zod-to-json-schema: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.14.2
pendingBuilds: []
prunedAt: Fri, 18 Jul 2025 00:41:10 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.4'
  - '@emnapi/runtime@1.4.4'
  - '@emnapi/wasi-threads@1.0.3'
  - '@img/sharp-darwin-x64@0.34.3'
  - '@img/sharp-libvips-darwin-x64@1.2.0'
  - '@img/sharp-libvips-linux-arm64@1.2.0'
  - '@img/sharp-libvips-linux-arm@1.2.0'
  - '@img/sharp-libvips-linux-ppc64@1.2.0'
  - '@img/sharp-libvips-linux-s390x@1.2.0'
  - '@img/sharp-libvips-linux-x64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.2.0'
  - '@img/sharp-linux-arm64@0.34.3'
  - '@img/sharp-linux-arm@0.34.3'
  - '@img/sharp-linux-ppc64@0.34.3'
  - '@img/sharp-linux-s390x@0.34.3'
  - '@img/sharp-linux-x64@0.34.3'
  - '@img/sharp-linuxmusl-arm64@0.34.3'
  - '@img/sharp-linuxmusl-x64@0.34.3'
  - '@img/sharp-wasm32@0.34.3'
  - '@img/sharp-win32-arm64@0.34.3'
  - '@img/sharp-win32-ia32@0.34.3'
  - '@img/sharp-win32-x64@0.34.3'
  - '@napi-rs/wasm-runtime@0.2.12'
  - '@next/swc-darwin-x64@15.4.1'
  - '@next/swc-linux-arm64-gnu@15.4.1'
  - '@next/swc-linux-arm64-musl@15.4.1'
  - '@next/swc-linux-x64-gnu@15.4.1'
  - '@next/swc-linux-x64-musl@15.4.1'
  - '@next/swc-win32-arm64-msvc@15.4.1'
  - '@next/swc-win32-x64-msvc@15.4.1'
  - '@tailwindcss/oxide-android-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-x64@4.1.11'
  - '@tailwindcss/oxide-freebsd-x64@4.1.11'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.11'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.11'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.11'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.11'
  - '@tailwindcss/oxide-win32-x64-msvc@4.1.11'
  - '@tybys/wasm-util@0.10.0'
  - '@unrs/resolver-binding-android-arm-eabi@1.11.1'
  - '@unrs/resolver-binding-android-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-x64@1.11.1'
  - '@unrs/resolver-binding-freebsd-x64@1.11.1'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-musl@1.11.1'
  - '@unrs/resolver-binding-wasm32-wasi@1.11.1'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-x64-msvc@1.11.1'
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - lightningcss-win32-x64-msvc@1.30.1
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
