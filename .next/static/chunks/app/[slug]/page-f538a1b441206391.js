(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[182],{839:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9636,23)),Promise.resolve().then(r.bind(r,3150))},1214:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let n=r(1721),o=r(1632);function i(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},1476:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(1589);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=i(e,n)),t&&(o.current=i(t,n))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1721:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return g},PageNotFoundError:function(){return m},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return u},getLocationOrigin:function(){return s},getURL:function(){return a},isAbsoluteUrl:function(){return i},isResSent:function(){return l},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return x}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function s(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function a(){let{href:e}=window.location,t=s();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function l(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&l(r))return n;if(!n)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class g extends Error{}class m extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},1793:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(r,n(e));else t.set(r,n(o));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},3150:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var n=r(1825),o=r(5630);function i(){return(0,n.jsx)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-yellow-50 to-orange-100 p-8",children:(0,n.jsxs)("div",{className:"text-center space-y-6 max-w-md",children:[(0,n.jsx)("div",{className:"w-16 h-16 mx-auto bg-yellow-100 rounded-full flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-8 h-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:"Content Not Available"}),(0,n.jsx)("p",{className:"text-gray-600",children:"This request cannot be processed as it may contain inappropriate content or violates our content policy."})]}),(0,n.jsxs)("div",{className:"bg-white/50 rounded-lg p-4 text-left",children:[(0,n.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"Please try requests for:"}),(0,n.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,n.jsx)("li",{children:"• Business websites"}),(0,n.jsx)("li",{children:"• Portfolio pages"}),(0,n.jsx)("li",{children:"• Landing pages"}),(0,n.jsx)("li",{children:"• Educational content"}),(0,n.jsx)("li",{children:"• Creative projects"}),(0,n.jsx)("li",{children:"• Tools and utilities"})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,n.jsx)(o.$,{onClick:()=>window.location.href="/",className:"px-6",children:"Go Home"}),(0,n.jsx)(o.$,{variant:"outline",onClick:()=>window.history.back(),className:"px-6",children:"Go Back"})]}),(0,n.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,n.jsx)("p",{className:"text-sm text-gray-500 mb-3",children:"Questions about our content policy?"}),(0,n.jsxs)("a",{href:"https://twitter.com/n3sonline",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors",children:[(0,n.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})}),"Tweet @n3sonline"]})]})]}),(0,n.jsxs)("div",{className:"mt-8 text-xs text-gray-500",children:["Powered by"," ",(0,n.jsx)("a",{href:"https://dothistask.ai",className:"underline",children:"dothistask.ai"})]})]})})}},4402:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},4539:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return i},formatWithValidation:function(){return a},urlObjectKeys:function(){return s}});let n=r(1756)._(r(1793)),o=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:r}=e,i=e.protocol||"",s=e.pathname||"",a=e.hash||"",u=e.query||"",l=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?l=t+e.host:r&&(l=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(l+=":"+e.port)),u&&"object"==typeof u&&(u=String(n.urlQueryToSearchParams(u)));let c=e.search||u&&"?"+u||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||o.test(i))&&!1!==l?(l="//"+(l||""),s&&"/"!==s[0]&&(s="/"+s)):l||(l=""),a&&"#"!==a[0]&&(a="#"+a),c&&"?"!==c[0]&&(c="?"+c),""+i+l+(s=s.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+a}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return i(e)}},5630:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var n=r(1825);r(1589);var o=r(7704),i=r(812),s=r(3258),a=r(1598);let u=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:i,asChild:l=!1,...c}=e,f=l?o.DX:"button";return(0,n.jsx)(f,{"data-slot":"button",className:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}(u({variant:r,size:i,className:t})),...c})}},9636:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},useLinkStatus:function(){return v}});let n=r(1756),o=r(1825),i=n._(r(1589)),s=r(4539),a=r(2701),u=r(2840),l=r(1476),c=r(1721),f=r(5823);r(9220);let d=r(8556),p=r(1214),h=r(8220);function g(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function m(e){let t,r,n,[s,m]=(0,i.useOptimistic)(d.IDLE_LINK_STATUS),v=(0,i.useRef)(null),{href:x,as:b,children:j,prefetch:N=null,passHref:P,replace:_,shallow:w,scroll:k,onClick:E,onMouseEnter:O,onTouchStart:C,legacyBehavior:L=!1,onNavigate:T,ref:A,unstable_dynamicOnHover:S,...M}=e;t=j,L&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let U=i.default.useContext(a.AppRouterContext),R=!1!==N,I=null===N||"auto"===N?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:z,as:B}=i.default.useMemo(()=>{let e=g(x);return{href:e,as:b?g(b):e}},[x,b]);L&&(r=i.default.Children.only(t));let D=L?r&&"object"==typeof r&&r.ref:A,F=i.default.useCallback(e=>(null!==U&&(v.current=(0,d.mountLinkInstance)(e,z,U,I,R,m)),()=>{v.current&&((0,d.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,d.unmountPrefetchableInstance)(e)}),[R,z,U,I,m]),K={ref:(0,l.useMergedRef)(F,D),onClick(e){L||"function"!=typeof E||E(e),L&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),U&&(e.defaultPrevented||function(e,t,r,n,o,s,a){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),a){let e=!1;if(a({preventDefault:()=>{e=!0}}),e)return}i.default.startTransition(()=>{(0,h.dispatchNavigateAction)(r||t,o?"replace":"push",null==s||s,n.current)})}}(e,z,B,v,_,k,T))},onMouseEnter(e){L||"function"!=typeof O||O(e),L&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),U&&R&&(0,d.onNavigationIntent)(e.currentTarget,!0===S)},onTouchStart:function(e){L||"function"!=typeof C||C(e),L&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),U&&R&&(0,d.onNavigationIntent)(e.currentTarget,!0===S)}};return(0,c.isAbsoluteUrl)(B)?K.href=B:L&&!P&&("a"!==r.type||"href"in r.props)||(K.href=(0,f.addBasePath)(B)),n=L?i.default.cloneElement(r,K):(0,o.jsx)("a",{...M,...K,children:t}),(0,o.jsx)(y.Provider,{value:s,children:n})}r(4402);let y=(0,i.createContext)(d.IDLE_LINK_STATUS),v=()=>(0,i.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}},e=>{e.O(0,[744,692,276,358],()=>e(e.s=839)),_N_E=e.O()}]);