(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{5630:(e,s,t)=>{"use strict";t.d(s,{$:()=>o});var a=t(1825);t(1589);var r=t(7704),n=t(812),l=t(3258),i=t(1598);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:s,variant:t,size:n,asChild:o=!1,...x}=e,c=o?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,i.QP)((0,l.$)(s))}(d({variant:t,size:n,className:s})),...x})}},6911:(e,s,t)=>{Promise.resolve().then(t.bind(t,7762))},7762:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(1825),r=t(5630),n=t(1589),l=t(773);function i(){let[e,s]=(0,n.useState)(""),[t,i]=(0,n.useState)(!1),d=(0,l.useRouter)();return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",children:[(0,a.jsx)("header",{className:"pt-6 sm:pt-8 pb-4 text-center",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6",children:[(0,a.jsx)("h1",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4",children:"Every Website AI"}),(0,a.jsx)("p",{className:"text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-2",children:"Generate any webpage instantly with AI. Just change the URL to describe what you want."}),(0,a.jsx)("div",{className:"mt-6 p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-200 max-w-xl mx-auto",children:(0,a.jsxs)("p",{className:"text-blue-800 font-mono text-sm sm:text-base lg:text-lg break-all",children:["everywebsite.ai/",(0,a.jsx)("span",{className:"bg-blue-200 px-1 sm:px-2 py-1 rounded",children:"your-prompt-here"})]})})]})}),(0,a.jsx)("main",{className:"flex-1 flex items-center justify-center px-4 sm:px-6 py-8 sm:py-12",children:(0,a.jsxs)("div",{className:"max-w-2xl w-full space-y-6 sm:space-y-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8 border border-gray-100",children:(0,a.jsxs)("form",{onSubmit:s=>{if(s.preventDefault(),!e.trim())return;i(!0);let t=e.trim().replace(/[^a-zA-Z0-9\s\-_]/g,"").replace(/\s+/g,"-");d.push("/".concat(t))},className:"space-y-4 sm:space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"webpage-input",className:"block text-base sm:text-lg font-semibold text-gray-800 mb-3",children:"Try it now - enter your prompt:"}),(0,a.jsxs)("div",{className:"flex items-center border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent transition-all",children:[(0,a.jsx)("span",{className:"pl-3 pr-1 text-gray-500 font-mono text-sm sm:text-base whitespace-nowrap",children:"everywebsite.ai/"}),(0,a.jsx)("input",{id:"webpage-input",type:"text",value:e,onChange:e=>s(e.target.value),placeholder:"calculator",className:"flex-1 pr-4 py-3 text-sm sm:text-lg border-0 outline-none font-mono bg-transparent",disabled:t})]})]}),(0,a.jsx)(r.$,{type:"submit",className:"w-full py-3 text-base sm:text-lg font-semibold",disabled:!e.trim()||t,children:t?"Generating...":"Generate Webpage"})]})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-gray-600 mb-4 text-sm sm:text-base",children:"Try these example URLs:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 justify-center px-2",children:["portfolio-website","restaurant-menu","landing-page-for-saas","personal-blog","event-invitation","product-showcase"].map(e=>(0,a.jsxs)("button",{onClick:()=>s(e),className:"px-2 sm:px-3 py-1 text-xs sm:text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors font-mono break-all",disabled:t,children:["/",e]},e))})]})]})}),(0,a.jsx)("section",{className:"py-12 sm:py-16 bg-white/50",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-center text-gray-900 mb-8 sm:mb-12",children:"How it works"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-blue-600 font-bold text-lg",children:"1"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-2 text-base sm:text-lg",children:"Change the URL"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm sm:text-base px-2",children:"Add your prompt to the URL: everywebsite.ai/your-idea"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-blue-600 font-bold text-lg",children:"2"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-2 text-base sm:text-lg",children:"AI Generates"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm sm:text-base px-2",children:"Our AI instantly creates a complete, responsive webpage"})]}),(0,a.jsxs)("div",{className:"text-center sm:col-span-2 lg:col-span-1",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-blue-600 font-bold text-lg",children:"3"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-2 text-base sm:text-lg",children:"That's it!"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm sm:text-base px-2",children:"Your custom webpage is ready to use and share"})]})]})]})}),(0,a.jsx)("footer",{className:"py-6 sm:py-8 text-center text-gray-500 text-xs sm:text-sm px-4",children:(0,a.jsxs)("p",{className:"flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-0",children:[(0,a.jsxs)("span",{children:["Powered by"," ",(0,a.jsx)("a",{href:"https://dothistask.ai",className:"underline hover:text-gray-700",children:"dothistask.ai"})]}),(0,a.jsx)("span",{className:"hidden sm:inline",children:" • "}),(0,a.jsx)("a",{href:"https://twitter.com/n3sonline",className:"underline hover:text-gray-700",children:"@n3sonline"})]})})]})}}},e=>{e.O(0,[744,692,276,358],()=>e(e.s=6911)),_N_E=e.O()}]);