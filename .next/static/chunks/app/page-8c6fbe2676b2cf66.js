(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{388:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(1825),a=r(5630),n=r(1589),i=r(773);function o(){let[e,t]=(0,n.useState)(""),[r,o]=(0,n.useState)(!1),l=(0,i.useRouter)();return(0,s.jsxs)("div",{className:"max-w-2xl w-full space-y-6 sm:space-y-8",children:[(0,s.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8 border border-gray-100",children:(0,s.jsxs)("form",{onSubmit:t=>{if(t.preventDefault(),!e.trim())return;o(!0);let r=e.trim().replace(/[^a-zA-Z0-9\s\-_]/g,"").replace(/\s+/g,"-");l.push("/".concat(r))},className:"space-y-4 sm:space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"webpage-input",className:"block text-base sm:text-lg font-semibold text-gray-800 mb-3",children:"Try it now - enter your prompt:"}),(0,s.jsxs)("div",{className:"flex items-center border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent transition-all",children:[(0,s.jsx)("span",{className:"pl-3 pr-1 text-gray-500 font-mono text-sm sm:text-base whitespace-nowrap",children:"everywebsite.ai/"}),(0,s.jsx)("input",{id:"webpage-input",type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"calculator",className:"flex-1 pr-4 py-3 text-sm sm:text-lg border-0 outline-none font-mono bg-transparent",disabled:r})]})]}),(0,s.jsx)(a.$,{type:"submit",className:"w-full py-3 text-base sm:text-lg font-semibold",disabled:!e.trim()||r,children:r?"Generating...":"Generate Webpage"})]})}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-gray-600 mb-4 text-sm sm:text-base",children:"Try these example URLs:"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 justify-center px-2",children:["portfolio-website","restaurant-menu","landing-page-for-saas","personal-blog","event-invitation","product-showcase"].map(e=>(0,s.jsxs)("button",{onClick:()=>t(e),className:"px-2 sm:px-3 py-1 text-xs sm:text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors font-mono break-all",disabled:r,children:["/",e]},e))})]})]})}},1990:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9636,23)),Promise.resolve().then(r.bind(r,388))},5630:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(1825);r(1589);var a=r(7704),n=r(812),i=r(3258),o=r(1598);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:n,asChild:d=!1,...c}=e,u=d?a.DX:"button";return(0,s.jsx)(u,{"data-slot":"button",className:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,i.$)(t))}(l({variant:r,size:n,className:t})),...c})}}},e=>{e.O(0,[744,255,692,276,358],()=>e(e.s=1990)),_N_E=e.O()}]);