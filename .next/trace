[{"name": "generate-buildid", "duration": 106, "timestamp": 58744232811, "id": 4, "parentId": 1, "tags": {}, "startTime": 1752855862858, "traceId": "36ed095d54d93308"}, {"name": "load-custom-routes", "duration": 131, "timestamp": 58744232966, "id": 5, "parentId": 1, "tags": {}, "startTime": 1752855862858, "traceId": "36ed095d54d93308"}, {"name": "create-dist-dir", "duration": 1087, "timestamp": 58744260273, "id": 6, "parentId": 1, "tags": {}, "startTime": 1752855862885, "traceId": "36ed095d54d93308"}, {"name": "create-pages-mapping", "duration": 72, "timestamp": 58744265099, "id": 7, "parentId": 1, "tags": {}, "startTime": 1752855862890, "traceId": "36ed095d54d93308"}, {"name": "collect-app-paths", "duration": 828, "timestamp": 58744265184, "id": 8, "parentId": 1, "tags": {}, "startTime": 1752855862890, "traceId": "36ed095d54d93308"}, {"name": "create-app-mapping", "duration": 22791, "timestamp": 58744266026, "id": 9, "parentId": 1, "tags": {}, "startTime": 1752855862891, "traceId": "36ed095d54d93308"}, {"name": "public-dir-conflict-check", "duration": 313, "timestamp": 58744288984, "id": 10, "parentId": 1, "tags": {}, "startTime": 1752855862914, "traceId": "36ed095d54d93308"}, {"name": "generate-routes-manifest", "duration": 858, "timestamp": 58744289411, "id": 11, "parentId": 1, "tags": {}, "startTime": 1752855862915, "traceId": "36ed095d54d93308"}, {"name": "create-entrypoints", "duration": 6359, "timestamp": 58744623482, "id": 15, "parentId": 13, "tags": {}, "startTime": 1752855863249, "traceId": "36ed095d54d93308"}, {"name": "generate-webpack-config", "duration": 51399, "timestamp": 58744629912, "id": 16, "parentId": 14, "tags": {}, "startTime": 1752855863255, "traceId": "36ed095d54d93308"}, {"name": "next-trace-entrypoint-plugin", "duration": 1225, "timestamp": 58744723624, "id": 18, "parentId": 17, "tags": {}, "startTime": 1752855863349, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 119661, "timestamp": 58744727738, "id": 22, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1752855863353, "traceId": "36ed095d54d93308"}, {"name": "build-module-ts", "duration": 14684, "timestamp": 58744996434, "id": 27, "parentId": 17, "tags": {"name": "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/redis.ts", "layer": "rsc"}, "startTime": 1752855863622, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 308152, "timestamp": 58744727828, "id": 26, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1752855863353, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 308185, "timestamp": 58744727807, "id": 23, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1752855863353, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 308577, "timestamp": 58744727419, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fnode_modules%2F.pnpm%2Fnext%4015.4.1_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-not-found.js&appDir=%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fsrc%2Fapp&appPaths=%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fnode_modules%2F.pnpm%2Fnext%4015.4.1_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-not-found&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1752855863353, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 308183, "timestamp": 58744727816, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fsrc%2Fapp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1752855863353, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 313652, "timestamp": 58744727716, "id": 21, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fsrc%2Fapp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1752855863353, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 338310, "timestamp": 58744727822, "id": 25, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F%5Bslug%5D%2Fpage&name=app%2F%5Bslug%5D%2Fpage&pagePath=private-next-app-dir%2F%5Bslug%5D%2Fpage.tsx&appDir=%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fsrc%2Fapp&appPaths=%2F%5Bslug%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!"}, "startTime": 1752855863353, "traceId": "36ed095d54d93308"}, {"name": "make", "duration": 420563, "timestamp": 58744727291, "id": 19, "parentId": 17, "tags": {}, "startTime": 1752855863352, "traceId": "36ed095d54d93308"}, {"name": "get-entries", "duration": 203, "timestamp": 58745148428, "id": 53, "parentId": 52, "tags": {}, "startTime": 1752855863774, "traceId": "36ed095d54d93308"}, {"name": "node-file-trace-plugin", "duration": 52390, "timestamp": 58745150917, "id": 54, "parentId": 52, "tags": {"traceEntryCount": "10"}, "startTime": 1752855863776, "traceId": "36ed095d54d93308"}, {"name": "collect-traced-files", "duration": 380, "timestamp": 58745203317, "id": 55, "parentId": 52, "tags": {}, "startTime": 1752855863828, "traceId": "36ed095d54d93308"}, {"name": "finish-modules", "duration": 55366, "timestamp": 58745148334, "id": 52, "parentId": 18, "tags": {}, "startTime": 1752855863773, "traceId": "36ed095d54d93308"}, {"name": "chunk-graph", "duration": 6885, "timestamp": 58745227339, "id": 57, "parentId": 56, "tags": {}, "startTime": 1752855863852, "traceId": "36ed095d54d93308"}, {"name": "optimize-modules", "duration": 10, "timestamp": 58745234283, "id": 59, "parentId": 56, "tags": {}, "startTime": 1752855863859, "traceId": "36ed095d54d93308"}, {"name": "optimize-chunks", "duration": 7709, "timestamp": 58745234329, "id": 60, "parentId": 56, "tags": {}, "startTime": 1752855863859, "traceId": "36ed095d54d93308"}, {"name": "optimize-tree", "duration": 64, "timestamp": 58745242094, "id": 61, "parentId": 56, "tags": {}, "startTime": 1752855863867, "traceId": "36ed095d54d93308"}, {"name": "optimize-chunk-modules", "duration": 9338, "timestamp": 58745242206, "id": 62, "parentId": 56, "tags": {}, "startTime": 1752855863867, "traceId": "36ed095d54d93308"}, {"name": "optimize", "duration": 17331, "timestamp": 58745234260, "id": 58, "parentId": 56, "tags": {}, "startTime": 1752855863859, "traceId": "36ed095d54d93308"}, {"name": "module-hash", "duration": 9111, "timestamp": 58745261765, "id": 63, "parentId": 56, "tags": {}, "startTime": 1752855863887, "traceId": "36ed095d54d93308"}, {"name": "code-generation", "duration": 125813, "timestamp": 58745270914, "id": 64, "parentId": 56, "tags": {}, "startTime": 1752855863896, "traceId": "36ed095d54d93308"}, {"name": "hash", "duration": 3653, "timestamp": 58745399340, "id": 65, "parentId": 56, "tags": {}, "startTime": 1752855864024, "traceId": "36ed095d54d93308"}, {"name": "code-generation-jobs", "duration": 167, "timestamp": 58745402991, "id": 66, "parentId": 56, "tags": {}, "startTime": 1752855864028, "traceId": "36ed095d54d93308"}, {"name": "module-assets", "duration": 246, "timestamp": 58745403139, "id": 67, "parentId": 56, "tags": {}, "startTime": 1752855864028, "traceId": "36ed095d54d93308"}, {"name": "create-chunk-assets", "duration": 2114, "timestamp": 58745403389, "id": 68, "parentId": 56, "tags": {}, "startTime": 1752855864029, "traceId": "36ed095d54d93308"}, {"name": "minify-js", "duration": 10963, "timestamp": 58745411559, "id": 70, "parentId": 69, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1752855864037, "traceId": "36ed095d54d93308"}, {"name": "minify-js", "duration": 10901, "timestamp": 58745411629, "id": 71, "parentId": 69, "tags": {"name": "../app/favicon.ico/route.js", "cache": "HIT"}, "startTime": 1752855864037, "traceId": "36ed095d54d93308"}, {"name": "minify-js", "duration": 10895, "timestamp": 58745411636, "id": 72, "parentId": 69, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1752855864037, "traceId": "36ed095d54d93308"}, {"name": "minify-js", "duration": 10892, "timestamp": 58745411640, "id": 73, "parentId": 69, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1752855864037, "traceId": "36ed095d54d93308"}, {"name": "minify-js", "duration": 10889, "timestamp": 58745411643, "id": 74, "parentId": 69, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1752855864037, "traceId": "36ed095d54d93308"}, {"name": "minify-js", "duration": 144, "timestamp": 58745422389, "id": 76, "parentId": 69, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1752855864048, "traceId": "36ed095d54d93308"}, {"name": "minify-js", "duration": 122, "timestamp": 58745422412, "id": 77, "parentId": 69, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1752855864048, "traceId": "36ed095d54d93308"}, {"name": "minify-js", "duration": 117, "timestamp": 58745422417, "id": 78, "parentId": 69, "tags": {"name": "505.js", "cache": "HIT"}, "startTime": 1752855864048, "traceId": "36ed095d54d93308"}, {"name": "minify-js", "duration": 114, "timestamp": 58745422420, "id": 79, "parentId": 69, "tags": {"name": "926.js", "cache": "HIT"}, "startTime": 1752855864048, "traceId": "36ed095d54d93308"}, {"name": "minify-js", "duration": 23, "timestamp": 58745422512, "id": 80, "parentId": 69, "tags": {"name": "232.js", "cache": "HIT"}, "startTime": 1752855864048, "traceId": "36ed095d54d93308"}, {"name": "minify-js", "duration": 17, "timestamp": 58745422518, "id": 81, "parentId": 69, "tags": {"name": "236.js", "cache": "HIT"}, "startTime": 1752855864048, "traceId": "36ed095d54d93308"}, {"name": "minify-js", "duration": 103429, "timestamp": 58745411646, "id": 75, "parentId": 69, "tags": {"name": "../app/[slug]/page.js", "cache": "MISS"}, "startTime": 1752855864037, "traceId": "36ed095d54d93308"}, {"name": "minify-webpack-plugin-optimize", "duration": 107865, "timestamp": 58745407225, "id": 69, "parentId": 17, "tags": {"compilationName": "server", "mangle": "[object Object]"}, "startTime": 1752855864032, "traceId": "36ed095d54d93308"}, {"name": "css-minimizer-plugin", "duration": 114, "timestamp": 58745515159, "id": 82, "parentId": 17, "tags": {}, "startTime": 1752855864140, "traceId": "36ed095d54d93308"}, {"name": "create-trace-assets", "duration": 735, "timestamp": 58745515381, "id": 83, "parentId": 18, "tags": {}, "startTime": 1752855864141, "traceId": "36ed095d54d93308"}, {"name": "seal", "duration": 303663, "timestamp": 58745216741, "id": 56, "parentId": 17, "tags": {}, "startTime": 1752855863842, "traceId": "36ed095d54d93308"}, {"name": "webpack-compilation", "duration": 801909, "timestamp": 58744722619, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1752855863348, "traceId": "36ed095d54d93308"}, {"name": "emit", "duration": 3409, "timestamp": 58745524708, "id": 84, "parentId": 14, "tags": {}, "startTime": 1752855864150, "traceId": "36ed095d54d93308"}, {"name": "webpack-close", "duration": 75262, "timestamp": 58745528757, "id": 85, "parentId": 14, "tags": {"name": "server"}, "startTime": 1752855864154, "traceId": "36ed095d54d93308"}, {"name": "webpack-generate-error-stats", "duration": 1304, "timestamp": 58745604059, "id": 86, "parentId": 85, "tags": {}, "startTime": 1752855864229, "traceId": "36ed095d54d93308"}, {"name": "run-webpack-compiler", "duration": 982071, "timestamp": 58744623480, "id": 14, "parentId": 13, "tags": {}, "startTime": 1752855863249, "traceId": "36ed095d54d93308"}, {"name": "format-webpack-messages", "duration": 38, "timestamp": 58745605556, "id": 87, "parentId": 13, "tags": {}, "startTime": 1752855864231, "traceId": "36ed095d54d93308"}, {"name": "worker-main-server", "duration": 982355, "timestamp": 58744623308, "id": 13, "parentId": 1, "tags": {}, "startTime": 1752855863248, "traceId": "36ed095d54d93308"}, {"name": "create-entrypoints", "duration": 6722, "timestamp": 58745959216, "id": 90, "parentId": 88, "tags": {}, "startTime": 1752855864584, "traceId": "36ed095d54d93308"}, {"name": "generate-webpack-config", "duration": 54741, "timestamp": 58745966013, "id": 91, "parentId": 89, "tags": {}, "startTime": 1752855864591, "traceId": "36ed095d54d93308"}, {"name": "make", "duration": 485, "timestamp": 58746069259, "id": 93, "parentId": 92, "tags": {}, "startTime": 1752855864694, "traceId": "36ed095d54d93308"}, {"name": "chunk-graph", "duration": 399, "timestamp": 58746071300, "id": 95, "parentId": 94, "tags": {}, "startTime": 1752855864696, "traceId": "36ed095d54d93308"}, {"name": "optimize-modules", "duration": 14, "timestamp": 58746071753, "id": 97, "parentId": 94, "tags": {}, "startTime": 1752855864697, "traceId": "36ed095d54d93308"}, {"name": "optimize-chunks", "duration": 456, "timestamp": 58746071810, "id": 98, "parentId": 94, "tags": {}, "startTime": 1752855864697, "traceId": "36ed095d54d93308"}, {"name": "optimize-tree", "duration": 58, "timestamp": 58746072296, "id": 99, "parentId": 94, "tags": {}, "startTime": 1752855864697, "traceId": "36ed095d54d93308"}, {"name": "optimize-chunk-modules", "duration": 269, "timestamp": 58746072445, "id": 100, "parentId": 94, "tags": {}, "startTime": 1752855864698, "traceId": "36ed095d54d93308"}, {"name": "optimize", "duration": 1117, "timestamp": 58746071731, "id": 96, "parentId": 94, "tags": {}, "startTime": 1752855864697, "traceId": "36ed095d54d93308"}, {"name": "module-hash", "duration": 32, "timestamp": 58746073283, "id": 101, "parentId": 94, "tags": {}, "startTime": 1752855864698, "traceId": "36ed095d54d93308"}, {"name": "code-generation", "duration": 79, "timestamp": 58746073328, "id": 102, "parentId": 94, "tags": {}, "startTime": 1752855864698, "traceId": "36ed095d54d93308"}, {"name": "hash", "duration": 158, "timestamp": 58746073584, "id": 103, "parentId": 94, "tags": {}, "startTime": 1752855864699, "traceId": "36ed095d54d93308"}, {"name": "code-generation-jobs", "duration": 49, "timestamp": 58746073741, "id": 104, "parentId": 94, "tags": {}, "startTime": 1752855864699, "traceId": "36ed095d54d93308"}, {"name": "module-assets", "duration": 25, "timestamp": 58746073782, "id": 105, "parentId": 94, "tags": {}, "startTime": 1752855864699, "traceId": "36ed095d54d93308"}, {"name": "create-chunk-assets", "duration": 81, "timestamp": 58746073810, "id": 106, "parentId": 94, "tags": {}, "startTime": 1752855864699, "traceId": "36ed095d54d93308"}, {"name": "minify-js", "duration": 278, "timestamp": 58746080733, "id": 108, "parentId": 107, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1752855864706, "traceId": "36ed095d54d93308"}, {"name": "minify-webpack-plugin-optimize", "duration": 3693, "timestamp": 58746077333, "id": 107, "parentId": 92, "tags": {"compilationName": "edge-server", "mangle": "[object Object]"}, "startTime": 1752855864702, "traceId": "36ed095d54d93308"}, {"name": "css-minimizer-plugin", "duration": 83, "timestamp": 58746081080, "id": 109, "parentId": 92, "tags": {}, "startTime": 1752855864706, "traceId": "36ed095d54d93308"}, {"name": "seal", "duration": 12025, "timestamp": 58746070998, "id": 94, "parentId": 92, "tags": {}, "startTime": 1752855864696, "traceId": "36ed095d54d93308"}, {"name": "webpack-compilation", "duration": 17742, "timestamp": 58746065439, "id": 92, "parentId": 89, "tags": {"name": "edge-server"}, "startTime": 1752855864691, "traceId": "36ed095d54d93308"}, {"name": "emit", "duration": 1308, "timestamp": 58746083351, "id": 110, "parentId": 89, "tags": {}, "startTime": 1752855864708, "traceId": "36ed095d54d93308"}, {"name": "webpack-close", "duration": 302, "timestamp": 58746085043, "id": 111, "parentId": 89, "tags": {"name": "edge-server"}, "startTime": 1752855864710, "traceId": "36ed095d54d93308"}, {"name": "webpack-generate-error-stats", "duration": 1098, "timestamp": 58746085364, "id": 112, "parentId": 111, "tags": {}, "startTime": 1752855864711, "traceId": "36ed095d54d93308"}, {"name": "run-webpack-compiler", "duration": 127289, "timestamp": 58745959214, "id": 89, "parentId": 88, "tags": {}, "startTime": 1752855864584, "traceId": "36ed095d54d93308"}, {"name": "format-webpack-messages", "duration": 33, "timestamp": 58746086506, "id": 113, "parentId": 88, "tags": {}, "startTime": 1752855864712, "traceId": "36ed095d54d93308"}, {"name": "worker-main-edge-server", "duration": 127549, "timestamp": 58745959047, "id": 88, "parentId": 1, "tags": {}, "startTime": 1752855864584, "traceId": "36ed095d54d93308"}, {"name": "create-entrypoints", "duration": 7062, "timestamp": 58746433968, "id": 116, "parentId": 114, "tags": {}, "startTime": 1752855865059, "traceId": "36ed095d54d93308"}, {"name": "generate-webpack-config", "duration": 53743, "timestamp": 58746441104, "id": 117, "parentId": 115, "tags": {}, "startTime": 1752855865066, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 61278, "timestamp": 58746540777, "id": 129, "parentId": 119, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1752855865166, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 61217, "timestamp": 58746540854, "id": 131, "parentId": 119, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1752855865166, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 72978, "timestamp": 58746540789, "id": 130, "parentId": 119, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fsrc%2Fapp%2F%5Bslug%5D%2Ferror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752855865166, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 139701, "timestamp": 58746540744, "id": 124, "parentId": 119, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1752855865166, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 140453, "timestamp": 58746540673, "id": 123, "parentId": 119, "tags": {"request": "next-client-pages-loader?absolutePagePath=%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fnode_modules%2F.pnpm%2Fnext%4015.4.1_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-not-found.js&page=%2F_not-found%2Fpage!"}, "startTime": 1752855865166, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 140379, "timestamp": 58746540758, "id": 126, "parentId": 119, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1752855865166, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 153558, "timestamp": 58746540865, "id": 132, "parentId": 119, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fsrc%2Fapp%2F%5Bslug%5D%2Fnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752855865166, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 155906, "timestamp": 58746540752, "id": 125, "parentId": 119, "tags": {"request": "/Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/router.js"}, "startTime": 1752855865166, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 269558, "timestamp": 58746540424, "id": 120, "parentId": 119, "tags": {"request": "./node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/next.js"}, "startTime": 1752855865166, "traceId": "36ed095d54d93308"}, {"name": "postcss-process", "duration": 50978, "timestamp": 58746799005, "id": 137, "parentId": 136, "tags": {}, "startTime": 1752855865424, "traceId": "36ed095d54d93308"}, {"name": "postcss-loader", "duration": 174171, "timestamp": 58746675854, "id": 136, "parentId": 135, "tags": {}, "startTime": 1752855865301, "traceId": "36ed095d54d93308"}, {"name": "css-loader", "duration": 14760, "timestamp": 58746850133, "id": 138, "parentId": 135, "tags": {"astUsed": "true"}, "startTime": 1752855865475, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 332378, "timestamp": 58746540876, "id": 133, "parentId": 119, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fnode_modules%2F.pnpm%2Fnext%4015.4.1_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fsrc%2Fcomponents%2Finappropriate-content.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"}, "startTime": 1752855865166, "traceId": "36ed095d54d93308"}, {"name": "add-entry", "duration": 332655, "timestamp": 58746540662, "id": 122, "parentId": 119, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fnode_modules%2F.pnpm%2Fnext%4015.4.1_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fnode_modules%2F.pnpm%2Fnext%4015.4.1_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fnode_modules%2F.pnpm%2Fnext%4015.4.1_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fnode_modules%2F.pnpm%2Fnext%4015.4.1_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fnode_modules%2F.pnpm%2Fnext%4015.4.1_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fnode_modules%2F.pnpm%2Fnext%4015.4.1_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fnode_modules%2F.pnpm%2Fnext%4015.4.1_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fnode_modules%2F.pnpm%2Fnext%4015.4.1_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwillness%2FDocuments%2FProjects%2FN3S%2Fevery-website-ai%2Fnode_modules%2F.pnpm%2Fnext%4015.4.1_%40opentelemetry%2Bapi%401.9.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1752855865166, "traceId": "36ed095d54d93308"}]