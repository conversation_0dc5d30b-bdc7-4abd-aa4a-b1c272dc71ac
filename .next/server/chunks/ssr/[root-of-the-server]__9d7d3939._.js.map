{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/env.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nconst envSchema = z.object({\n  // AWS S3 Configuration\n  AWS_ACCESS_KEY_ID: z.string().min(1, \"AWS_ACCESS_KEY_ID is required\"),\n  AWS_SECRET_ACCESS_KEY: z.string().min(1, \"AWS_SECRET_ACCESS_KEY is required\"),\n  AWS_REGION: z.string().min(1, \"AWS_REGION is required\"),\n  AWS_S3_BUCKET_NAME: z.string().min(1, \"AWS_S3_BUCKET_NAME is required\"),\n\n  // Anthropic Claude Configuration\n  ANTHROPIC_API_KEY: z.string().min(1, \"ANTHROPIC_API_KEY is required\"),\n\n  // Next.js Environment\n  NODE_ENV: z\n    .enum([\"development\", \"production\", \"test\"])\n    .default(\"development\"),\n});\n\n// Validate environment variables\nconst parseEnv = () => {\n  // Skip validation during build time if we're not in a runtime environment\n  if (typeof window === \"undefined\" && !process.env.ANTHROPIC_API_KEY) {\n    console.warn(\"Environment variables not available during build time\");\n    return {} as z.infer<typeof envSchema>;\n  }\n\n  try {\n    return envSchema.parse(process.env);\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      const missingVars = error.errors\n        .map((err) => err.path.join(\".\"))\n        .join(\", \");\n      throw new Error(\n        `Missing or invalid environment variables: ${missingVars}`\n      );\n    }\n    throw error;\n  }\n};\n\nexport const env = parseEnv();\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY,oNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzB,uBAAuB;IACvB,mBAAmB,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACrC,uBAAuB,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzC,YAAY,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC9B,oBAAoB,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAEtC,iCAAiC;IACjC,mBAAmB,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAErC,sBAAsB;IACtB,UAAU,oNAAA,CAAA,IAAC,CACR,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAC1C,OAAO,CAAC;AACb;AAEA,iCAAiC;AACjC,MAAM,WAAW;IACf,0EAA0E;IAC1E,IAAI,gBAAkB,eAAe,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;QACnE,QAAQ,IAAI,CAAC;QACb,OAAO,CAAC;IACV;IAEA,IAAI;QACF,OAAO,UAAU,KAAK,CAAC,QAAQ,GAAG;IACpC,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,oNAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,MAAM,cAAc,MAAM,MAAM,CAC7B,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,MAC3B,IAAI,CAAC;YACR,MAAM,IAAI,MACR,CAAC,0CAA0C,EAAE,aAAa;QAE9D;QACA,MAAM;IACR;AACF;AAEO,MAAM,MAAM", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/ai.ts"], "sourcesContent": ["import { createAnthropic } from \"@ai-sdk/anthropic\";\nimport { generateText } from \"ai\";\nimport { env } from \"./env\";\n\n// Initialize Anthropic with API key\nconst anthropic = createAnthropic({\n  apiKey: env.ANTHROPIC_API_KEY,\n});\n\nconst MAX_TOKENS = 4000;\n\n/**\n * Enhanced system prompt for generating complete webpages\n */\nconst SYSTEM_PROMPT = `You are an expert web developer and designer. Your task is to generate a complete, modern, and visually appealing HTML webpage based on the given purpose/description.\n\nIMPORTANT SAFETY REQUIREMENT:\n- If the user request is for NSFW content, illegal activities, harmful content, or triggers your safety system in any way, respond with ONLY the text \"INAPPROPRIATE_PROMPT_DETECTED\" and nothing else.\n- Do not generate any HTML, explanations, or other content for inappropriate requests.\n\nREQUIREMENTS:\n1. Generate a complete HTML5 document with proper DOCTYPE, head, and body sections\n2. Include responsive design using modern CSS (flexbox/grid)\n3. Use semantic HTML elements (header, main, section, article, footer, etc.)\n4. Include proper meta tags for SEO and viewport\n5. Add attractive styling with CSS (embedded in <style> tags in the head)\n6. Use modern color schemes and typography\n7. Make it desktop and mobile-responsive\n8. Include interactive elements where appropriate (buttons, forms, etc.)\n9. Add proper accessibility attributes (alt text, ARIA labels, etc.)\n10. Use modern CSS features like CSS Grid, Flexbox, custom properties (CSS variables)\n11. You must include two following adverts:\n  - Advert to https://www.dothistask.ai (logo is https://www.dothistask.ai/icon.png)\n    - The copy for this advert must be specific to the use-case for this page + the purpose of dothistask.ai (help knowledge workers find AI solutions for their jobs)\n    - The advert (logo + text) must link to https://dothistask.ai\n  - Advert to https://everywebsite.ai (no logo)\n    - The copy for this advert should indiciate that this page was generated by https://everywebsite.ai and they can generate new pages by just changing the url\n    - The advert (text) must link to https://everywebsite.ai\n11. You must include a subtle advert to https://dothistask.ai (logo is https://www.dothistask.ai/icon.png)\n  - The copy for this advert must be specific to the use-case for this page + the purpose of dothistask.ai (help knowledge workers find AI solutions for their jobs)\n  - The advert (logo + text) must link to https://dothistask.ai\n12. You have no backend and cannot implement any features that require a backend (database, APIs, AI, etc.) You need to generate the webpage with this in mind\n13. Do not include a footer, copyright, etc.. Just a functional webpage\n14. You don't have access to images besides the Dothistask logo, you should rely on emojis or svgs that you can define. \n\nSTYLE GUIDELINES:\n- Use a modern, clean design aesthetic\n- Implement a cohesive color palette\n- Use proper typography hierarchy\n- Add subtle animations/transitions for better UX\n- Ensure good contrast ratios for accessibility\n- Use modern CSS techniques (no inline styles except for the main <style> tag)\n\nOUTPUT FORMAT:\n- Return ONLY the complete HTML document\n- No markdown code blocks or explanations\n- Start with <!DOCTYPE html> and end with </html>\n- Ensure the HTML is valid and well-formatted\n- Limit the output to ${MAX_TOKENS} tokens\n\nThe webpage should be production-ready and look professional.`;\n\n/**\n * Generate HTML content using Claude Sonnet 4\n */\nexport async function generateWebpageHtml(slug: string): Promise<string> {\n  try {\n    const { text } = await generateText({\n      model: anthropic(\"claude-3-5-sonnet-20241022\"),\n      system: SYSTEM_PROMPT,\n      prompt: `Generate a complete HTML webpage that solves this purpose: \"${slug}\"\n      \n      Make sure the webpage is:\n      - Fully functional and complete\n      - Visually appealing and modern\n      - Responsive across all devices\n      - Accessible and SEO-friendly\n      - Professional and production-ready\n      \n      The purpose \"${slug}\" should guide the content, design, and functionality of the webpage.`,\n      maxTokens: MAX_TOKENS,\n      temperature: 0.7,\n    });\n\n    return text;\n  } catch (error) {\n    console.error(\"Error generating webpage with Claude:\", error);\n    throw new Error(\"Failed to generate webpage content\");\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,oCAAoC;AACpC,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,QAAQ,iHAAA,CAAA,MAAG,CAAC,iBAAiB;AAC/B;AAEA,MAAM,aAAa;AAEnB;;CAEC,GACD,MAAM,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBA4CD,EAAE,WAAW;;6DAE0B,CAAC;AAKvD,eAAe,oBAAoB,IAAY;IACpD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,+OAAA,CAAA,eAAY,AAAD,EAAE;YAClC,OAAO,UAAU;YACjB,QAAQ;YACR,QAAQ,CAAC,4DAA4D,EAAE,KAAK;;;;;;;;;mBAS/D,EAAE,KAAK,qEAAqE,CAAC;YAC1F,WAAW;YACX,aAAa;QACf;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/s3.ts"], "sourcesContent": ["import {\n  S3Client,\n  PutObjectCommand,\n  GetObjectCommand,\n} from \"@aws-sdk/client-s3\";\nimport { env } from \"./env\";\n\n// Initialize S3 client\nconst s3Client = new S3Client({\n  region: env.AWS_REGION,\n  credentials: {\n    accessKeyId: env.AWS_ACCESS_KEY_ID,\n    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,\n  },\n});\n\n/**\n * Upload HTML content to S3\n */\nexport async function uploadHtmlToS3(\n  slug: string,\n  htmlContent: string\n): Promise<string> {\n  const key = `websites/${slug}.html`;\n\n  // Determine content type based on content\n  const isInappropriate =\n    htmlContent.trim() === \"INAPPROPRIATE_PROMPT_DETECTED\";\n  const contentType = isInappropriate ? \"text/plain\" : \"text/html\";\n\n  const command = new PutObjectCommand({\n    Bucket: env.AWS_S3_BUCKET_NAME,\n    Key: key,\n    Body: htmlContent,\n    ContentType: contentType,\n    CacheControl: \"max-age=3600\", // Cache for 1 hour\n    Metadata: {\n      inappropriate: isInappropriate ? \"true\" : \"false\",\n    },\n  });\n\n  try {\n    await s3Client.send(command);\n    return key;\n  } catch (error) {\n    console.error(\"Error uploading to S3:\", error);\n    throw new Error(\"Failed to upload HTML to S3\");\n  }\n}\n\n/**\n * Fetch HTML content from S3\n */\nexport async function fetchHtmlFromS3(slug: string): Promise<string | null> {\n  const key = `websites/${slug}.html`;\n\n  const command = new GetObjectCommand({\n    Bucket: env.AWS_S3_BUCKET_NAME,\n    Key: key,\n  });\n\n  try {\n    const response = await s3Client.send(command);\n    if (response.Body) {\n      return await response.Body.transformToString();\n    }\n    return null;\n  } catch (error) {\n    // If file doesn't exist, return null instead of throwing\n    if ((error as { name?: string })?.name === \"NoSuchKey\") {\n      return null;\n    }\n    console.error(\"Error fetching from S3:\", error);\n    throw new Error(\"Failed to fetch HTML from S3\");\n  }\n}\n\n/**\n * Check if HTML exists in S3\n */\nexport async function htmlExistsInS3(slug: string): Promise<boolean> {\n  try {\n    const html = await fetchHtmlFromS3(slug);\n    return html !== null;\n  } catch {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAKA;;;AAEA,uBAAuB;AACvB,MAAM,WAAW,IAAI,iJAAA,CAAA,WAAQ,CAAC;IAC5B,QAAQ,iHAAA,CAAA,MAAG,CAAC,UAAU;IACtB,aAAa;QACX,aAAa,iHAAA,CAAA,MAAG,CAAC,iBAAiB;QAClC,iBAAiB,iHAAA,CAAA,MAAG,CAAC,qBAAqB;IAC5C;AACF;AAKO,eAAe,eACpB,IAAY,EACZ,WAAmB;IAEnB,MAAM,MAAM,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC;IAEnC,0CAA0C;IAC1C,MAAM,kBACJ,YAAY,IAAI,OAAO;IACzB,MAAM,cAAc,kBAAkB,eAAe;IAErD,MAAM,UAAU,IAAI,iJAAA,CAAA,mBAAgB,CAAC;QACnC,QAAQ,iHAAA,CAAA,MAAG,CAAC,kBAAkB;QAC9B,KAAK;QACL,MAAM;QACN,aAAa;QACb,cAAc;QACd,UAAU;YACR,eAAe,kBAAkB,SAAS;QAC5C;IACF;IAEA,IAAI;QACF,MAAM,SAAS,IAAI,CAAC;QACpB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,gBAAgB,IAAY;IAChD,MAAM,MAAM,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC;IAEnC,MAAM,UAAU,IAAI,iJAAA,CAAA,mBAAgB,CAAC;QACnC,QAAQ,iHAAA,CAAA,MAAG,CAAC,kBAAkB;QAC9B,KAAK;IACP;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,SAAS,IAAI,CAAC;QACrC,IAAI,SAAS,IAAI,EAAE;YACjB,OAAO,MAAM,SAAS,IAAI,CAAC,iBAAiB;QAC9C;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,yDAAyD;QACzD,IAAI,AAAC,OAA6B,SAAS,aAAa;YACtD,OAAO;QACT;QACA,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,eAAe,IAAY;IAC/C,IAAI;QACF,MAAM,OAAO,MAAM,gBAAgB;QACnC,OAAO,SAAS;IAClB,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/inappropriate-content.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/inappropriate-content.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/inappropriate-content.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oZAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/inappropriate-content.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/inappropriate-content.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/inappropriate-content.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oZAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/%5Bslug%5D/page.tsx"], "sourcesContent": ["import { generateWebpageHtml } from \"@/lib/ai\";\nimport { fetchHtmlFromS3, uploadHtmlToS3 } from \"@/lib/s3\";\nimport { notFound } from \"next/navigation\";\nimport InappropriateContent from \"@/components/inappropriate-content\";\n\ninterface PageProps {\n  params: Promise<{ slug: string }>;\n}\n\nexport default async function SlugPage({ params }: PageProps) {\n  const { slug } = await params;\n\n  // Validate slug (basic sanitization)\n  if (!slug || slug.length > 100 || !/^[a-zA-Z0-9\\-_\\s]+$/.test(slug)) {\n    notFound();\n  }\n\n  let htmlContent: string;\n\n  try {\n    // First, try to fetch existing HTML from S3\n    const existingHtml = await fetchHtmlFromS3(slug);\n\n    if (existingHtml) {\n      htmlContent = existingHtml;\n    } else {\n      // Generate new HTML using AI\n      console.log(`Generating new webpage for slug: ${slug}`);\n      htmlContent = await generateWebpageHtml(slug);\n\n      // Upload to S3 for caching (including inappropriate content responses)\n      try {\n        await uploadHtmlToS3(slug, htmlContent);\n        console.log(`Successfully cached webpage for slug: ${slug}`);\n      } catch (uploadError) {\n        console.error(\"Failed to cache webpage to S3:\", uploadError);\n        // Continue anyway, we have the HTML content\n      }\n    }\n  } catch (error) {\n    console.error(\"Error processing webpage:\", error);\n    throw new Error(\"Failed to generate webpage. Please try again later.\");\n  }\n\n  // Check if the content is inappropriate\n  if (htmlContent.trim() === \"INAPPROPRIATE_PROMPT_DETECTED\") {\n    return <InappropriateContent />;\n  }\n\n  // Return the HTML content directly\n  return (\n    <div\n      dangerouslySetInnerHTML={{ __html: htmlContent }}\n      style={{ width: \"100%\", height: \"100vh\" }}\n    />\n  );\n}\n\n// Generate metadata for the page\nexport async function generateMetadata({ params }: PageProps) {\n  const { slug } = await params;\n\n  return {\n    title: `${slug\n      .replace(/[-_]/g, \" \")\n      .replace(/\\b\\w/g, (l) => l.toUpperCase())} | Every Website AI`,\n    description: `AI-generated webpage for: ${slug}`,\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AACA;;;;;;AAMe,eAAe,SAAS,EAAE,MAAM,EAAa;IAC1D,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAEvB,qCAAqC;IACrC,IAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,OAAO,CAAC,sBAAsB,IAAI,CAAC,OAAO;QACnE,CAAA,GAAA,4VAAA,CAAA,WAAQ,AAAD;IACT;IAEA,IAAI;IAEJ,IAAI;QACF,4CAA4C;QAC5C,MAAM,eAAe,MAAM,CAAA,GAAA,gHAAA,CAAA,kBAAe,AAAD,EAAE;QAE3C,IAAI,cAAc;YAChB,cAAc;QAChB,OAAO;YACL,6BAA6B;YAC7B,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,MAAM;YACtD,cAAc,MAAM,CAAA,GAAA,gHAAA,CAAA,sBAAmB,AAAD,EAAE;YAExC,uEAAuE;YACvE,IAAI;gBACF,MAAM,CAAA,GAAA,gHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM;gBAC3B,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,MAAM;YAC7D,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,4CAA4C;YAC9C;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM,IAAI,MAAM;IAClB;IAEA,wCAAwC;IACxC,IAAI,YAAY,IAAI,OAAO,iCAAiC;QAC1D,qBAAO,qZAAC,8IAAA,CAAA,UAAoB;;;;;IAC9B;IAEA,mCAAmC;IACnC,qBACE,qZAAC;QACC,yBAAyB;YAAE,QAAQ;QAAY;QAC/C,OAAO;YAAE,OAAO;YAAQ,QAAQ;QAAQ;;;;;;AAG9C;AAGO,eAAe,iBAAiB,EAAE,MAAM,EAAa;IAC1D,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAEvB,OAAO;QACL,OAAO,GAAG,KACP,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,SAAS,CAAC,IAAM,EAAE,WAAW,IAAI,mBAAmB,CAAC;QAChE,aAAa,CAAC,0BAA0B,EAAE,MAAM;IAClD;AACF", "debugId": null}}]}