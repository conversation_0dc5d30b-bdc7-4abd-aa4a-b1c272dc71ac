{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/%5Bslug%5D/loading.tsx"], "sourcesContent": ["export default function Loading() {\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"text-center space-y-6 p-8\">\n        {/* Animated spinner */}\n        <div className=\"relative\">\n          <div className=\"w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto\"></div>\n          <div\n            className=\"absolute inset-0 w-16 h-16 border-4 border-transparent border-r-indigo-400 rounded-full animate-spin mx-auto\"\n            style={{ animationDirection: \"reverse\", animationDuration: \"1.5s\" }}\n          ></div>\n        </div>\n\n        {/* Loading text */}\n        <div className=\"space-y-2\">\n          <h1 className=\"text-2xl font-bold text-gray-800\">\n            Generating your page now...\n          </h1>\n          <p className=\"text-gray-600 max-w-md mx-auto\">\n            Our AI is crafting a custom webpage just for you. This usually takes\n            a few seconds.\n          </p>\n        </div>\n\n        {/* Progress dots */}\n        <div className=\"flex space-x-2 justify-center\">\n          <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-bounce\"></div>\n          <div\n            className=\"w-2 h-2 bg-blue-400 rounded-full animate-bounce\"\n            style={{ animationDelay: \"0.1s\" }}\n          ></div>\n          <div\n            className=\"w-2 h-2 bg-blue-400 rounded-full animate-bounce\"\n            style={{ animationDelay: \"0.2s\" }}\n          ></div>\n        </div>\n\n        {/* Powered by notice */}\n        <div className=\"mt-8 text-xs text-gray-500\">\n          Powered by{\" \"}\n          <a href=\"https://dothistask.ai\" className=\"underline\">\n            dothistask.ai\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,qZAAC;QAAI,WAAU;kBACb,cAAA,qZAAC;YAAI,WAAU;;8BAEb,qZAAC;oBAAI,WAAU;;sCACb,qZAAC;4BAAI,WAAU;;;;;;sCACf,qZAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,oBAAoB;gCAAW,mBAAmB;4BAAO;;;;;;;;;;;;8BAKtE,qZAAC;oBAAI,WAAU;;sCACb,qZAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAGjD,qZAAC;4BAAE,WAAU;sCAAiC;;;;;;;;;;;;8BAOhD,qZAAC;oBAAI,WAAU;;sCACb,qZAAC;4BAAI,WAAU;;;;;;sCACf,qZAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;sCAElC,qZAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;;;;;;;8BAKpC,qZAAC;oBAAI,WAAU;;wBAA6B;wBAC/B;sCACX,qZAAC;4BAAE,MAAK;4BAAwB,WAAU;sCAAY;;;;;;;;;;;;;;;;;;;;;;;AAOhE", "debugId": null}}]}