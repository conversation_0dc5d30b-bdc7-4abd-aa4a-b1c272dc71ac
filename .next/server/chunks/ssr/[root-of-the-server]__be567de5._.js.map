{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,qZAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/homepage-form.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\n\nexport default function HomepageForm() {\n  const [input, setInput] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const router = useRouter();\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim()) return;\n\n    setIsLoading(true);\n    // Clean the input for URL usage\n    const cleanInput = input\n      .trim()\n      .replace(/[^a-zA-Z0-9\\s\\-_]/g, \"\")\n      .replace(/\\s+/g, \"-\");\n    router.push(`/${cleanInput}`);\n  };\n\n  const examples = [\n    \"portfolio-website\",\n    \"restaurant-menu\",\n    \"landing-page-for-saas\",\n    \"personal-blog\",\n    \"event-invitation\",\n    \"product-showcase\",\n  ];\n\n  return (\n    <div className=\"max-w-2xl w-full space-y-6 sm:space-y-8\">\n      {/* Input Section */}\n      <div className=\"bg-white rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8 border border-gray-100\">\n        <form onSubmit={handleSubmit} className=\"space-y-4 sm:space-y-6\">\n          <div>\n            <label\n              htmlFor=\"webpage-input\"\n              className=\"block text-base sm:text-lg font-semibold text-gray-800 mb-3\"\n            >\n              Try it now - enter your prompt:\n            </label>\n            <div className=\"flex items-center border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent transition-all\">\n              <span className=\"pl-3 pr-1 text-gray-500 font-mono text-sm sm:text-base whitespace-nowrap\">\n                everywebsite.ai/\n              </span>\n              <input\n                id=\"webpage-input\"\n                type=\"text\"\n                value={input}\n                onChange={(e) => setInput(e.target.value)}\n                placeholder=\"calculator\"\n                className=\"flex-1 pr-4 py-3 text-sm sm:text-lg border-0 outline-none font-mono bg-transparent\"\n                disabled={isLoading}\n              />\n            </div>\n          </div>\n          <Button\n            type=\"submit\"\n            className=\"w-full py-3 text-base sm:text-lg font-semibold\"\n            disabled={!input.trim() || isLoading}\n          >\n            {isLoading ? \"Generating...\" : \"Generate Webpage\"}\n          </Button>\n        </form>\n      </div>\n\n      {/* Examples */}\n      <div className=\"text-center\">\n        <p className=\"text-gray-600 mb-4 text-sm sm:text-base\">\n          Try these example URLs:\n        </p>\n        <div className=\"flex flex-wrap gap-2 justify-center px-2\">\n          {examples.map((example) => (\n            <button\n              key={example}\n              onClick={() => setInput(example)}\n              className=\"px-2 sm:px-3 py-1 text-xs sm:text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors font-mono break-all\"\n              disabled={isLoading}\n            >\n              /{example}\n            </button>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,ySAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,aAAa;QACb,gCAAgC;QAChC,MAAM,aAAa,MAChB,IAAI,GACJ,OAAO,CAAC,sBAAsB,IAC9B,OAAO,CAAC,QAAQ;QACnB,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY;IAC9B;IAEA,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,qZAAC;QAAI,WAAU;;0BAEb,qZAAC;gBAAI,WAAU;0BACb,cAAA,qZAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,qZAAC;;8CACC,qZAAC;oCACC,SAAQ;oCACR,WAAU;8CACX;;;;;;8CAGD,qZAAC;oCAAI,WAAU;;sDACb,qZAAC;4CAAK,WAAU;sDAA2E;;;;;;sDAG3F,qZAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,aAAY;4CACZ,WAAU;4CACV,UAAU;;;;;;;;;;;;;;;;;;sCAIhB,qZAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU,CAAC,MAAM,IAAI,MAAM;sCAE1B,YAAY,kBAAkB;;;;;;;;;;;;;;;;;0BAMrC,qZAAC;gBAAI,WAAU;;kCACb,qZAAC;wBAAE,WAAU;kCAA0C;;;;;;kCAGvD,qZAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,qZAAC;gCAEC,SAAS,IAAM,SAAS;gCACxB,WAAU;gCACV,UAAU;;oCACX;oCACG;;+BALG;;;;;;;;;;;;;;;;;;;;;;AAYnB", "debugId": null}}]}