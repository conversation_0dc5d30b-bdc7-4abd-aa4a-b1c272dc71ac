{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/utils/lodash.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isArguments = exports.defaults = exports.noop = void 0;\nconst defaults = require(\"lodash.defaults\");\nexports.defaults = defaults;\nconst isArguments = require(\"lodash.isarguments\");\nexports.isArguments = isArguments;\nfunction noop() { }\nexports.noop = noop;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG,QAAQ,QAAQ,GAAG,QAAQ,IAAI,GAAG,KAAK;AAC7D,MAAM;AACN,QAAQ,QAAQ,GAAG;AACnB,MAAM;AACN,QAAQ,WAAW,GAAG;AACtB,SAAS,QAAS;AAClB,QAAQ,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/utils/debug.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.genRedactedString = exports.getStringValue = exports.MAX_ARGUMENT_LENGTH = void 0;\nconst debug_1 = require(\"debug\");\nconst MAX_ARGUMENT_LENGTH = 200;\nexports.MAX_ARGUMENT_LENGTH = MAX_ARGUMENT_LENGTH;\nconst NAMESPACE_PREFIX = \"ioredis\";\n/**\n * helper function that tried to get a string value for\n * arbitrary \"debug\" arg\n */\nfunction getStringValue(v) {\n    if (v === null) {\n        return;\n    }\n    switch (typeof v) {\n        case \"boolean\":\n            return;\n        case \"number\":\n            return;\n        case \"object\":\n            if (Buffer.isBuffer(v)) {\n                return v.toString(\"hex\");\n            }\n            if (Array.isArray(v)) {\n                return v.join(\",\");\n            }\n            try {\n                return JSON.stringify(v);\n            }\n            catch (e) {\n                return;\n            }\n        case \"string\":\n            return v;\n    }\n}\nexports.getStringValue = getStringValue;\n/**\n * helper function that redacts a string representation of a \"debug\" arg\n */\nfunction genRedactedString(str, maxLen) {\n    const { length } = str;\n    return length <= maxLen\n        ? str\n        : str.slice(0, maxLen) + ' ... <REDACTED full-length=\"' + length + '\">';\n}\nexports.genRedactedString = genRedactedString;\n/**\n * a wrapper for the `debug` module, used to generate\n * \"debug functions\" that trim the values in their output\n */\nfunction genDebugFunction(namespace) {\n    const fn = (0, debug_1.default)(`${NAMESPACE_PREFIX}:${namespace}`);\n    function wrappedDebug(...args) {\n        if (!fn.enabled) {\n            return; // no-op\n        }\n        // we skip the first arg because that is the message\n        for (let i = 1; i < args.length; i++) {\n            const str = getStringValue(args[i]);\n            if (typeof str === \"string\" && str.length > MAX_ARGUMENT_LENGTH) {\n                args[i] = genRedactedString(str, MAX_ARGUMENT_LENGTH);\n            }\n        }\n        return fn.apply(null, args);\n    }\n    Object.defineProperties(wrappedDebug, {\n        namespace: {\n            get() {\n                return fn.namespace;\n            },\n        },\n        enabled: {\n            get() {\n                return fn.enabled;\n            },\n        },\n        destroy: {\n            get() {\n                return fn.destroy;\n            },\n        },\n        log: {\n            get() {\n                return fn.log;\n            },\n            set(l) {\n                fn.log = l;\n            },\n        },\n    });\n    return wrappedDebug;\n}\nexports.default = genDebugFunction;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,QAAQ,cAAc,GAAG,QAAQ,mBAAmB,GAAG,KAAK;AACxF,MAAM;AACN,MAAM,sBAAsB;AAC5B,QAAQ,mBAAmB,GAAG;AAC9B,MAAM,mBAAmB;AACzB;;;CAGC,GACD,SAAS,eAAe,CAAC;IACrB,IAAI,MAAM,MAAM;QACZ;IACJ;IACA,OAAQ,OAAO;QACX,KAAK;YACD;QACJ,KAAK;YACD;QACJ,KAAK;YACD,IAAI,OAAO,QAAQ,CAAC,IAAI;gBACpB,OAAO,EAAE,QAAQ,CAAC;YACtB;YACA,IAAI,MAAM,OAAO,CAAC,IAAI;gBAClB,OAAO,EAAE,IAAI,CAAC;YAClB;YACA,IAAI;gBACA,OAAO,KAAK,SAAS,CAAC;YAC1B,EACA,OAAO,GAAG;gBACN;YACJ;QACJ,KAAK;YACD,OAAO;IACf;AACJ;AACA,QAAQ,cAAc,GAAG;AACzB;;CAEC,GACD,SAAS,kBAAkB,GAAG,EAAE,MAAM;IAClC,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,OAAO,UAAU,SACX,MACA,IAAI,KAAK,CAAC,GAAG,UAAU,iCAAiC,SAAS;AAC3E;AACA,QAAQ,iBAAiB,GAAG;AAC5B;;;CAGC,GACD,SAAS,iBAAiB,SAAS;IAC/B,MAAM,KAAK,CAAC,GAAG,QAAQ,OAAO,EAAE,GAAG,iBAAiB,CAAC,EAAE,WAAW;IAClE,SAAS,aAAa,GAAG,IAAI;QACzB,IAAI,CAAC,GAAG,OAAO,EAAE;YACb,QAAQ,QAAQ;QACpB;QACA,oDAAoD;QACpD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAClC,MAAM,MAAM,eAAe,IAAI,CAAC,EAAE;YAClC,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,GAAG,qBAAqB;gBAC7D,IAAI,CAAC,EAAE,GAAG,kBAAkB,KAAK;YACrC;QACJ;QACA,OAAO,GAAG,KAAK,CAAC,MAAM;IAC1B;IACA,OAAO,gBAAgB,CAAC,cAAc;QAClC,WAAW;YACP;gBACI,OAAO,GAAG,SAAS;YACvB;QACJ;QACA,SAAS;YACL;gBACI,OAAO,GAAG,OAAO;YACrB;QACJ;QACA,SAAS;YACL;gBACI,OAAO,GAAG,OAAO;YACrB;QACJ;QACA,KAAK;YACD;gBACI,OAAO,GAAG,GAAG;YACjB;YACA,KAAI,CAAC;gBACD,GAAG,GAAG,GAAG;YACb;QACJ;IACJ;IACA,OAAO;AACX;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/constants/TLSProfiles.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * TLS settings for Redis Cloud. Updated on 2022-08-19.\n */\nconst RedisCloudCA = `-----BEGIN CERTIFICATE-----\nMIIDTzCCAjegAwIBAgIJAKSVpiDswLcwMA0GCSqGSIb3DQEBBQUAMD4xFjAUBgNV\nBAoMDUdhcmFudGlhIERhdGExJDAiBgNVBAMMG1NTTCBDZXJ0aWZpY2F0aW9uIEF1\ndGhvcml0eTAeFw0xMzEwMDExMjE0NTVaFw0yMzA5MjkxMjE0NTVaMD4xFjAUBgNV\nBAoMDUdhcmFudGlhIERhdGExJDAiBgNVBAMMG1NTTCBDZXJ0aWZpY2F0aW9uIEF1\ndGhvcml0eTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALZqkh/DczWP\nJnxnHLQ7QL0T4B4CDKWBKCcisriGbA6ZePWVNo4hfKQC6JrzfR+081NeD6VcWUiz\nrmd+jtPhIY4c+WVQYm5PKaN6DT1imYdxQw7aqO5j2KUCEh/cznpLxeSHoTxlR34E\nQwF28Wl3eg2vc5ct8LjU3eozWVk3gb7alx9mSA2SgmuX5lEQawl++rSjsBStemY2\nBDwOpAMXIrdEyP/cVn8mkvi/BDs5M5G+09j0gfhyCzRWMQ7Hn71u1eolRxwVxgi3\nTMn+/vTaFSqxKjgck6zuAYjBRPaHe7qLxHNr1So/Mc9nPy+3wHebFwbIcnUojwbp\n4nctkWbjb2cCAwEAAaNQME4wHQYDVR0OBBYEFP1whtcrydmW3ZJeuSoKZIKjze3w\nMB8GA1UdIwQYMBaAFP1whtcrydmW3ZJeuSoKZIKjze3wMAwGA1UdEwQFMAMBAf8w\nDQYJKoZIhvcNAQEFBQADggEBAG2erXhwRAa7+ZOBs0B6X57Hwyd1R4kfmXcs0rta\nlbPpvgULSiB+TCbf3EbhJnHGyvdCY1tvlffLjdA7HJ0PCOn+YYLBA0pTU/dyvrN6\nSu8NuS5yubnt9mb13nDGYo1rnt0YRfxN+8DM3fXIVr038A30UlPX2Ou1ExFJT0MZ\nuFKY6ZvLdI6/1cbgmguMlAhM+DhKyV6Sr5699LM3zqeI816pZmlREETYkGr91q7k\nBpXJu/dtHaGxg1ZGu6w/PCsYGUcECWENYD4VQPd8N32JjOfu6vEgoEAwfPP+3oGp\nZ4m3ewACcWOAenqflb+cQYC4PsF7qbXDmRaWrbKntOlZ3n0=\n-----END CERTIFICATE-----\n-----BEGIN CERTIFICATE-----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\n616pxqo=\n-----END CERTIFICATE-----\n-----BEGIN CERTIFICATE-----\nMIIFujCCA6KgAwIBAgIJAJ1aTT1lu2ScMA0GCSqGSIb3DQEBCwUAMGoxCzAJBgNV\nBAYTAlVTMQswCQYDVQQIDAJDQTELMAkGA1UEBwwCQ0ExEjAQBgNVBAoMCVJlZGlz\nTGFiczEtMCsGA1UEAwwkUmVkaXNMYWJzIFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9y\naXR5MB4XDTE4MDIyNTE1MjA0MloXDTM4MDIyMDE1MjA0MlowajELMAkGA1UEBhMC\nVVMxCzAJBgNVBAgMAkNBMQswCQYDVQQHDAJDQTESMBAGA1UECgwJUmVkaXNMYWJz\nMS0wKwYDVQQDDCRSZWRpc0xhYnMgUm9vdCBDZXJ0aWZpY2F0ZSBBdXRob3JpdHkw\nggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQDLEjXy7YrbN5Waau5cd6g1\nG5C2tMmeTpZ0duFAPxNU4oE3RHS5gGiok346fUXuUxbZ6QkuzeN2/2Z+RmRcJhQY\nDm0ZgdG4x59An1TJfnzKKoWj8ISmoHS/TGNBdFzXV7FYNLBuqZouqePI6ReC6Qhl\npp45huV32Q3a6IDrrvx7Wo5ZczEQeFNbCeCOQYNDdTmCyEkHqc2AGo8eoIlSTutT\nULOC7R5gzJVTS0e1hesQ7jmqHjbO+VQS1NAL4/5K6cuTEqUl+XhVhPdLWBXJQ5ag\n54qhX4v+ojLzeU1R/Vc6NjMvVtptWY6JihpgplprN0Yh2556ewcXMeturcKgXfGJ\nxeYzsjzXerEjrVocX5V8BNrg64NlifzTMKNOOv4fVZszq1SIHR8F9ROrqiOdh8iC\nJpUbLpXH9hWCSEO6VRMB2xJoKu3cgl63kF30s77x7wLFMEHiwsQRKxooE1UhgS9K\n2sO4TlQ1eWUvFvHSTVDQDlGQ6zu4qjbOpb3Q8bQwoK+ai2alkXVR4Ltxe9QlgYK3\nStsnPhruzZGA0wbXdpw0bnM+YdlEm5ffSTpNIfgHeaa7Dtb801FtA71ZlH7A6TaI\nSIQuUST9EKmv7xrJyx0W1pGoPOLw5T029aTjnICSLdtV9bLwysrLhIYG5bnPq78B\ncS+jZHFGzD7PUVGQD01nOQIDAQABo2MwYTAdBgNVHQ4EFgQUIuNsB+oTOC9rLoxL\nyzNP7vJUV08wHwYDVR0jBBgwFoAUIuNsB+oTOC9rLoxLyzNP7vJUV08wDwYDVR0T\nAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAYYwDQYJKoZIhvcNAQELBQADggIBAHfg\nz5pMNUAKdMzK1aS1EDdK9yKz4qicILz5czSLj1mC7HKDRy8cVADUxEICis++CsCu\nrYOvyCVergHQLREcxPq4rc5Nq1uj6J6649NEeh4WazOOjL4ZfQ1jVznMbGy+fJm3\n3Hoelv6jWRG9iqeJZja7/1s6YC6bWymI/OY1e4wUKeNHAo+Vger7MlHV+RuabaX+\nhSJ8bJAM59NCM7AgMTQpJCncrcdLeceYniGy5Q/qt2b5mJkQVkIdy4TPGGB+AXDJ\nD0q3I/JDRkDUFNFdeW0js7fHdsvCR7O3tJy5zIgEV/o/BCkmJVtuwPYOrw/yOlKj\nTY/U7ATAx9VFF6/vYEOMYSmrZlFX+98L6nJtwDqfLB5VTltqZ4H/KBxGE3IRSt9l\nFXy40U+LnXzhhW+7VBAvyYX8GEXhHkKU8Gqk1xitrqfBXY74xKgyUSTolFSfFVgj\nmcM/X4K45bka+qpkj7Kfv/8D4j6aZekwhN2ly6hhC1SmQ8qjMjpG/mrWOSSHZFmf\nybu9iD2AYHeIOkshIl6xYIa++Q/00/vs46IzAbQyriOi0XxlSMMVtPx0Q3isp+ji\nn8Mq9eOuxYOEQ4of8twUkUDd528iwGtEdwf0Q01UyT84S62N8AySl1ZBKXJz6W4F\nUhWfa/HQYOAPDdEjNgnVwLI23b8t0TozyCWw7q8h\n-----END CERTIFICATE-----\n\n-----BEGIN CERTIFICATE-----\nMIIEjzCCA3egAwIBAgIQe55B/ALCKJDZtdNT8kD6hTANBgkqhkiG9w0BAQsFADBM\nMSAwHgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEGA1UEChMKR2xv\nYmFsU2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjAeFw0yMjAxMjYxMjAwMDBaFw0y\nNTAxMjYwMDAwMDBaMFgxCzAJBgNVBAYTAkJFMRkwFwYDVQQKExBHbG9iYWxTaWdu\nIG52LXNhMS4wLAYDVQQDEyVHbG9iYWxTaWduIEF0bGFzIFIzIE9WIFRMUyBDQSAy\nMDIyIFEyMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmGmg1LW9b7Lf\n8zDD83yBDTEkt+FOxKJZqF4veWc5KZsQj9HfnUS2e5nj/E+JImlGPsQuoiosLuXD\nBVBNAMcUFa11buFMGMeEMwiTmCXoXRrXQmH0qjpOfKgYc5gHG3BsRGaRrf7VR4eg\nofNMG9wUBw4/g/TT7+bQJdA4NfE7Y4d5gEryZiBGB/swaX6Jp/8MF4TgUmOWmalK\ndZCKyb4sPGQFRTtElk67F7vU+wdGcrcOx1tDcIB0ncjLPMnaFicagl+daWGsKqTh\ncounQb6QJtYHa91KvCfKWocMxQ7OIbB5UARLPmC4CJ1/f8YFm35ebfzAeULYdGXu\njE9CLor0OwIDAQABo4IBXzCCAVswDgYDVR0PAQH/BAQDAgGGMB0GA1UdJQQWMBQG\nCCsGAQUFBwMBBggrBgEFBQcDAjASBgNVHRMBAf8ECDAGAQH/AgEAMB0GA1UdDgQW\nBBSH5Zq7a7B/t95GfJWkDBpA8HHqdjAfBgNVHSMEGDAWgBSP8Et/qC5FJK5NUPpj\nmove4t0bvDB7BggrBgEFBQcBAQRvMG0wLgYIKwYBBQUHMAGGImh0dHA6Ly9vY3Nw\nMi5nbG9iYWxzaWduLmNvbS9yb290cjMwOwYIKwYBBQUHMAKGL2h0dHA6Ly9zZWN1\ncmUuZ2xvYmFsc2lnbi5jb20vY2FjZXJ0L3Jvb3QtcjMuY3J0MDYGA1UdHwQvMC0w\nK6ApoCeGJWh0dHA6Ly9jcmwuZ2xvYmFsc2lnbi5jb20vcm9vdC1yMy5jcmwwIQYD\nVR0gBBowGDAIBgZngQwBAgIwDAYKKwYBBAGgMgoBAjANBgkqhkiG9w0BAQsFAAOC\nAQEAKRic9/f+nmhQU/wz04APZLjgG5OgsuUOyUEZjKVhNGDwxGTvKhyXGGAMW2B/\n3bRi+aElpXwoxu3pL6fkElbX3B0BeS5LoDtxkyiVEBMZ8m+sXbocwlPyxrPbX6mY\n0rVIvnuUeBH8X0L5IwfpNVvKnBIilTbcebfHyXkPezGwz7E1yhUULjJFm2bt0SdX\ny+4X/WeiiYIv+fTVgZZgl+/2MKIsu/qdBJc3f3TvJ8nz+Eax1zgZmww+RSQWeOj3\n15Iw6Z5FX+NwzY/Ab+9PosR5UosSeq+9HhtaxZttXG1nVh+avYPGYddWmiMT90J5\nZgKnO/Fx2hBgTxhOTMYaD312kg==\n-----END CERTIFICATE-----\n\n-----BEGIN CERTIFICATE-----\nMIIDXzCCAkegAwIBAgILBAAAAAABIVhTCKIwDQYJKoZIhvcNAQELBQAwTDEgMB4G\nA1UECxMXR2xvYmFsU2lnbiBSb290IENBIC0gUjMxEzARBgNVBAoTCkdsb2JhbFNp\nZ24xEzARBgNVBAMTCkdsb2JhbFNpZ24wHhcNMDkwMzE4MTAwMDAwWhcNMjkwMzE4\nMTAwMDAwWjBMMSAwHgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEG\nA1UEChMKR2xvYmFsU2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjCCASIwDQYJKoZI\nhvcNAQEBBQADggEPADCCAQoCggEBAMwldpB5BngiFvXAg7aEyiie/QV2EcWtiHL8\nRgJDx7KKnQRfJMsuS+FggkbhUqsMgUdwbN1k0ev1LKMPgj0MK66X17YUhhB5uzsT\ngHeMCOFJ0mpiLx9e+pZo34knlTifBtc+ycsmWQ1z3rDI6SYOgxXG71uL0gRgykmm\nKPZpO/bLyCiR5Z2KYVc3rHQU3HTgOu5yLy6c+9C7v/U9AOEGM+iCK65TpjoWc4zd\nQQ4gOsC0p6Hpsk+QLjJg6VfLuQSSaGjlOCZgdbKfd/+RFO+uIEn8rUAVSNECMWEZ\nXriX7613t2Saer9fwRPvm2L7DWzgVGkWqQPabumDk3F2xmmFghcCAwEAAaNCMEAw\nDgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFI/wS3+o\nLkUkrk1Q+mOai97i3Ru8MA0GCSqGSIb3DQEBCwUAA4IBAQBLQNvAUKr+yAzv95ZU\nRUm7lgAJQayzE4aGKAczymvmdLm6AC2upArT9fHxD4q/c2dKg8dEe3jgr25sbwMp\njjM5RcOO5LlXbKr8EpbsU8Yt5CRsuZRj+9xTaGdWPoO4zzUhw8lo/s7awlOqzJCK\n6fBdRoyV3XpYKBovHd7NADdBj+1EbddTKJd+82cEHhXXipa0095MJ6RMG3NzdvQX\nmcIfeg7jLQitChws/zyrVQ4PkX4268NXSb7hLi18YIvDQVETI53O9zJrlAGomecs\nMx86OyXShkDOOyyGeMlhLxS67ttVb9+E7gUJTb0o2HLO02JQZR7rkpeDMdmztcpH\nWD9f\n-----END CERTIFICATE-----`;\nconst TLSProfiles = {\n    RedisCloudFixed: { ca: RedisCloudCA },\n    RedisCloudFlexible: { ca: RedisCloudCA },\n};\nexports.default = TLSProfiles;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D;;CAEC,GACD,MAAM,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yBA0IG,CAAC;AAC1B,MAAM,cAAc;IAChB,iBAAiB;QAAE,IAAI;IAAa;IACpC,oBAAoB;QAAE,IAAI;IAAa;AAC3C;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/utils/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.noop = exports.defaults = exports.Debug = exports.zipMap = exports.CONNECTION_CLOSED_ERROR_MSG = exports.shuffle = exports.sample = exports.resolveTLSProfile = exports.parseURL = exports.optimizeErrorStack = exports.toArg = exports.convertMapToArray = exports.convertObjectToArray = exports.timeout = exports.packObject = exports.isInt = exports.wrapMultiResult = exports.convertBufferToString = void 0;\nconst url_1 = require(\"url\");\nconst lodash_1 = require(\"./lodash\");\nObject.defineProperty(exports, \"defaults\", { enumerable: true, get: function () { return lodash_1.defaults; } });\nObject.defineProperty(exports, \"noop\", { enumerable: true, get: function () { return lodash_1.noop; } });\nconst debug_1 = require(\"./debug\");\nexports.Debug = debug_1.default;\nconst TLSProfiles_1 = require(\"../constants/TLSProfiles\");\n/**\n * Convert a buffer to string, supports buffer array\n *\n * @example\n * ```js\n * const input = [Buffer.from('foo'), [Buffer.from('bar')]]\n * const res = convertBufferToString(input, 'utf8')\n * expect(res).to.eql(['foo', ['bar']])\n * ```\n */\nfunction convertBufferToString(value, encoding) {\n    if (value instanceof Buffer) {\n        return value.toString(encoding);\n    }\n    if (Array.isArray(value)) {\n        const length = value.length;\n        const res = Array(length);\n        for (let i = 0; i < length; ++i) {\n            res[i] =\n                value[i] instanceof Buffer && encoding === \"utf8\"\n                    ? value[i].toString()\n                    : convertBufferToString(value[i], encoding);\n        }\n        return res;\n    }\n    return value;\n}\nexports.convertBufferToString = convertBufferToString;\n/**\n * Convert a list of results to node-style\n *\n * @example\n * ```js\n * const input = ['a', 'b', new Error('c'), 'd']\n * const output = exports.wrapMultiResult(input)\n * expect(output).to.eql([[null, 'a'], [null, 'b'], [new Error('c')], [null, 'd'])\n * ```\n */\nfunction wrapMultiResult(arr) {\n    // When using WATCH/EXEC transactions, the EXEC will return\n    // a null instead of an array\n    if (!arr) {\n        return null;\n    }\n    const result = [];\n    const length = arr.length;\n    for (let i = 0; i < length; ++i) {\n        const item = arr[i];\n        if (item instanceof Error) {\n            result.push([item]);\n        }\n        else {\n            result.push([null, item]);\n        }\n    }\n    return result;\n}\nexports.wrapMultiResult = wrapMultiResult;\n/**\n * Detect if the argument is a int\n * @example\n * ```js\n * > isInt('123')\n * true\n * > isInt('123.3')\n * false\n * > isInt('1x')\n * false\n * > isInt(123)\n * true\n * > isInt(true)\n * false\n * ```\n */\nfunction isInt(value) {\n    const x = parseFloat(value);\n    return !isNaN(value) && (x | 0) === x;\n}\nexports.isInt = isInt;\n/**\n * Pack an array to an Object\n *\n * @example\n * ```js\n * > packObject(['a', 'b', 'c', 'd'])\n * { a: 'b', c: 'd' }\n * ```\n */\nfunction packObject(array) {\n    const result = {};\n    const length = array.length;\n    for (let i = 1; i < length; i += 2) {\n        result[array[i - 1]] = array[i];\n    }\n    return result;\n}\nexports.packObject = packObject;\n/**\n * Return a callback with timeout\n */\nfunction timeout(callback, timeout) {\n    let timer = null;\n    const run = function () {\n        if (timer) {\n            clearTimeout(timer);\n            timer = null;\n            callback.apply(this, arguments);\n        }\n    };\n    timer = setTimeout(run, timeout, new Error(\"timeout\"));\n    return run;\n}\nexports.timeout = timeout;\n/**\n * Convert an object to an array\n * @example\n * ```js\n * > convertObjectToArray({ a: '1' })\n * ['a', '1']\n * ```\n */\nfunction convertObjectToArray(obj) {\n    const result = [];\n    const keys = Object.keys(obj); // Object.entries requires node 7+\n    for (let i = 0, l = keys.length; i < l; i++) {\n        result.push(keys[i], obj[keys[i]]);\n    }\n    return result;\n}\nexports.convertObjectToArray = convertObjectToArray;\n/**\n * Convert a map to an array\n * @example\n * ```js\n * > convertMapToArray(new Map([[1, '2']]))\n * [1, '2']\n * ```\n */\nfunction convertMapToArray(map) {\n    const result = [];\n    let pos = 0;\n    map.forEach(function (value, key) {\n        result[pos] = key;\n        result[pos + 1] = value;\n        pos += 2;\n    });\n    return result;\n}\nexports.convertMapToArray = convertMapToArray;\n/**\n * Convert a non-string arg to a string\n */\nfunction toArg(arg) {\n    if (arg === null || typeof arg === \"undefined\") {\n        return \"\";\n    }\n    return String(arg);\n}\nexports.toArg = toArg;\n/**\n * Optimize error stack\n *\n * @param error actually error\n * @param friendlyStack the stack that more meaningful\n * @param filterPath only show stacks with the specified path\n */\nfunction optimizeErrorStack(error, friendlyStack, filterPath) {\n    const stacks = friendlyStack.split(\"\\n\");\n    let lines = \"\";\n    let i;\n    for (i = 1; i < stacks.length; ++i) {\n        if (stacks[i].indexOf(filterPath) === -1) {\n            break;\n        }\n    }\n    for (let j = i; j < stacks.length; ++j) {\n        lines += \"\\n\" + stacks[j];\n    }\n    if (error.stack) {\n        const pos = error.stack.indexOf(\"\\n\");\n        error.stack = error.stack.slice(0, pos) + lines;\n    }\n    return error;\n}\nexports.optimizeErrorStack = optimizeErrorStack;\n/**\n * Parse the redis protocol url\n */\nfunction parseURL(url) {\n    if (isInt(url)) {\n        return { port: url };\n    }\n    let parsed = (0, url_1.parse)(url, true, true);\n    if (!parsed.slashes && url[0] !== \"/\") {\n        url = \"//\" + url;\n        parsed = (0, url_1.parse)(url, true, true);\n    }\n    const options = parsed.query || {};\n    const result = {};\n    if (parsed.auth) {\n        const index = parsed.auth.indexOf(\":\");\n        result.username = index === -1 ? parsed.auth : parsed.auth.slice(0, index);\n        result.password = index === -1 ? \"\" : parsed.auth.slice(index + 1);\n    }\n    if (parsed.pathname) {\n        if (parsed.protocol === \"redis:\" || parsed.protocol === \"rediss:\") {\n            if (parsed.pathname.length > 1) {\n                result.db = parsed.pathname.slice(1);\n            }\n        }\n        else {\n            result.path = parsed.pathname;\n        }\n    }\n    if (parsed.host) {\n        result.host = parsed.hostname;\n    }\n    if (parsed.port) {\n        result.port = parsed.port;\n    }\n    if (typeof options.family === \"string\") {\n        const intFamily = Number.parseInt(options.family, 10);\n        if (!Number.isNaN(intFamily)) {\n            result.family = intFamily;\n        }\n    }\n    (0, lodash_1.defaults)(result, options);\n    return result;\n}\nexports.parseURL = parseURL;\n/**\n * Resolve TLS profile shortcut in connection options\n */\nfunction resolveTLSProfile(options) {\n    let tls = options === null || options === void 0 ? void 0 : options.tls;\n    if (typeof tls === \"string\")\n        tls = { profile: tls };\n    const profile = TLSProfiles_1.default[tls === null || tls === void 0 ? void 0 : tls.profile];\n    if (profile) {\n        tls = Object.assign({}, profile, tls);\n        delete tls.profile;\n        options = Object.assign({}, options, { tls });\n    }\n    return options;\n}\nexports.resolveTLSProfile = resolveTLSProfile;\n/**\n * Get a random element from `array`\n */\nfunction sample(array, from = 0) {\n    const length = array.length;\n    if (from >= length) {\n        return null;\n    }\n    return array[from + Math.floor(Math.random() * (length - from))];\n}\nexports.sample = sample;\n/**\n * Shuffle the array using the Fisher-Yates Shuffle.\n * This method will mutate the original array.\n */\nfunction shuffle(array) {\n    let counter = array.length;\n    // While there are elements in the array\n    while (counter > 0) {\n        // Pick a random index\n        const index = Math.floor(Math.random() * counter);\n        // Decrease counter by 1\n        counter--;\n        // And swap the last element with it\n        [array[counter], array[index]] = [array[index], array[counter]];\n    }\n    return array;\n}\nexports.shuffle = shuffle;\n/**\n * Error message for connection being disconnected\n */\nexports.CONNECTION_CLOSED_ERROR_MSG = \"Connection is closed.\";\nfunction zipMap(keys, values) {\n    const map = new Map();\n    keys.forEach((key, index) => {\n        map.set(key, values[index]);\n    });\n    return map;\n}\nexports.zipMap = zipMap;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,IAAI,GAAG,QAAQ,QAAQ,GAAG,QAAQ,KAAK,GAAG,QAAQ,MAAM,GAAG,QAAQ,2BAA2B,GAAG,QAAQ,OAAO,GAAG,QAAQ,MAAM,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,QAAQ,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,KAAK,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,oBAAoB,GAAG,QAAQ,OAAO,GAAG,QAAQ,UAAU,GAAG,QAAQ,KAAK,GAAG,QAAQ,eAAe,GAAG,QAAQ,qBAAqB,GAAG,KAAK;AACzZ,MAAM;AACN,MAAM;AACN,OAAO,cAAc,CAAC,SAAS,YAAY;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,SAAS,QAAQ;IAAE;AAAE;AAC9G,OAAO,cAAc,CAAC,SAAS,QAAQ;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,SAAS,IAAI;IAAE;AAAE;AACtG,MAAM;AACN,QAAQ,KAAK,GAAG,QAAQ,OAAO;AAC/B,MAAM;AACN;;;;;;;;;CASC,GACD,SAAS,sBAAsB,KAAK,EAAE,QAAQ;IAC1C,IAAI,iBAAiB,QAAQ;QACzB,OAAO,MAAM,QAAQ,CAAC;IAC1B;IACA,IAAI,MAAM,OAAO,CAAC,QAAQ;QACtB,MAAM,SAAS,MAAM,MAAM;QAC3B,MAAM,MAAM,MAAM;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,EAAE,EAAG;YAC7B,GAAG,CAAC,EAAE,GACF,KAAK,CAAC,EAAE,YAAY,UAAU,aAAa,SACrC,KAAK,CAAC,EAAE,CAAC,QAAQ,KACjB,sBAAsB,KAAK,CAAC,EAAE,EAAE;QAC9C;QACA,OAAO;IACX;IACA,OAAO;AACX;AACA,QAAQ,qBAAqB,GAAG;AAChC;;;;;;;;;CASC,GACD,SAAS,gBAAgB,GAAG;IACxB,2DAA2D;IAC3D,6BAA6B;IAC7B,IAAI,CAAC,KAAK;QACN,OAAO;IACX;IACA,MAAM,SAAS,EAAE;IACjB,MAAM,SAAS,IAAI,MAAM;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,EAAE,EAAG;QAC7B,MAAM,OAAO,GAAG,CAAC,EAAE;QACnB,IAAI,gBAAgB,OAAO;YACvB,OAAO,IAAI,CAAC;gBAAC;aAAK;QACtB,OACK;YACD,OAAO,IAAI,CAAC;gBAAC;gBAAM;aAAK;QAC5B;IACJ;IACA,OAAO;AACX;AACA,QAAQ,eAAe,GAAG;AAC1B;;;;;;;;;;;;;;;CAeC,GACD,SAAS,MAAM,KAAK;IAChB,MAAM,IAAI,WAAW;IACrB,OAAO,CAAC,MAAM,UAAU,CAAC,IAAI,CAAC,MAAM;AACxC;AACA,QAAQ,KAAK,GAAG;AAChB;;;;;;;;CAQC,GACD,SAAS,WAAW,KAAK;IACrB,MAAM,SAAS,CAAC;IAChB,MAAM,SAAS,MAAM,MAAM;IAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,KAAK,EAAG;QAChC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE;IACnC;IACA,OAAO;AACX;AACA,QAAQ,UAAU,GAAG;AACrB;;CAEC,GACD,SAAS,QAAQ,QAAQ,EAAE,OAAO;IAC9B,IAAI,QAAQ;IACZ,MAAM,MAAM;QACR,IAAI,OAAO;YACP,aAAa;YACb,QAAQ;YACR,SAAS,KAAK,CAAC,IAAI,EAAE;QACzB;IACJ;IACA,QAAQ,WAAW,KAAK,SAAS,IAAI,MAAM;IAC3C,OAAO;AACX;AACA,QAAQ,OAAO,GAAG;AAClB;;;;;;;CAOC,GACD,SAAS,qBAAqB,GAAG;IAC7B,MAAM,SAAS,EAAE;IACjB,MAAM,OAAO,OAAO,IAAI,CAAC,MAAM,kCAAkC;IACjE,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,GAAG,IAAK;QACzC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;IACrC;IACA,OAAO;AACX;AACA,QAAQ,oBAAoB,GAAG;AAC/B;;;;;;;CAOC,GACD,SAAS,kBAAkB,GAAG;IAC1B,MAAM,SAAS,EAAE;IACjB,IAAI,MAAM;IACV,IAAI,OAAO,CAAC,SAAU,KAAK,EAAE,GAAG;QAC5B,MAAM,CAAC,IAAI,GAAG;QACd,MAAM,CAAC,MAAM,EAAE,GAAG;QAClB,OAAO;IACX;IACA,OAAO;AACX;AACA,QAAQ,iBAAiB,GAAG;AAC5B;;CAEC,GACD,SAAS,MAAM,GAAG;IACd,IAAI,QAAQ,QAAQ,OAAO,QAAQ,aAAa;QAC5C,OAAO;IACX;IACA,OAAO,OAAO;AAClB;AACA,QAAQ,KAAK,GAAG;AAChB;;;;;;CAMC,GACD,SAAS,mBAAmB,KAAK,EAAE,aAAa,EAAE,UAAU;IACxD,MAAM,SAAS,cAAc,KAAK,CAAC;IACnC,IAAI,QAAQ;IACZ,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;QAChC,IAAI,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG;YACtC;QACJ;IACJ;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;QACpC,SAAS,OAAO,MAAM,CAAC,EAAE;IAC7B;IACA,IAAI,MAAM,KAAK,EAAE;QACb,MAAM,MAAM,MAAM,KAAK,CAAC,OAAO,CAAC;QAChC,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO;IAC9C;IACA,OAAO;AACX;AACA,QAAQ,kBAAkB,GAAG;AAC7B;;CAEC,GACD,SAAS,SAAS,GAAG;IACjB,IAAI,MAAM,MAAM;QACZ,OAAO;YAAE,MAAM;QAAI;IACvB;IACA,IAAI,SAAS,CAAC,GAAG,MAAM,KAAK,EAAE,KAAK,MAAM;IACzC,IAAI,CAAC,OAAO,OAAO,IAAI,GAAG,CAAC,EAAE,KAAK,KAAK;QACnC,MAAM,OAAO;QACb,SAAS,CAAC,GAAG,MAAM,KAAK,EAAE,KAAK,MAAM;IACzC;IACA,MAAM,UAAU,OAAO,KAAK,IAAI,CAAC;IACjC,MAAM,SAAS,CAAC;IAChB,IAAI,OAAO,IAAI,EAAE;QACb,MAAM,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC;QAClC,OAAO,QAAQ,GAAG,UAAU,CAAC,IAAI,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG;QACpE,OAAO,QAAQ,GAAG,UAAU,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IACpE;IACA,IAAI,OAAO,QAAQ,EAAE;QACjB,IAAI,OAAO,QAAQ,KAAK,YAAY,OAAO,QAAQ,KAAK,WAAW;YAC/D,IAAI,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC5B,OAAO,EAAE,GAAG,OAAO,QAAQ,CAAC,KAAK,CAAC;YACtC;QACJ,OACK;YACD,OAAO,IAAI,GAAG,OAAO,QAAQ;QACjC;IACJ;IACA,IAAI,OAAO,IAAI,EAAE;QACb,OAAO,IAAI,GAAG,OAAO,QAAQ;IACjC;IACA,IAAI,OAAO,IAAI,EAAE;QACb,OAAO,IAAI,GAAG,OAAO,IAAI;IAC7B;IACA,IAAI,OAAO,QAAQ,MAAM,KAAK,UAAU;QACpC,MAAM,YAAY,OAAO,QAAQ,CAAC,QAAQ,MAAM,EAAE;QAClD,IAAI,CAAC,OAAO,KAAK,CAAC,YAAY;YAC1B,OAAO,MAAM,GAAG;QACpB;IACJ;IACA,CAAC,GAAG,SAAS,QAAQ,EAAE,QAAQ;IAC/B,OAAO;AACX;AACA,QAAQ,QAAQ,GAAG;AACnB;;CAEC,GACD,SAAS,kBAAkB,OAAO;IAC9B,IAAI,MAAM,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAG;IACvE,IAAI,OAAO,QAAQ,UACf,MAAM;QAAE,SAAS;IAAI;IACzB,MAAM,UAAU,cAAc,OAAO,CAAC,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,OAAO,CAAC;IAC5F,IAAI,SAAS;QACT,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS;QACjC,OAAO,IAAI,OAAO;QAClB,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS;YAAE;QAAI;IAC/C;IACA,OAAO;AACX;AACA,QAAQ,iBAAiB,GAAG;AAC5B;;CAEC,GACD,SAAS,OAAO,KAAK,EAAE,OAAO,CAAC;IAC3B,MAAM,SAAS,MAAM,MAAM;IAC3B,IAAI,QAAQ,QAAQ;QAChB,OAAO;IACX;IACA,OAAO,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,SAAS,IAAI,GAAG;AACpE;AACA,QAAQ,MAAM,GAAG;AACjB;;;CAGC,GACD,SAAS,QAAQ,KAAK;IAClB,IAAI,UAAU,MAAM,MAAM;IAC1B,wCAAwC;IACxC,MAAO,UAAU,EAAG;QAChB,sBAAsB;QACtB,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACzC,wBAAwB;QACxB;QACA,oCAAoC;QACpC,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG;YAAC,KAAK,CAAC,MAAM;YAAE,KAAK,CAAC,QAAQ;SAAC;IACnE;IACA,OAAO;AACX;AACA,QAAQ,OAAO,GAAG;AAClB;;CAEC,GACD,QAAQ,2BAA2B,GAAG;AACtC,SAAS,OAAO,IAAI,EAAE,MAAM;IACxB,MAAM,MAAM,IAAI;IAChB,KAAK,OAAO,CAAC,CAAC,KAAK;QACf,IAAI,GAAG,CAAC,KAAK,MAAM,CAAC,MAAM;IAC9B;IACA,OAAO;AACX;AACA,QAAQ,MAAM,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/Command.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst commands_1 = require(\"@ioredis/commands\");\nconst calculateSlot = require(\"cluster-key-slot\");\nconst standard_as_callback_1 = require(\"standard-as-callback\");\nconst utils_1 = require(\"./utils\");\n/**\n * Command instance\n *\n * It's rare that you need to create a Command instance yourself.\n *\n * ```js\n * var infoCommand = new Command('info', null, function (err, result) {\n *   console.log('result', result);\n * });\n *\n * redis.sendCommand(infoCommand);\n *\n * // When no callback provided, Command instance will have a `promise` property,\n * // which will resolve/reject with the result of the command.\n * var getCommand = new Command('get', ['foo']);\n * getCommand.promise.then(function (result) {\n *   console.log('result', result);\n * });\n * ```\n */\nclass Command {\n    /**\n     * Creates an instance of Command.\n     * @param name Command name\n     * @param args An array of command arguments\n     * @param options\n     * @param callback The callback that handles the response.\n     * If omit, the response will be handled via Promise\n     */\n    constructor(name, args = [], options = {}, callback) {\n        this.name = name;\n        this.inTransaction = false;\n        this.isResolved = false;\n        this.transformed = false;\n        this.replyEncoding = options.replyEncoding;\n        this.errorStack = options.errorStack;\n        this.args = args.flat();\n        this.callback = callback;\n        this.initPromise();\n        if (options.keyPrefix) {\n            // @ts-expect-error\n            const isBufferKeyPrefix = options.keyPrefix instanceof Buffer;\n            // @ts-expect-error\n            let keyPrefixBuffer = isBufferKeyPrefix\n                ? options.keyPrefix\n                : null;\n            this._iterateKeys((key) => {\n                if (key instanceof Buffer) {\n                    if (keyPrefixBuffer === null) {\n                        keyPrefixBuffer = Buffer.from(options.keyPrefix);\n                    }\n                    return Buffer.concat([keyPrefixBuffer, key]);\n                }\n                else if (isBufferKeyPrefix) {\n                    // @ts-expect-error\n                    return Buffer.concat([options.keyPrefix, Buffer.from(String(key))]);\n                }\n                return options.keyPrefix + key;\n            });\n        }\n        if (options.readOnly) {\n            this.isReadOnly = true;\n        }\n    }\n    /**\n     * Check whether the command has the flag\n     */\n    static checkFlag(flagName, commandName) {\n        return !!this.getFlagMap()[flagName][commandName];\n    }\n    static setArgumentTransformer(name, func) {\n        this._transformer.argument[name] = func;\n    }\n    static setReplyTransformer(name, func) {\n        this._transformer.reply[name] = func;\n    }\n    static getFlagMap() {\n        if (!this.flagMap) {\n            this.flagMap = Object.keys(Command.FLAGS).reduce((map, flagName) => {\n                map[flagName] = {};\n                Command.FLAGS[flagName].forEach((commandName) => {\n                    map[flagName][commandName] = true;\n                });\n                return map;\n            }, {});\n        }\n        return this.flagMap;\n    }\n    getSlot() {\n        if (typeof this.slot === \"undefined\") {\n            const key = this.getKeys()[0];\n            this.slot = key == null ? null : calculateSlot(key);\n        }\n        return this.slot;\n    }\n    getKeys() {\n        return this._iterateKeys();\n    }\n    /**\n     * Convert command to writable buffer or string\n     */\n    toWritable(_socket) {\n        let result;\n        const commandStr = \"*\" +\n            (this.args.length + 1) +\n            \"\\r\\n$\" +\n            Buffer.byteLength(this.name) +\n            \"\\r\\n\" +\n            this.name +\n            \"\\r\\n\";\n        if (this.bufferMode) {\n            const buffers = new MixedBuffers();\n            buffers.push(commandStr);\n            for (let i = 0; i < this.args.length; ++i) {\n                const arg = this.args[i];\n                if (arg instanceof Buffer) {\n                    if (arg.length === 0) {\n                        buffers.push(\"$0\\r\\n\\r\\n\");\n                    }\n                    else {\n                        buffers.push(\"$\" + arg.length + \"\\r\\n\");\n                        buffers.push(arg);\n                        buffers.push(\"\\r\\n\");\n                    }\n                }\n                else {\n                    buffers.push(\"$\" +\n                        Buffer.byteLength(arg) +\n                        \"\\r\\n\" +\n                        arg +\n                        \"\\r\\n\");\n                }\n            }\n            result = buffers.toBuffer();\n        }\n        else {\n            result = commandStr;\n            for (let i = 0; i < this.args.length; ++i) {\n                const arg = this.args[i];\n                result +=\n                    \"$\" +\n                        Buffer.byteLength(arg) +\n                        \"\\r\\n\" +\n                        arg +\n                        \"\\r\\n\";\n            }\n        }\n        return result;\n    }\n    stringifyArguments() {\n        for (let i = 0; i < this.args.length; ++i) {\n            const arg = this.args[i];\n            if (typeof arg === \"string\") {\n                // buffers and strings don't need any transformation\n            }\n            else if (arg instanceof Buffer) {\n                this.bufferMode = true;\n            }\n            else {\n                this.args[i] = (0, utils_1.toArg)(arg);\n            }\n        }\n    }\n    /**\n     * Convert buffer/buffer[] to string/string[],\n     * and apply reply transformer.\n     */\n    transformReply(result) {\n        if (this.replyEncoding) {\n            result = (0, utils_1.convertBufferToString)(result, this.replyEncoding);\n        }\n        const transformer = Command._transformer.reply[this.name];\n        if (transformer) {\n            result = transformer(result);\n        }\n        return result;\n    }\n    /**\n     * Set the wait time before terminating the attempt to execute a command\n     * and generating an error.\n     */\n    setTimeout(ms) {\n        if (!this._commandTimeoutTimer) {\n            this._commandTimeoutTimer = setTimeout(() => {\n                if (!this.isResolved) {\n                    this.reject(new Error(\"Command timed out\"));\n                }\n            }, ms);\n        }\n    }\n    initPromise() {\n        const promise = new Promise((resolve, reject) => {\n            if (!this.transformed) {\n                this.transformed = true;\n                const transformer = Command._transformer.argument[this.name];\n                if (transformer) {\n                    this.args = transformer(this.args);\n                }\n                this.stringifyArguments();\n            }\n            this.resolve = this._convertValue(resolve);\n            if (this.errorStack) {\n                this.reject = (err) => {\n                    reject((0, utils_1.optimizeErrorStack)(err, this.errorStack.stack, __dirname));\n                };\n            }\n            else {\n                this.reject = reject;\n            }\n        });\n        this.promise = (0, standard_as_callback_1.default)(promise, this.callback);\n    }\n    /**\n     * Iterate through the command arguments that are considered keys.\n     */\n    _iterateKeys(transform = (key) => key) {\n        if (typeof this.keys === \"undefined\") {\n            this.keys = [];\n            if ((0, commands_1.exists)(this.name)) {\n                // @ts-expect-error\n                const keyIndexes = (0, commands_1.getKeyIndexes)(this.name, this.args);\n                for (const index of keyIndexes) {\n                    this.args[index] = transform(this.args[index]);\n                    this.keys.push(this.args[index]);\n                }\n            }\n        }\n        return this.keys;\n    }\n    /**\n     * Convert the value from buffer to the target encoding.\n     */\n    _convertValue(resolve) {\n        return (value) => {\n            try {\n                const existingTimer = this._commandTimeoutTimer;\n                if (existingTimer) {\n                    clearTimeout(existingTimer);\n                    delete this._commandTimeoutTimer;\n                }\n                resolve(this.transformReply(value));\n                this.isResolved = true;\n            }\n            catch (err) {\n                this.reject(err);\n            }\n            return this.promise;\n        };\n    }\n}\nexports.default = Command;\nCommand.FLAGS = {\n    VALID_IN_SUBSCRIBER_MODE: [\n        \"subscribe\",\n        \"psubscribe\",\n        \"unsubscribe\",\n        \"punsubscribe\",\n        \"ssubscribe\",\n        \"sunsubscribe\",\n        \"ping\",\n        \"quit\",\n    ],\n    VALID_IN_MONITOR_MODE: [\"monitor\", \"auth\"],\n    ENTER_SUBSCRIBER_MODE: [\"subscribe\", \"psubscribe\", \"ssubscribe\"],\n    EXIT_SUBSCRIBER_MODE: [\"unsubscribe\", \"punsubscribe\", \"sunsubscribe\"],\n    WILL_DISCONNECT: [\"quit\"],\n};\nCommand._transformer = {\n    argument: {},\n    reply: {},\n};\nconst msetArgumentTransformer = function (args) {\n    if (args.length === 1) {\n        if (args[0] instanceof Map) {\n            return (0, utils_1.convertMapToArray)(args[0]);\n        }\n        if (typeof args[0] === \"object\" && args[0] !== null) {\n            return (0, utils_1.convertObjectToArray)(args[0]);\n        }\n    }\n    return args;\n};\nconst hsetArgumentTransformer = function (args) {\n    if (args.length === 2) {\n        if (args[1] instanceof Map) {\n            return [args[0]].concat((0, utils_1.convertMapToArray)(args[1]));\n        }\n        if (typeof args[1] === \"object\" && args[1] !== null) {\n            return [args[0]].concat((0, utils_1.convertObjectToArray)(args[1]));\n        }\n    }\n    return args;\n};\nCommand.setArgumentTransformer(\"mset\", msetArgumentTransformer);\nCommand.setArgumentTransformer(\"msetnx\", msetArgumentTransformer);\nCommand.setArgumentTransformer(\"hset\", hsetArgumentTransformer);\nCommand.setArgumentTransformer(\"hmset\", hsetArgumentTransformer);\nCommand.setReplyTransformer(\"hgetall\", function (result) {\n    if (Array.isArray(result)) {\n        const obj = {};\n        for (let i = 0; i < result.length; i += 2) {\n            const key = result[i];\n            const value = result[i + 1];\n            if (key in obj) {\n                // can only be truthy if the property is special somehow, like '__proto__' or 'constructor'\n                // https://github.com/luin/ioredis/issues/1267\n                Object.defineProperty(obj, key, {\n                    value,\n                    configurable: true,\n                    enumerable: true,\n                    writable: true,\n                });\n            }\n            else {\n                obj[key] = value;\n            }\n        }\n        return obj;\n    }\n    return result;\n});\nclass MixedBuffers {\n    constructor() {\n        this.length = 0;\n        this.items = [];\n    }\n    push(x) {\n        this.length += Buffer.byteLength(x);\n        this.items.push(x);\n    }\n    toBuffer() {\n        const result = Buffer.allocUnsafe(this.length);\n        let offset = 0;\n        for (const item of this.items) {\n            const length = Buffer.byteLength(item);\n            Buffer.isBuffer(item)\n                ? item.copy(result, offset)\n                : result.write(item, offset, length);\n            offset += length;\n        }\n        return result;\n    }\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN;;;;;;;;;;;;;;;;;;;CAmBC,GACD,MAAM;IACF;;;;;;;KAOC,GACD,YAAY,IAAI,EAAE,OAAO,EAAE,EAAE,UAAU,CAAC,CAAC,EAAE,QAAQ,CAAE;QACjD,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,aAAa,GAAG,QAAQ,aAAa;QAC1C,IAAI,CAAC,UAAU,GAAG,QAAQ,UAAU;QACpC,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QACrB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,WAAW;QAChB,IAAI,QAAQ,SAAS,EAAE;YACnB,mBAAmB;YACnB,MAAM,oBAAoB,QAAQ,SAAS,YAAY;YACvD,mBAAmB;YACnB,IAAI,kBAAkB,oBAChB,QAAQ,SAAS,GACjB;YACN,IAAI,CAAC,YAAY,CAAC,CAAC;gBACf,IAAI,eAAe,QAAQ;oBACvB,IAAI,oBAAoB,MAAM;wBAC1B,kBAAkB,OAAO,IAAI,CAAC,QAAQ,SAAS;oBACnD;oBACA,OAAO,OAAO,MAAM,CAAC;wBAAC;wBAAiB;qBAAI;gBAC/C,OACK,IAAI,mBAAmB;oBACxB,mBAAmB;oBACnB,OAAO,OAAO,MAAM,CAAC;wBAAC,QAAQ,SAAS;wBAAE,OAAO,IAAI,CAAC,OAAO;qBAAM;gBACtE;gBACA,OAAO,QAAQ,SAAS,GAAG;YAC/B;QACJ;QACA,IAAI,QAAQ,QAAQ,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG;QACtB;IACJ;IACA;;KAEC,GACD,OAAO,UAAU,QAAQ,EAAE,WAAW,EAAE;QACpC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,CAAC,YAAY;IACrD;IACA,OAAO,uBAAuB,IAAI,EAAE,IAAI,EAAE;QACtC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,GAAG;IACvC;IACA,OAAO,oBAAoB,IAAI,EAAE,IAAI,EAAE;QACnC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG;IACpC;IACA,OAAO,aAAa;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,CAAC,QAAQ,KAAK,EAAE,MAAM,CAAC,CAAC,KAAK;gBACnD,GAAG,CAAC,SAAS,GAAG,CAAC;gBACjB,QAAQ,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;oBAC7B,GAAG,CAAC,SAAS,CAAC,YAAY,GAAG;gBACjC;gBACA,OAAO;YACX,GAAG,CAAC;QACR;QACA,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,UAAU;QACN,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,aAAa;YAClC,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE;YAC7B,IAAI,CAAC,IAAI,GAAG,OAAO,OAAO,OAAO,cAAc;QACnD;QACA,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,UAAU;QACN,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA;;KAEC,GACD,WAAW,OAAO,EAAE;QAChB,IAAI;QACJ,MAAM,aAAa,MACf,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IACrB,UACA,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,IAC3B,SACA,IAAI,CAAC,IAAI,GACT;QACJ,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,MAAM,UAAU,IAAI;YACpB,QAAQ,IAAI,CAAC;YACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAG;gBACvC,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gBACxB,IAAI,eAAe,QAAQ;oBACvB,IAAI,IAAI,MAAM,KAAK,GAAG;wBAClB,QAAQ,IAAI,CAAC;oBACjB,OACK;wBACD,QAAQ,IAAI,CAAC,MAAM,IAAI,MAAM,GAAG;wBAChC,QAAQ,IAAI,CAAC;wBACb,QAAQ,IAAI,CAAC;oBACjB;gBACJ,OACK;oBACD,QAAQ,IAAI,CAAC,MACT,OAAO,UAAU,CAAC,OAClB,SACA,MACA;gBACR;YACJ;YACA,SAAS,QAAQ,QAAQ;QAC7B,OACK;YACD,SAAS;YACT,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAG;gBACvC,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gBACxB,UACI,MACI,OAAO,UAAU,CAAC,OAClB,SACA,MACA;YACZ;QACJ;QACA,OAAO;IACX;IACA,qBAAqB;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAG;YACvC,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;YACxB,IAAI,OAAO,QAAQ,UAAU;YACzB,oDAAoD;YACxD,OACK,IAAI,eAAe,QAAQ;gBAC5B,IAAI,CAAC,UAAU,GAAG;YACtB,OACK;gBACD,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,QAAQ,KAAK,EAAE;YACtC;QACJ;IACJ;IACA;;;KAGC,GACD,eAAe,MAAM,EAAE;QACnB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,SAAS,CAAC,GAAG,QAAQ,qBAAqB,EAAE,QAAQ,IAAI,CAAC,aAAa;QAC1E;QACA,MAAM,cAAc,QAAQ,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;QACzD,IAAI,aAAa;YACb,SAAS,YAAY;QACzB;QACA,OAAO;IACX;IACA;;;KAGC,GACD,WAAW,EAAE,EAAE;QACX,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,IAAI,CAAC,oBAAoB,GAAG,WAAW;gBACnC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBAClB,IAAI,CAAC,MAAM,CAAC,IAAI,MAAM;gBAC1B;YACJ,GAAG;QACP;IACJ;IACA,cAAc;QACV,MAAM,UAAU,IAAI,QAAQ,CAAC,SAAS;YAClC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACnB,IAAI,CAAC,WAAW,GAAG;gBACnB,MAAM,cAAc,QAAQ,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC5D,IAAI,aAAa;oBACb,IAAI,CAAC,IAAI,GAAG,YAAY,IAAI,CAAC,IAAI;gBACrC;gBACA,IAAI,CAAC,kBAAkB;YAC3B;YACA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;YAClC,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,MAAM,GAAG,CAAC;oBACX,OAAO,CAAC,GAAG,QAAQ,kBAAkB,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK;gBACrE;YACJ,OACK;gBACD,IAAI,CAAC,MAAM,GAAG;YAClB;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,uBAAuB,OAAO,EAAE,SAAS,IAAI,CAAC,QAAQ;IAC7E;IACA;;KAEC,GACD,aAAa,YAAY,CAAC,MAAQ,GAAG,EAAE;QACnC,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,aAAa;YAClC,IAAI,CAAC,IAAI,GAAG,EAAE;YACd,IAAI,CAAC,GAAG,WAAW,MAAM,EAAE,IAAI,CAAC,IAAI,GAAG;gBACnC,mBAAmB;gBACnB,MAAM,aAAa,CAAC,GAAG,WAAW,aAAa,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI;gBACrE,KAAK,MAAM,SAAS,WAAY;oBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM;oBAC7C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;gBACnC;YACJ;QACJ;QACA,OAAO,IAAI,CAAC,IAAI;IACpB;IACA;;KAEC,GACD,cAAc,OAAO,EAAE;QACnB,OAAO,CAAC;YACJ,IAAI;gBACA,MAAM,gBAAgB,IAAI,CAAC,oBAAoB;gBAC/C,IAAI,eAAe;oBACf,aAAa;oBACb,OAAO,IAAI,CAAC,oBAAoB;gBACpC;gBACA,QAAQ,IAAI,CAAC,cAAc,CAAC;gBAC5B,IAAI,CAAC,UAAU,GAAG;YACtB,EACA,OAAO,KAAK;gBACR,IAAI,CAAC,MAAM,CAAC;YAChB;YACA,OAAO,IAAI,CAAC,OAAO;QACvB;IACJ;AACJ;AACA,QAAQ,OAAO,GAAG;AAClB,QAAQ,KAAK,GAAG;IACZ,0BAA0B;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,uBAAuB;QAAC;QAAW;KAAO;IAC1C,uBAAuB;QAAC;QAAa;QAAc;KAAa;IAChE,sBAAsB;QAAC;QAAe;QAAgB;KAAe;IACrE,iBAAiB;QAAC;KAAO;AAC7B;AACA,QAAQ,YAAY,GAAG;IACnB,UAAU,CAAC;IACX,OAAO,CAAC;AACZ;AACA,MAAM,0BAA0B,SAAU,IAAI;IAC1C,IAAI,KAAK,MAAM,KAAK,GAAG;QACnB,IAAI,IAAI,CAAC,EAAE,YAAY,KAAK;YACxB,OAAO,CAAC,GAAG,QAAQ,iBAAiB,EAAE,IAAI,CAAC,EAAE;QACjD;QACA,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,YAAY,IAAI,CAAC,EAAE,KAAK,MAAM;YACjD,OAAO,CAAC,GAAG,QAAQ,oBAAoB,EAAE,IAAI,CAAC,EAAE;QACpD;IACJ;IACA,OAAO;AACX;AACA,MAAM,0BAA0B,SAAU,IAAI;IAC1C,IAAI,KAAK,MAAM,KAAK,GAAG;QACnB,IAAI,IAAI,CAAC,EAAE,YAAY,KAAK;YACxB,OAAO;gBAAC,IAAI,CAAC,EAAE;aAAC,CAAC,MAAM,CAAC,CAAC,GAAG,QAAQ,iBAAiB,EAAE,IAAI,CAAC,EAAE;QAClE;QACA,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,YAAY,IAAI,CAAC,EAAE,KAAK,MAAM;YACjD,OAAO;gBAAC,IAAI,CAAC,EAAE;aAAC,CAAC,MAAM,CAAC,CAAC,GAAG,QAAQ,oBAAoB,EAAE,IAAI,CAAC,EAAE;QACrE;IACJ;IACA,OAAO;AACX;AACA,QAAQ,sBAAsB,CAAC,QAAQ;AACvC,QAAQ,sBAAsB,CAAC,UAAU;AACzC,QAAQ,sBAAsB,CAAC,QAAQ;AACvC,QAAQ,sBAAsB,CAAC,SAAS;AACxC,QAAQ,mBAAmB,CAAC,WAAW,SAAU,MAAM;IACnD,IAAI,MAAM,OAAO,CAAC,SAAS;QACvB,MAAM,MAAM,CAAC;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;YACvC,MAAM,MAAM,MAAM,CAAC,EAAE;YACrB,MAAM,QAAQ,MAAM,CAAC,IAAI,EAAE;YAC3B,IAAI,OAAO,KAAK;gBACZ,2FAA2F;gBAC3F,8CAA8C;gBAC9C,OAAO,cAAc,CAAC,KAAK,KAAK;oBAC5B;oBACA,cAAc;oBACd,YAAY;oBACZ,UAAU;gBACd;YACJ,OACK;gBACD,GAAG,CAAC,IAAI,GAAG;YACf;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;AACA,MAAM;IACF,aAAc;QACV,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG,EAAE;IACnB;IACA,KAAK,CAAC,EAAE;QACJ,IAAI,CAAC,MAAM,IAAI,OAAO,UAAU,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IACpB;IACA,WAAW;QACP,MAAM,SAAS,OAAO,WAAW,CAAC,IAAI,CAAC,MAAM;QAC7C,IAAI,SAAS;QACb,KAAK,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAE;YAC3B,MAAM,SAAS,OAAO,UAAU,CAAC;YACjC,OAAO,QAAQ,CAAC,QACV,KAAK,IAAI,CAAC,QAAQ,UAClB,OAAO,KAAK,CAAC,MAAM,QAAQ;YACjC,UAAU;QACd;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/errors/ClusterAllFailedError.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst redis_errors_1 = require(\"redis-errors\");\nclass ClusterAllFailedError extends redis_errors_1.RedisError {\n    constructor(message, lastNodeError) {\n        super(message);\n        this.lastNodeError = lastNodeError;\n        Error.captureStackTrace(this, this.constructor);\n    }\n    get name() {\n        return this.constructor.name;\n    }\n}\nexports.default = ClusterAllFailedError;\nClusterAllFailedError.defaultMessage = \"Failed to refresh slots cache.\";\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM,8BAA8B,eAAe,UAAU;IACzD,YAAY,OAAO,EAAE,aAAa,CAAE;QAChC,KAAK,CAAC;QACN,IAAI,CAAC,aAAa,GAAG;QACrB,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;IAClD;IACA,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;IAChC;AACJ;AACA,QAAQ,OAAO,GAAG;AAClB,sBAAsB,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/ScanStream.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst stream_1 = require(\"stream\");\n/**\n * Convenient class to convert the process of scanning keys to a readable stream.\n */\nclass ScanStream extends stream_1.Readable {\n    constructor(opt) {\n        super(opt);\n        this.opt = opt;\n        this._redisCursor = \"0\";\n        this._redisDrained = false;\n    }\n    _read() {\n        if (this._redisDrained) {\n            this.push(null);\n            return;\n        }\n        const args = [this._redisCursor];\n        if (this.opt.key) {\n            args.unshift(this.opt.key);\n        }\n        if (this.opt.match) {\n            args.push(\"MATCH\", this.opt.match);\n        }\n        if (this.opt.type) {\n            args.push(\"TYPE\", this.opt.type);\n        }\n        if (this.opt.count) {\n            args.push(\"COUNT\", String(this.opt.count));\n        }\n        if (this.opt.noValues) {\n            args.push(\"NOVALUES\");\n        }\n        this.opt.redis[this.opt.command](args, (err, res) => {\n            if (err) {\n                this.emit(\"error\", err);\n                return;\n            }\n            this._redisCursor = res[0] instanceof Buffer ? res[0].toString() : res[0];\n            if (this._redisCursor === \"0\") {\n                this._redisDrained = true;\n            }\n            this.push(res[1]);\n        });\n    }\n    close() {\n        this._redisDrained = true;\n    }\n}\nexports.default = ScanStream;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN;;CAEC,GACD,MAAM,mBAAmB,SAAS,QAAQ;IACtC,YAAY,GAAG,CAAE;QACb,KAAK,CAAC;QACN,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,aAAa,GAAG;IACzB;IACA,QAAQ;QACJ,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC;YACV;QACJ;QACA,MAAM,OAAO;YAAC,IAAI,CAAC,YAAY;SAAC;QAChC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;YACd,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG;QAC7B;QACA,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;YAChB,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,GAAG,CAAC,KAAK;QACrC;QACA,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;YACf,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI;QACnC;QACA,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;YAChB,KAAK,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK;QAC5C;QACA,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE;YACnB,KAAK,IAAI,CAAC;QACd;QACA,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK;YACzC,IAAI,KAAK;gBACL,IAAI,CAAC,IAAI,CAAC,SAAS;gBACnB;YACJ;YACA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,EAAE,YAAY,SAAS,GAAG,CAAC,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE;YACzE,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK;gBAC3B,IAAI,CAAC,aAAa,GAAG;YACzB;YACA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QACpB;IACJ;IACA,QAAQ;QACJ,IAAI,CAAC,aAAa,GAAG;IACzB;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1010, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/autoPipelining.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.executeWithAutoPipelining = exports.getFirstValueInFlattenedArray = exports.shouldUseAutoPipelining = exports.notAllowedAutoPipelineCommands = exports.kCallbacks = exports.kExec = void 0;\nconst lodash_1 = require(\"./utils/lodash\");\nconst calculateSlot = require(\"cluster-key-slot\");\nconst standard_as_callback_1 = require(\"standard-as-callback\");\nexports.kExec = Symbol(\"exec\");\nexports.kCallbacks = Symbol(\"callbacks\");\nexports.notAllowedAutoPipelineCommands = [\n    \"auth\",\n    \"info\",\n    \"script\",\n    \"quit\",\n    \"cluster\",\n    \"pipeline\",\n    \"multi\",\n    \"subscribe\",\n    \"psubscribe\",\n    \"unsubscribe\",\n    \"unpsubscribe\",\n    \"select\",\n];\nfunction executeAutoPipeline(client, slotKey) {\n    /*\n      If a pipeline is already executing, keep queueing up commands\n      since ioredis won't serve two pipelines at the same time\n    */\n    if (client._runningAutoPipelines.has(slotKey)) {\n        return;\n    }\n    if (!client._autoPipelines.has(slotKey)) {\n        /*\n          Rare edge case. Somehow, something has deleted this running autopipeline in an immediate\n          call to executeAutoPipeline.\n         \n          Maybe the callback in the pipeline.exec is sometimes called in the same tick,\n          e.g. if redis is disconnected?\n        */\n        return;\n    }\n    client._runningAutoPipelines.add(slotKey);\n    // Get the pipeline and immediately delete it so that new commands are queued on a new pipeline\n    const pipeline = client._autoPipelines.get(slotKey);\n    client._autoPipelines.delete(slotKey);\n    const callbacks = pipeline[exports.kCallbacks];\n    // Stop keeping a reference to callbacks immediately after the callbacks stop being used.\n    // This allows the GC to reclaim objects referenced by callbacks, especially with 16384 slots\n    // in Redis.Cluster\n    pipeline[exports.kCallbacks] = null;\n    // Perform the call\n    pipeline.exec(function (err, results) {\n        client._runningAutoPipelines.delete(slotKey);\n        /*\n          Invoke all callback in nextTick so the stack is cleared\n          and callbacks can throw errors without affecting other callbacks.\n        */\n        if (err) {\n            for (let i = 0; i < callbacks.length; i++) {\n                process.nextTick(callbacks[i], err);\n            }\n        }\n        else {\n            for (let i = 0; i < callbacks.length; i++) {\n                process.nextTick(callbacks[i], ...results[i]);\n            }\n        }\n        // If there is another pipeline on the same node, immediately execute it without waiting for nextTick\n        if (client._autoPipelines.has(slotKey)) {\n            executeAutoPipeline(client, slotKey);\n        }\n    });\n}\nfunction shouldUseAutoPipelining(client, functionName, commandName) {\n    return (functionName &&\n        client.options.enableAutoPipelining &&\n        !client.isPipeline &&\n        !exports.notAllowedAutoPipelineCommands.includes(commandName) &&\n        !client.options.autoPipeliningIgnoredCommands.includes(commandName));\n}\nexports.shouldUseAutoPipelining = shouldUseAutoPipelining;\nfunction getFirstValueInFlattenedArray(args) {\n    for (let i = 0; i < args.length; i++) {\n        const arg = args[i];\n        if (typeof arg === \"string\") {\n            return arg;\n        }\n        else if (Array.isArray(arg) || (0, lodash_1.isArguments)(arg)) {\n            if (arg.length === 0) {\n                continue;\n            }\n            return arg[0];\n        }\n        const flattened = [arg].flat();\n        if (flattened.length > 0) {\n            return flattened[0];\n        }\n    }\n    return undefined;\n}\nexports.getFirstValueInFlattenedArray = getFirstValueInFlattenedArray;\nfunction executeWithAutoPipelining(client, functionName, commandName, args, callback) {\n    // On cluster mode let's wait for slots to be available\n    if (client.isCluster && !client.slots.length) {\n        if (client.status === \"wait\")\n            client.connect().catch(lodash_1.noop);\n        return (0, standard_as_callback_1.default)(new Promise(function (resolve, reject) {\n            client.delayUntilReady((err) => {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                executeWithAutoPipelining(client, functionName, commandName, args, null).then(resolve, reject);\n            });\n        }), callback);\n    }\n    // If we have slot information, we can improve routing by grouping slots served by the same subset of nodes\n    // Note that the first value in args may be a (possibly empty) array.\n    // ioredis will only flatten one level of the array, in the Command constructor.\n    const prefix = client.options.keyPrefix || \"\";\n    const slotKey = client.isCluster\n        ? client.slots[calculateSlot(`${prefix}${getFirstValueInFlattenedArray(args)}`)].join(\",\")\n        : \"main\";\n    if (!client._autoPipelines.has(slotKey)) {\n        const pipeline = client.pipeline();\n        pipeline[exports.kExec] = false;\n        pipeline[exports.kCallbacks] = [];\n        client._autoPipelines.set(slotKey, pipeline);\n    }\n    const pipeline = client._autoPipelines.get(slotKey);\n    /*\n      Mark the pipeline as scheduled.\n      The symbol will make sure that the pipeline is only scheduled once per tick.\n      New commands are appended to an already scheduled pipeline.\n    */\n    if (!pipeline[exports.kExec]) {\n        pipeline[exports.kExec] = true;\n        /*\n          Deferring with setImmediate so we have a chance to capture multiple\n          commands that can be scheduled by I/O events already in the event loop queue.\n        */\n        setImmediate(executeAutoPipeline, client, slotKey);\n    }\n    // Create the promise which will execute the command in the pipeline.\n    const autoPipelinePromise = new Promise(function (resolve, reject) {\n        pipeline[exports.kCallbacks].push(function (err, value) {\n            if (err) {\n                reject(err);\n                return;\n            }\n            resolve(value);\n        });\n        if (functionName === \"call\") {\n            args.unshift(commandName);\n        }\n        pipeline[functionName](...args);\n    });\n    return (0, standard_as_callback_1.default)(autoPipelinePromise, callback);\n}\nexports.executeWithAutoPipelining = executeWithAutoPipelining;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,yBAAyB,GAAG,QAAQ,6BAA6B,GAAG,QAAQ,uBAAuB,GAAG,QAAQ,8BAA8B,GAAG,QAAQ,UAAU,GAAG,QAAQ,KAAK,GAAG,KAAK;AACjM,MAAM;AACN,MAAM;AACN,MAAM;AACN,QAAQ,KAAK,GAAG,OAAO;AACvB,QAAQ,UAAU,GAAG,OAAO;AAC5B,QAAQ,8BAA8B,GAAG;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,SAAS,oBAAoB,MAAM,EAAE,OAAO;IACxC;;;IAGA,GACA,IAAI,OAAO,qBAAqB,CAAC,GAAG,CAAC,UAAU;QAC3C;IACJ;IACA,IAAI,CAAC,OAAO,cAAc,CAAC,GAAG,CAAC,UAAU;QACrC;;;;;;QAMA,GACA;IACJ;IACA,OAAO,qBAAqB,CAAC,GAAG,CAAC;IACjC,+FAA+F;IAC/F,MAAM,WAAW,OAAO,cAAc,CAAC,GAAG,CAAC;IAC3C,OAAO,cAAc,CAAC,MAAM,CAAC;IAC7B,MAAM,YAAY,QAAQ,CAAC,QAAQ,UAAU,CAAC;IAC9C,yFAAyF;IACzF,6FAA6F;IAC7F,mBAAmB;IACnB,QAAQ,CAAC,QAAQ,UAAU,CAAC,GAAG;IAC/B,mBAAmB;IACnB,SAAS,IAAI,CAAC,SAAU,GAAG,EAAE,OAAO;QAChC,OAAO,qBAAqB,CAAC,MAAM,CAAC;QACpC;;;QAGA,GACA,IAAI,KAAK;YACL,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACvC,QAAQ,QAAQ,CAAC,SAAS,CAAC,EAAE,EAAE;YACnC;QACJ,OACK;YACD,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACvC,QAAQ,QAAQ,CAAC,SAAS,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE;YAChD;QACJ;QACA,qGAAqG;QACrG,IAAI,OAAO,cAAc,CAAC,GAAG,CAAC,UAAU;YACpC,oBAAoB,QAAQ;QAChC;IACJ;AACJ;AACA,SAAS,wBAAwB,MAAM,EAAE,YAAY,EAAE,WAAW;IAC9D,OAAQ,gBACJ,OAAO,OAAO,CAAC,oBAAoB,IACnC,CAAC,OAAO,UAAU,IAClB,CAAC,QAAQ,8BAA8B,CAAC,QAAQ,CAAC,gBACjD,CAAC,OAAO,OAAO,CAAC,6BAA6B,CAAC,QAAQ,CAAC;AAC/D;AACA,QAAQ,uBAAuB,GAAG;AAClC,SAAS,8BAA8B,IAAI;IACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QAClC,MAAM,MAAM,IAAI,CAAC,EAAE;QACnB,IAAI,OAAO,QAAQ,UAAU;YACzB,OAAO;QACX,OACK,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,GAAG,SAAS,WAAW,EAAE,MAAM;YAC3D,IAAI,IAAI,MAAM,KAAK,GAAG;gBAClB;YACJ;YACA,OAAO,GAAG,CAAC,EAAE;QACjB;QACA,MAAM,YAAY;YAAC;SAAI,CAAC,IAAI;QAC5B,IAAI,UAAU,MAAM,GAAG,GAAG;YACtB,OAAO,SAAS,CAAC,EAAE;QACvB;IACJ;IACA,OAAO;AACX;AACA,QAAQ,6BAA6B,GAAG;AACxC,SAAS,0BAA0B,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ;IAChF,uDAAuD;IACvD,IAAI,OAAO,SAAS,IAAI,CAAC,OAAO,KAAK,CAAC,MAAM,EAAE;QAC1C,IAAI,OAAO,MAAM,KAAK,QAClB,OAAO,OAAO,GAAG,KAAK,CAAC,SAAS,IAAI;QACxC,OAAO,CAAC,GAAG,uBAAuB,OAAO,EAAE,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YAC5E,OAAO,eAAe,CAAC,CAAC;gBACpB,IAAI,KAAK;oBACL,OAAO;oBACP;gBACJ;gBACA,0BAA0B,QAAQ,cAAc,aAAa,MAAM,MAAM,IAAI,CAAC,SAAS;YAC3F;QACJ,IAAI;IACR;IACA,2GAA2G;IAC3G,qEAAqE;IACrE,gFAAgF;IAChF,MAAM,SAAS,OAAO,OAAO,CAAC,SAAS,IAAI;IAC3C,MAAM,UAAU,OAAO,SAAS,GAC1B,OAAO,KAAK,CAAC,cAAc,GAAG,SAAS,8BAA8B,OAAO,EAAE,CAAC,IAAI,CAAC,OACpF;IACN,IAAI,CAAC,OAAO,cAAc,CAAC,GAAG,CAAC,UAAU;QACrC,MAAM,WAAW,OAAO,QAAQ;QAChC,QAAQ,CAAC,QAAQ,KAAK,CAAC,GAAG;QAC1B,QAAQ,CAAC,QAAQ,UAAU,CAAC,GAAG,EAAE;QACjC,OAAO,cAAc,CAAC,GAAG,CAAC,SAAS;IACvC;IACA,MAAM,WAAW,OAAO,cAAc,CAAC,GAAG,CAAC;IAC3C;;;;IAIA,GACA,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,CAAC,EAAE;QAC1B,QAAQ,CAAC,QAAQ,KAAK,CAAC,GAAG;QAC1B;;;QAGA,GACA,aAAa,qBAAqB,QAAQ;IAC9C;IACA,qEAAqE;IACrE,MAAM,sBAAsB,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;QAC7D,QAAQ,CAAC,QAAQ,UAAU,CAAC,CAAC,IAAI,CAAC,SAAU,GAAG,EAAE,KAAK;YAClD,IAAI,KAAK;gBACL,OAAO;gBACP;YACJ;YACA,QAAQ;QACZ;QACA,IAAI,iBAAiB,QAAQ;YACzB,KAAK,OAAO,CAAC;QACjB;QACA,QAAQ,CAAC,aAAa,IAAI;IAC9B;IACA,OAAO,CAAC,GAAG,uBAAuB,OAAO,EAAE,qBAAqB;AACpE;AACA,QAAQ,yBAAyB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/Script.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst crypto_1 = require(\"crypto\");\nconst Command_1 = require(\"./Command\");\nconst standard_as_callback_1 = require(\"standard-as-callback\");\nclass Script {\n    constructor(lua, numberOfKeys = null, keyPrefix = \"\", readOnly = false) {\n        this.lua = lua;\n        this.numberOfKeys = numberOfKeys;\n        this.keyPrefix = keyPrefix;\n        this.readOnly = readOnly;\n        this.sha = (0, crypto_1.createHash)(\"sha1\").update(lua).digest(\"hex\");\n        const sha = this.sha;\n        const socketHasScriptLoaded = new WeakSet();\n        this.Command = class CustomScriptCommand extends Command_1.default {\n            toWritable(socket) {\n                const origReject = this.reject;\n                this.reject = (err) => {\n                    if (err.message.indexOf(\"NOSCRIPT\") !== -1) {\n                        socketHasScriptLoaded.delete(socket);\n                    }\n                    origReject.call(this, err);\n                };\n                if (!socketHasScriptLoaded.has(socket)) {\n                    socketHasScriptLoaded.add(socket);\n                    this.name = \"eval\";\n                    this.args[0] = lua;\n                }\n                else if (this.name === \"eval\") {\n                    this.name = \"evalsha\";\n                    this.args[0] = sha;\n                }\n                return super.toWritable(socket);\n            }\n        };\n    }\n    execute(container, args, options, callback) {\n        if (typeof this.numberOfKeys === \"number\") {\n            args.unshift(this.numberOfKeys);\n        }\n        if (this.keyPrefix) {\n            options.keyPrefix = this.keyPrefix;\n        }\n        if (this.readOnly) {\n            options.readOnly = true;\n        }\n        const evalsha = new this.Command(\"evalsha\", [this.sha, ...args], options);\n        evalsha.promise = evalsha.promise.catch((err) => {\n            if (err.message.indexOf(\"NOSCRIPT\") === -1) {\n                throw err;\n            }\n            // Resend the same custom evalsha command that gets transformed\n            // to an eval in case it's not loaded yet on the connection.\n            const resend = new this.Command(\"evalsha\", [this.sha, ...args], options);\n            const client = container.isPipeline ? container.redis : container;\n            return client.sendCommand(resend);\n        });\n        (0, standard_as_callback_1.default)(evalsha.promise, callback);\n        return container.sendCommand(evalsha);\n    }\n}\nexports.default = Script;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;IACF,YAAY,GAAG,EAAE,eAAe,IAAI,EAAE,YAAY,EAAE,EAAE,WAAW,KAAK,CAAE;QACpE,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS,UAAU,EAAE,QAAQ,MAAM,CAAC,KAAK,MAAM,CAAC;QAC/D,MAAM,MAAM,IAAI,CAAC,GAAG;QACpB,MAAM,wBAAwB,IAAI;QAClC,IAAI,CAAC,OAAO,GAAG,MAAM,4BAA4B,UAAU,OAAO;YAC9D,WAAW,MAAM,EAAE;gBACf,MAAM,aAAa,IAAI,CAAC,MAAM;gBAC9B,IAAI,CAAC,MAAM,GAAG,CAAC;oBACX,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG;wBACxC,sBAAsB,MAAM,CAAC;oBACjC;oBACA,WAAW,IAAI,CAAC,IAAI,EAAE;gBAC1B;gBACA,IAAI,CAAC,sBAAsB,GAAG,CAAC,SAAS;oBACpC,sBAAsB,GAAG,CAAC;oBAC1B,IAAI,CAAC,IAAI,GAAG;oBACZ,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;gBACnB,OACK,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ;oBAC3B,IAAI,CAAC,IAAI,GAAG;oBACZ,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG;gBACnB;gBACA,OAAO,KAAK,CAAC,WAAW;YAC5B;QACJ;IACJ;IACA,QAAQ,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE;QACxC,IAAI,OAAO,IAAI,CAAC,YAAY,KAAK,UAAU;YACvC,KAAK,OAAO,CAAC,IAAI,CAAC,YAAY;QAClC;QACA,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,QAAQ,SAAS,GAAG,IAAI,CAAC,SAAS;QACtC;QACA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,QAAQ,QAAQ,GAAG;QACvB;QACA,MAAM,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW;YAAC,IAAI,CAAC,GAAG;eAAK;SAAK,EAAE;QACjE,QAAQ,OAAO,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG;gBACxC,MAAM;YACV;YACA,+DAA+D;YAC/D,4DAA4D;YAC5D,MAAM,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW;gBAAC,IAAI,CAAC,GAAG;mBAAK;aAAK,EAAE;YAChE,MAAM,SAAS,UAAU,UAAU,GAAG,UAAU,KAAK,GAAG;YACxD,OAAO,OAAO,WAAW,CAAC;QAC9B;QACA,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,OAAO,EAAE;QACrD,OAAO,UAAU,WAAW,CAAC;IACjC;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/utils/Commander.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst commands_1 = require(\"@ioredis/commands\");\nconst autoPipelining_1 = require(\"../autoPipelining\");\nconst Command_1 = require(\"../Command\");\nconst Script_1 = require(\"../Script\");\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nclass Commander {\n    constructor() {\n        this.options = {};\n        /**\n         * @ignore\n         */\n        this.scriptsSet = {};\n        /**\n         * @ignore\n         */\n        this.addedBuiltinSet = new Set();\n    }\n    /**\n     * Return supported builtin commands\n     */\n    getBuiltinCommands() {\n        return commands.slice(0);\n    }\n    /**\n     * Create a builtin command\n     */\n    createBuiltinCommand(commandName) {\n        return {\n            string: generateFunction(null, commandName, \"utf8\"),\n            buffer: generateFunction(null, commandName, null),\n        };\n    }\n    /**\n     * Create add builtin command\n     */\n    addBuiltinCommand(commandName) {\n        this.addedBuiltinSet.add(commandName);\n        this[commandName] = generateFunction(commandName, commandName, \"utf8\");\n        this[commandName + \"Buffer\"] = generateFunction(commandName + \"Buffer\", commandName, null);\n    }\n    /**\n     * Define a custom command using lua script\n     */\n    defineCommand(name, definition) {\n        const script = new Script_1.default(definition.lua, definition.numberOfKeys, this.options.keyPrefix, definition.readOnly);\n        this.scriptsSet[name] = script;\n        this[name] = generateScriptingFunction(name, name, script, \"utf8\");\n        this[name + \"Buffer\"] = generateScriptingFunction(name + \"Buffer\", name, script, null);\n    }\n    /**\n     * @ignore\n     */\n    sendCommand(command, stream, node) {\n        throw new Error('\"sendCommand\" is not implemented');\n    }\n}\nconst commands = commands_1.list.filter((command) => command !== \"monitor\");\ncommands.push(\"sentinel\");\ncommands.forEach(function (commandName) {\n    Commander.prototype[commandName] = generateFunction(commandName, commandName, \"utf8\");\n    Commander.prototype[commandName + \"Buffer\"] = generateFunction(commandName + \"Buffer\", commandName, null);\n});\nCommander.prototype.call = generateFunction(\"call\", \"utf8\");\nCommander.prototype.callBuffer = generateFunction(\"callBuffer\", null);\n// @ts-expect-error\nCommander.prototype.send_command = Commander.prototype.call;\nfunction generateFunction(functionName, _commandName, _encoding) {\n    if (typeof _encoding === \"undefined\") {\n        _encoding = _commandName;\n        _commandName = null;\n    }\n    return function (...args) {\n        const commandName = (_commandName || args.shift());\n        let callback = args[args.length - 1];\n        if (typeof callback === \"function\") {\n            args.pop();\n        }\n        else {\n            callback = undefined;\n        }\n        const options = {\n            errorStack: this.options.showFriendlyErrorStack ? new Error() : undefined,\n            keyPrefix: this.options.keyPrefix,\n            replyEncoding: _encoding,\n        };\n        // No auto pipeline, use regular command sending\n        if (!(0, autoPipelining_1.shouldUseAutoPipelining)(this, functionName, commandName)) {\n            return this.sendCommand(\n            // @ts-expect-error\n            new Command_1.default(commandName, args, options, callback));\n        }\n        // Create a new pipeline and make sure it's scheduled\n        return (0, autoPipelining_1.executeWithAutoPipelining)(this, functionName, commandName, \n        // @ts-expect-error\n        args, callback);\n    };\n}\nfunction generateScriptingFunction(functionName, commandName, script, encoding) {\n    return function (...args) {\n        const callback = typeof args[args.length - 1] === \"function\" ? args.pop() : undefined;\n        const options = {\n            replyEncoding: encoding,\n        };\n        if (this.options.showFriendlyErrorStack) {\n            options.errorStack = new Error();\n        }\n        // No auto pipeline, use regular command sending\n        if (!(0, autoPipelining_1.shouldUseAutoPipelining)(this, functionName, commandName)) {\n            return script.execute(this, args, options, callback);\n        }\n        // Create a new pipeline and make sure it's scheduled\n        return (0, autoPipelining_1.executeWithAutoPipelining)(this, functionName, commandName, args, callback);\n    };\n}\nexports.default = Commander;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,6DAA6D;AAC7D,MAAM;IACF,aAAc;QACV,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB;;SAEC,GACD,IAAI,CAAC,UAAU,GAAG,CAAC;QACnB;;SAEC,GACD,IAAI,CAAC,eAAe,GAAG,IAAI;IAC/B;IACA;;KAEC,GACD,qBAAqB;QACjB,OAAO,SAAS,KAAK,CAAC;IAC1B;IACA;;KAEC,GACD,qBAAqB,WAAW,EAAE;QAC9B,OAAO;YACH,QAAQ,iBAAiB,MAAM,aAAa;YAC5C,QAAQ,iBAAiB,MAAM,aAAa;QAChD;IACJ;IACA;;KAEC,GACD,kBAAkB,WAAW,EAAE;QAC3B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,iBAAiB,aAAa,aAAa;QAC/D,IAAI,CAAC,cAAc,SAAS,GAAG,iBAAiB,cAAc,UAAU,aAAa;IACzF;IACA;;KAEC,GACD,cAAc,IAAI,EAAE,UAAU,EAAE;QAC5B,MAAM,SAAS,IAAI,SAAS,OAAO,CAAC,WAAW,GAAG,EAAE,WAAW,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,QAAQ;QACxH,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG;QACxB,IAAI,CAAC,KAAK,GAAG,0BAA0B,MAAM,MAAM,QAAQ;QAC3D,IAAI,CAAC,OAAO,SAAS,GAAG,0BAA0B,OAAO,UAAU,MAAM,QAAQ;IACrF;IACA;;KAEC,GACD,YAAY,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE;QAC/B,MAAM,IAAI,MAAM;IACpB;AACJ;AACA,MAAM,WAAW,WAAW,IAAI,CAAC,MAAM,CAAC,CAAC,UAAY,YAAY;AACjE,SAAS,IAAI,CAAC;AACd,SAAS,OAAO,CAAC,SAAU,WAAW;IAClC,UAAU,SAAS,CAAC,YAAY,GAAG,iBAAiB,aAAa,aAAa;IAC9E,UAAU,SAAS,CAAC,cAAc,SAAS,GAAG,iBAAiB,cAAc,UAAU,aAAa;AACxG;AACA,UAAU,SAAS,CAAC,IAAI,GAAG,iBAAiB,QAAQ;AACpD,UAAU,SAAS,CAAC,UAAU,GAAG,iBAAiB,cAAc;AAChE,mBAAmB;AACnB,UAAU,SAAS,CAAC,YAAY,GAAG,UAAU,SAAS,CAAC,IAAI;AAC3D,SAAS,iBAAiB,YAAY,EAAE,YAAY,EAAE,SAAS;IAC3D,IAAI,OAAO,cAAc,aAAa;QAClC,YAAY;QACZ,eAAe;IACnB;IACA,OAAO,SAAU,GAAG,IAAI;QACpB,MAAM,cAAe,gBAAgB,KAAK,KAAK;QAC/C,IAAI,WAAW,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACpC,IAAI,OAAO,aAAa,YAAY;YAChC,KAAK,GAAG;QACZ,OACK;YACD,WAAW;QACf;QACA,MAAM,UAAU;YACZ,YAAY,IAAI,CAAC,OAAO,CAAC,sBAAsB,GAAG,IAAI,UAAU;YAChE,WAAW,IAAI,CAAC,OAAO,CAAC,SAAS;YACjC,eAAe;QACnB;QACA,gDAAgD;QAChD,IAAI,CAAC,CAAC,GAAG,iBAAiB,uBAAuB,EAAE,IAAI,EAAE,cAAc,cAAc;YACjF,OAAO,IAAI,CAAC,WAAW,CACvB,mBAAmB;YACnB,IAAI,UAAU,OAAO,CAAC,aAAa,MAAM,SAAS;QACtD;QACA,qDAAqD;QACrD,OAAO,CAAC,GAAG,iBAAiB,yBAAyB,EAAE,IAAI,EAAE,cAAc,aAC3E,mBAAmB;QACnB,MAAM;IACV;AACJ;AACA,SAAS,0BAA0B,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ;IAC1E,OAAO,SAAU,GAAG,IAAI;QACpB,MAAM,WAAW,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,KAAK,aAAa,KAAK,GAAG,KAAK;QAC5E,MAAM,UAAU;YACZ,eAAe;QACnB;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YACrC,QAAQ,UAAU,GAAG,IAAI;QAC7B;QACA,gDAAgD;QAChD,IAAI,CAAC,CAAC,GAAG,iBAAiB,uBAAuB,EAAE,IAAI,EAAE,cAAc,cAAc;YACjF,OAAO,OAAO,OAAO,CAAC,IAAI,EAAE,MAAM,SAAS;QAC/C;QACA,qDAAqD;QACrD,OAAO,CAAC,GAAG,iBAAiB,yBAAyB,EAAE,IAAI,EAAE,cAAc,aAAa,MAAM;IAClG;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1352, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/Pipeline.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst calculateSlot = require(\"cluster-key-slot\");\nconst commands_1 = require(\"@ioredis/commands\");\nconst standard_as_callback_1 = require(\"standard-as-callback\");\nconst util_1 = require(\"util\");\nconst Command_1 = require(\"./Command\");\nconst utils_1 = require(\"./utils\");\nconst Commander_1 = require(\"./utils/Commander\");\n/*\n  This function derives from the cluster-key-slot implementation.\n  Instead of checking that all keys have the same slot, it checks that all slots are served by the same set of nodes.\n  If this is satisfied, it returns the first key's slot.\n*/\nfunction generateMultiWithNodes(redis, keys) {\n    const slot = calculateSlot(keys[0]);\n    const target = redis._groupsBySlot[slot];\n    for (let i = 1; i < keys.length; i++) {\n        if (redis._groupsBySlot[calculateSlot(keys[i])] !== target) {\n            return -1;\n        }\n    }\n    return slot;\n}\nclass Pipeline extends Commander_1.default {\n    constructor(redis) {\n        super();\n        this.redis = redis;\n        this.isPipeline = true;\n        this.replyPending = 0;\n        this._queue = [];\n        this._result = [];\n        this._transactions = 0;\n        this._shaToScript = {};\n        this.isCluster =\n            this.redis.constructor.name === \"Cluster\" || this.redis.isCluster;\n        this.options = redis.options;\n        Object.keys(redis.scriptsSet).forEach((name) => {\n            const script = redis.scriptsSet[name];\n            this._shaToScript[script.sha] = script;\n            this[name] = redis[name];\n            this[name + \"Buffer\"] = redis[name + \"Buffer\"];\n        });\n        redis.addedBuiltinSet.forEach((name) => {\n            this[name] = redis[name];\n            this[name + \"Buffer\"] = redis[name + \"Buffer\"];\n        });\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n        const _this = this;\n        Object.defineProperty(this, \"length\", {\n            get: function () {\n                return _this._queue.length;\n            },\n        });\n    }\n    fillResult(value, position) {\n        if (this._queue[position].name === \"exec\" && Array.isArray(value[1])) {\n            const execLength = value[1].length;\n            for (let i = 0; i < execLength; i++) {\n                if (value[1][i] instanceof Error) {\n                    continue;\n                }\n                const cmd = this._queue[position - (execLength - i)];\n                try {\n                    value[1][i] = cmd.transformReply(value[1][i]);\n                }\n                catch (err) {\n                    value[1][i] = err;\n                }\n            }\n        }\n        this._result[position] = value;\n        if (--this.replyPending) {\n            return;\n        }\n        if (this.isCluster) {\n            let retriable = true;\n            let commonError;\n            for (let i = 0; i < this._result.length; ++i) {\n                const error = this._result[i][0];\n                const command = this._queue[i];\n                if (error) {\n                    if (command.name === \"exec\" &&\n                        error.message ===\n                            \"EXECABORT Transaction discarded because of previous errors.\") {\n                        continue;\n                    }\n                    if (!commonError) {\n                        commonError = {\n                            name: error.name,\n                            message: error.message,\n                        };\n                    }\n                    else if (commonError.name !== error.name ||\n                        commonError.message !== error.message) {\n                        retriable = false;\n                        break;\n                    }\n                }\n                else if (!command.inTransaction) {\n                    const isReadOnly = (0, commands_1.exists)(command.name) && (0, commands_1.hasFlag)(command.name, \"readonly\");\n                    if (!isReadOnly) {\n                        retriable = false;\n                        break;\n                    }\n                }\n            }\n            if (commonError && retriable) {\n                const _this = this;\n                const errv = commonError.message.split(\" \");\n                const queue = this._queue;\n                let inTransaction = false;\n                this._queue = [];\n                for (let i = 0; i < queue.length; ++i) {\n                    if (errv[0] === \"ASK\" &&\n                        !inTransaction &&\n                        queue[i].name !== \"asking\" &&\n                        (!queue[i - 1] || queue[i - 1].name !== \"asking\")) {\n                        const asking = new Command_1.default(\"asking\");\n                        asking.ignore = true;\n                        this.sendCommand(asking);\n                    }\n                    queue[i].initPromise();\n                    this.sendCommand(queue[i]);\n                    inTransaction = queue[i].inTransaction;\n                }\n                let matched = true;\n                if (typeof this.leftRedirections === \"undefined\") {\n                    this.leftRedirections = {};\n                }\n                const exec = function () {\n                    _this.exec();\n                };\n                const cluster = this.redis;\n                cluster.handleError(commonError, this.leftRedirections, {\n                    moved: function (_slot, key) {\n                        _this.preferKey = key;\n                        cluster.slots[errv[1]] = [key];\n                        cluster._groupsBySlot[errv[1]] =\n                            cluster._groupsIds[cluster.slots[errv[1]].join(\";\")];\n                        cluster.refreshSlotsCache();\n                        _this.exec();\n                    },\n                    ask: function (_slot, key) {\n                        _this.preferKey = key;\n                        _this.exec();\n                    },\n                    tryagain: exec,\n                    clusterDown: exec,\n                    connectionClosed: exec,\n                    maxRedirections: () => {\n                        matched = false;\n                    },\n                    defaults: () => {\n                        matched = false;\n                    },\n                });\n                if (matched) {\n                    return;\n                }\n            }\n        }\n        let ignoredCount = 0;\n        for (let i = 0; i < this._queue.length - ignoredCount; ++i) {\n            if (this._queue[i + ignoredCount].ignore) {\n                ignoredCount += 1;\n            }\n            this._result[i] = this._result[i + ignoredCount];\n        }\n        this.resolve(this._result.slice(0, this._result.length - ignoredCount));\n    }\n    sendCommand(command) {\n        if (this._transactions > 0) {\n            command.inTransaction = true;\n        }\n        const position = this._queue.length;\n        command.pipelineIndex = position;\n        command.promise\n            .then((result) => {\n            this.fillResult([null, result], position);\n        })\n            .catch((error) => {\n            this.fillResult([error], position);\n        });\n        this._queue.push(command);\n        return this;\n    }\n    addBatch(commands) {\n        let command, commandName, args;\n        for (let i = 0; i < commands.length; ++i) {\n            command = commands[i];\n            commandName = command[0];\n            args = command.slice(1);\n            this[commandName].apply(this, args);\n        }\n        return this;\n    }\n}\nexports.default = Pipeline;\n// @ts-expect-error\nconst multi = Pipeline.prototype.multi;\n// @ts-expect-error\nPipeline.prototype.multi = function () {\n    this._transactions += 1;\n    return multi.apply(this, arguments);\n};\n// @ts-expect-error\nconst execBuffer = Pipeline.prototype.execBuffer;\n// @ts-expect-error\nPipeline.prototype.execBuffer = (0, util_1.deprecate)(function () {\n    if (this._transactions > 0) {\n        this._transactions -= 1;\n    }\n    return execBuffer.apply(this, arguments);\n}, \"Pipeline#execBuffer: Use Pipeline#exec instead\");\n// NOTE: To avoid an unhandled promise rejection, this will unconditionally always return this.promise,\n// which always has the rejection handled by standard-as-callback\n// adding the provided rejection callback.\n//\n// If a different promise instance were returned, that promise would cause its own unhandled promise rejection\n// errors, even if that promise unconditionally resolved to **the resolved value of** this.promise.\nPipeline.prototype.exec = function (callback) {\n    // Wait for the cluster to be connected, since we need nodes information before continuing\n    if (this.isCluster && !this.redis.slots.length) {\n        if (this.redis.status === \"wait\")\n            this.redis.connect().catch(utils_1.noop);\n        if (callback && !this.nodeifiedPromise) {\n            this.nodeifiedPromise = true;\n            (0, standard_as_callback_1.default)(this.promise, callback);\n        }\n        this.redis.delayUntilReady((err) => {\n            if (err) {\n                this.reject(err);\n                return;\n            }\n            this.exec(callback);\n        });\n        return this.promise;\n    }\n    if (this._transactions > 0) {\n        this._transactions -= 1;\n        return execBuffer.apply(this, arguments);\n    }\n    if (!this.nodeifiedPromise) {\n        this.nodeifiedPromise = true;\n        (0, standard_as_callback_1.default)(this.promise, callback);\n    }\n    if (!this._queue.length) {\n        this.resolve([]);\n    }\n    let pipelineSlot;\n    if (this.isCluster) {\n        // List of the first key for each command\n        const sampleKeys = [];\n        for (let i = 0; i < this._queue.length; i++) {\n            const keys = this._queue[i].getKeys();\n            if (keys.length) {\n                sampleKeys.push(keys[0]);\n            }\n            // For each command, check that the keys belong to the same slot\n            if (keys.length && calculateSlot.generateMulti(keys) < 0) {\n                this.reject(new Error(\"All the keys in a pipeline command should belong to the same slot\"));\n                return this.promise;\n            }\n        }\n        if (sampleKeys.length) {\n            pipelineSlot = generateMultiWithNodes(this.redis, sampleKeys);\n            if (pipelineSlot < 0) {\n                this.reject(new Error(\"All keys in the pipeline should belong to the same slots allocation group\"));\n                return this.promise;\n            }\n        }\n        else {\n            // Send the pipeline to a random node\n            pipelineSlot = (Math.random() * 16384) | 0;\n        }\n    }\n    const _this = this;\n    execPipeline();\n    return this.promise;\n    function execPipeline() {\n        let writePending = (_this.replyPending = _this._queue.length);\n        let node;\n        if (_this.isCluster) {\n            node = {\n                slot: pipelineSlot,\n                redis: _this.redis.connectionPool.nodes.all[_this.preferKey],\n            };\n        }\n        let data = \"\";\n        let buffers;\n        const stream = {\n            isPipeline: true,\n            destination: _this.isCluster ? node : { redis: _this.redis },\n            write(writable) {\n                if (typeof writable !== \"string\") {\n                    if (!buffers) {\n                        buffers = [];\n                    }\n                    if (data) {\n                        buffers.push(Buffer.from(data, \"utf8\"));\n                        data = \"\";\n                    }\n                    buffers.push(writable);\n                }\n                else {\n                    data += writable;\n                }\n                if (!--writePending) {\n                    if (buffers) {\n                        if (data) {\n                            buffers.push(Buffer.from(data, \"utf8\"));\n                        }\n                        stream.destination.redis.stream.write(Buffer.concat(buffers));\n                    }\n                    else {\n                        stream.destination.redis.stream.write(data);\n                    }\n                    // Reset writePending for resending\n                    writePending = _this._queue.length;\n                    data = \"\";\n                    buffers = undefined;\n                }\n            },\n        };\n        for (let i = 0; i < _this._queue.length; ++i) {\n            _this.redis.sendCommand(_this._queue[i], stream, node);\n        }\n        return _this.promise;\n    }\n};\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN;;;;AAIA,GACA,SAAS,uBAAuB,KAAK,EAAE,IAAI;IACvC,MAAM,OAAO,cAAc,IAAI,CAAC,EAAE;IAClC,MAAM,SAAS,MAAM,aAAa,CAAC,KAAK;IACxC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QAClC,IAAI,MAAM,aAAa,CAAC,cAAc,IAAI,CAAC,EAAE,EAAE,KAAK,QAAQ;YACxD,OAAO,CAAC;QACZ;IACJ;IACA,OAAO;AACX;AACA,MAAM,iBAAiB,YAAY,OAAO;IACtC,YAAY,KAAK,CAAE;QACf,KAAK;QACL,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,YAAY,GAAG,CAAC;QACrB,IAAI,CAAC,SAAS,GACV,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,aAAa,IAAI,CAAC,KAAK,CAAC,SAAS;QACrE,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;QAC5B,OAAO,IAAI,CAAC,MAAM,UAAU,EAAE,OAAO,CAAC,CAAC;YACnC,MAAM,SAAS,MAAM,UAAU,CAAC,KAAK;YACrC,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,GAAG;YAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;YACxB,IAAI,CAAC,OAAO,SAAS,GAAG,KAAK,CAAC,OAAO,SAAS;QAClD;QACA,MAAM,eAAe,CAAC,OAAO,CAAC,CAAC;YAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;YACxB,IAAI,CAAC,OAAO,SAAS,GAAG,KAAK,CAAC,OAAO,SAAS;QAClD;QACA,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,CAAC,SAAS;YACjC,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,MAAM,GAAG;QAClB;QACA,MAAM,QAAQ,IAAI;QAClB,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU;YAClC,KAAK;gBACD,OAAO,MAAM,MAAM,CAAC,MAAM;YAC9B;QACJ;IACJ;IACA,WAAW,KAAK,EAAE,QAAQ,EAAE;QACxB,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,UAAU,MAAM,OAAO,CAAC,KAAK,CAAC,EAAE,GAAG;YAClE,MAAM,aAAa,KAAK,CAAC,EAAE,CAAC,MAAM;YAClC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;gBACjC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,YAAY,OAAO;oBAC9B;gBACJ;gBACA,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE;gBACpD,IAAI;oBACA,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;gBAChD,EACA,OAAO,KAAK;oBACR,KAAK,CAAC,EAAE,CAAC,EAAE,GAAG;gBAClB;YACJ;QACJ;QACA,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG;QACzB,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE;YACrB;QACJ;QACA,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,YAAY;YAChB,IAAI;YACJ,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,EAAG;gBAC1C,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBAChC,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC9B,IAAI,OAAO;oBACP,IAAI,QAAQ,IAAI,KAAK,UACjB,MAAM,OAAO,KACT,+DAA+D;wBACnE;oBACJ;oBACA,IAAI,CAAC,aAAa;wBACd,cAAc;4BACV,MAAM,MAAM,IAAI;4BAChB,SAAS,MAAM,OAAO;wBAC1B;oBACJ,OACK,IAAI,YAAY,IAAI,KAAK,MAAM,IAAI,IACpC,YAAY,OAAO,KAAK,MAAM,OAAO,EAAE;wBACvC,YAAY;wBACZ;oBACJ;gBACJ,OACK,IAAI,CAAC,QAAQ,aAAa,EAAE;oBAC7B,MAAM,aAAa,CAAC,GAAG,WAAW,MAAM,EAAE,QAAQ,IAAI,KAAK,CAAC,GAAG,WAAW,OAAO,EAAE,QAAQ,IAAI,EAAE;oBACjG,IAAI,CAAC,YAAY;wBACb,YAAY;wBACZ;oBACJ;gBACJ;YACJ;YACA,IAAI,eAAe,WAAW;gBAC1B,MAAM,QAAQ,IAAI;gBAClB,MAAM,OAAO,YAAY,OAAO,CAAC,KAAK,CAAC;gBACvC,MAAM,QAAQ,IAAI,CAAC,MAAM;gBACzB,IAAI,gBAAgB;gBACpB,IAAI,CAAC,MAAM,GAAG,EAAE;gBAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;oBACnC,IAAI,IAAI,CAAC,EAAE,KAAK,SACZ,CAAC,iBACD,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,YAClB,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,QAAQ,GAAG;wBACnD,MAAM,SAAS,IAAI,UAAU,OAAO,CAAC;wBACrC,OAAO,MAAM,GAAG;wBAChB,IAAI,CAAC,WAAW,CAAC;oBACrB;oBACA,KAAK,CAAC,EAAE,CAAC,WAAW;oBACpB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;oBACzB,gBAAgB,KAAK,CAAC,EAAE,CAAC,aAAa;gBAC1C;gBACA,IAAI,UAAU;gBACd,IAAI,OAAO,IAAI,CAAC,gBAAgB,KAAK,aAAa;oBAC9C,IAAI,CAAC,gBAAgB,GAAG,CAAC;gBAC7B;gBACA,MAAM,OAAO;oBACT,MAAM,IAAI;gBACd;gBACA,MAAM,UAAU,IAAI,CAAC,KAAK;gBAC1B,QAAQ,WAAW,CAAC,aAAa,IAAI,CAAC,gBAAgB,EAAE;oBACpD,OAAO,SAAU,KAAK,EAAE,GAAG;wBACvB,MAAM,SAAS,GAAG;wBAClB,QAAQ,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG;4BAAC;yBAAI;wBAC9B,QAAQ,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,GAC1B,QAAQ,UAAU,CAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK;wBACxD,QAAQ,iBAAiB;wBACzB,MAAM,IAAI;oBACd;oBACA,KAAK,SAAU,KAAK,EAAE,GAAG;wBACrB,MAAM,SAAS,GAAG;wBAClB,MAAM,IAAI;oBACd;oBACA,UAAU;oBACV,aAAa;oBACb,kBAAkB;oBAClB,iBAAiB;wBACb,UAAU;oBACd;oBACA,UAAU;wBACN,UAAU;oBACd;gBACJ;gBACA,IAAI,SAAS;oBACT;gBACJ;YACJ;QACJ;QACA,IAAI,eAAe;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,cAAc,EAAE,EAAG;YACxD,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,aAAa,CAAC,MAAM,EAAE;gBACtC,gBAAgB;YACpB;YACA,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,aAAa;QACpD;QACA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG;IAC7D;IACA,YAAY,OAAO,EAAE;QACjB,IAAI,IAAI,CAAC,aAAa,GAAG,GAAG;YACxB,QAAQ,aAAa,GAAG;QAC5B;QACA,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,MAAM;QACnC,QAAQ,aAAa,GAAG;QACxB,QAAQ,OAAO,CACV,IAAI,CAAC,CAAC;YACP,IAAI,CAAC,UAAU,CAAC;gBAAC;gBAAM;aAAO,EAAE;QACpC,GACK,KAAK,CAAC,CAAC;YACR,IAAI,CAAC,UAAU,CAAC;gBAAC;aAAM,EAAE;QAC7B;QACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjB,OAAO,IAAI;IACf;IACA,SAAS,QAAQ,EAAE;QACf,IAAI,SAAS,aAAa;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,EAAE,EAAG;YACtC,UAAU,QAAQ,CAAC,EAAE;YACrB,cAAc,OAAO,CAAC,EAAE;YACxB,OAAO,QAAQ,KAAK,CAAC;YACrB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE;QAClC;QACA,OAAO,IAAI;IACf;AACJ;AACA,QAAQ,OAAO,GAAG;AAClB,mBAAmB;AACnB,MAAM,QAAQ,SAAS,SAAS,CAAC,KAAK;AACtC,mBAAmB;AACnB,SAAS,SAAS,CAAC,KAAK,GAAG;IACvB,IAAI,CAAC,aAAa,IAAI;IACtB,OAAO,MAAM,KAAK,CAAC,IAAI,EAAE;AAC7B;AACA,mBAAmB;AACnB,MAAM,aAAa,SAAS,SAAS,CAAC,UAAU;AAChD,mBAAmB;AACnB,SAAS,SAAS,CAAC,UAAU,GAAG,CAAC,GAAG,OAAO,SAAS,EAAE;IAClD,IAAI,IAAI,CAAC,aAAa,GAAG,GAAG;QACxB,IAAI,CAAC,aAAa,IAAI;IAC1B;IACA,OAAO,WAAW,KAAK,CAAC,IAAI,EAAE;AAClC,GAAG;AACH,uGAAuG;AACvG,iEAAiE;AACjE,0CAA0C;AAC1C,EAAE;AACF,8GAA8G;AAC9G,mGAAmG;AACnG,SAAS,SAAS,CAAC,IAAI,GAAG,SAAU,QAAQ;IACxC,0FAA0F;IAC1F,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE;QAC5C,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,QACtB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,QAAQ,IAAI;QAC3C,IAAI,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACpC,IAAI,CAAC,gBAAgB,GAAG;YACxB,CAAC,GAAG,uBAAuB,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QACtD;QACA,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YACxB,IAAI,KAAK;gBACL,IAAI,CAAC,MAAM,CAAC;gBACZ;YACJ;YACA,IAAI,CAAC,IAAI,CAAC;QACd;QACA,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,IAAI,IAAI,CAAC,aAAa,GAAG,GAAG;QACxB,IAAI,CAAC,aAAa,IAAI;QACtB,OAAO,WAAW,KAAK,CAAC,IAAI,EAAE;IAClC;IACA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QACxB,IAAI,CAAC,gBAAgB,GAAG;QACxB,CAAC,GAAG,uBAAuB,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;IACtD;IACA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;QACrB,IAAI,CAAC,OAAO,CAAC,EAAE;IACnB;IACA,IAAI;IACJ,IAAI,IAAI,CAAC,SAAS,EAAE;QAChB,yCAAyC;QACzC,MAAM,aAAa,EAAE;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAK;YACzC,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;YACnC,IAAI,KAAK,MAAM,EAAE;gBACb,WAAW,IAAI,CAAC,IAAI,CAAC,EAAE;YAC3B;YACA,gEAAgE;YAChE,IAAI,KAAK,MAAM,IAAI,cAAc,aAAa,CAAC,QAAQ,GAAG;gBACtD,IAAI,CAAC,MAAM,CAAC,IAAI,MAAM;gBACtB,OAAO,IAAI,CAAC,OAAO;YACvB;QACJ;QACA,IAAI,WAAW,MAAM,EAAE;YACnB,eAAe,uBAAuB,IAAI,CAAC,KAAK,EAAE;YAClD,IAAI,eAAe,GAAG;gBAClB,IAAI,CAAC,MAAM,CAAC,IAAI,MAAM;gBACtB,OAAO,IAAI,CAAC,OAAO;YACvB;QACJ,OACK;YACD,qCAAqC;YACrC,eAAe,AAAC,KAAK,MAAM,KAAK,QAAS;QAC7C;IACJ;IACA,MAAM,QAAQ,IAAI;IAClB;IACA,OAAO,IAAI,CAAC,OAAO;;;IACnB,SAAS;QACL,IAAI,eAAgB,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM;QAC5D,IAAI;QACJ,IAAI,MAAM,SAAS,EAAE;YACjB,OAAO;gBACH,MAAM;gBACN,OAAO,MAAM,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,SAAS,CAAC;YAChE;QACJ;QACA,IAAI,OAAO;QACX,IAAI;QACJ,MAAM,SAAS;YACX,YAAY;YACZ,aAAa,MAAM,SAAS,GAAG,OAAO;gBAAE,OAAO,MAAM,KAAK;YAAC;YAC3D,OAAM,QAAQ;gBACV,IAAI,OAAO,aAAa,UAAU;oBAC9B,IAAI,CAAC,SAAS;wBACV,UAAU,EAAE;oBAChB;oBACA,IAAI,MAAM;wBACN,QAAQ,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM;wBAC/B,OAAO;oBACX;oBACA,QAAQ,IAAI,CAAC;gBACjB,OACK;oBACD,QAAQ;gBACZ;gBACA,IAAI,CAAC,EAAE,cAAc;oBACjB,IAAI,SAAS;wBACT,IAAI,MAAM;4BACN,QAAQ,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM;wBACnC;wBACA,OAAO,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,MAAM,CAAC;oBACxD,OACK;wBACD,OAAO,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC1C;oBACA,mCAAmC;oBACnC,eAAe,MAAM,MAAM,CAAC,MAAM;oBAClC,OAAO;oBACP,UAAU;gBACd;YACJ;QACJ;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,CAAC,MAAM,EAAE,EAAE,EAAG;YAC1C,MAAM,KAAK,CAAC,WAAW,CAAC,MAAM,MAAM,CAAC,EAAE,EAAE,QAAQ;QACrD;QACA,OAAO,MAAM,OAAO;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/transaction.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.addTransactionSupport = void 0;\nconst utils_1 = require(\"./utils\");\nconst standard_as_callback_1 = require(\"standard-as-callback\");\nconst Pipeline_1 = require(\"./Pipeline\");\nfunction addTransactionSupport(redis) {\n    redis.pipeline = function (commands) {\n        const pipeline = new Pipeline_1.default(this);\n        if (Array.isArray(commands)) {\n            pipeline.addBatch(commands);\n        }\n        return pipeline;\n    };\n    const { multi } = redis;\n    redis.multi = function (commands, options) {\n        if (typeof options === \"undefined\" && !Array.isArray(commands)) {\n            options = commands;\n            commands = null;\n        }\n        if (options && options.pipeline === false) {\n            return multi.call(this);\n        }\n        const pipeline = new Pipeline_1.default(this);\n        // @ts-expect-error\n        pipeline.multi();\n        if (Array.isArray(commands)) {\n            pipeline.addBatch(commands);\n        }\n        const exec = pipeline.exec;\n        pipeline.exec = function (callback) {\n            // Wait for the cluster to be connected, since we need nodes information before continuing\n            if (this.isCluster && !this.redis.slots.length) {\n                if (this.redis.status === \"wait\")\n                    this.redis.connect().catch(utils_1.noop);\n                return (0, standard_as_callback_1.default)(new Promise((resolve, reject) => {\n                    this.redis.delayUntilReady((err) => {\n                        if (err) {\n                            reject(err);\n                            return;\n                        }\n                        this.exec(pipeline).then(resolve, reject);\n                    });\n                }), callback);\n            }\n            if (this._transactions > 0) {\n                exec.call(pipeline);\n            }\n            // Returns directly when the pipeline\n            // has been called multiple times (retries).\n            if (this.nodeifiedPromise) {\n                return exec.call(pipeline);\n            }\n            const promise = exec.call(pipeline);\n            return (0, standard_as_callback_1.default)(promise.then(function (result) {\n                const execResult = result[result.length - 1];\n                if (typeof execResult === \"undefined\") {\n                    throw new Error(\"Pipeline cannot be used to send any commands when the `exec()` has been called on it.\");\n                }\n                if (execResult[0]) {\n                    execResult[0].previousErrors = [];\n                    for (let i = 0; i < result.length - 1; ++i) {\n                        if (result[i][0]) {\n                            execResult[0].previousErrors.push(result[i][0]);\n                        }\n                    }\n                    throw execResult[0];\n                }\n                return (0, utils_1.wrapMultiResult)(execResult[1]);\n            }), callback);\n        };\n        // @ts-expect-error\n        const { execBuffer } = pipeline;\n        // @ts-expect-error\n        pipeline.execBuffer = function (callback) {\n            if (this._transactions > 0) {\n                execBuffer.call(pipeline);\n            }\n            return pipeline.exec(callback);\n        };\n        return pipeline;\n    };\n    const { exec } = redis;\n    redis.exec = function (callback) {\n        return (0, standard_as_callback_1.default)(exec.call(this).then(function (results) {\n            if (Array.isArray(results)) {\n                results = (0, utils_1.wrapMultiResult)(results);\n            }\n            return results;\n        }), callback);\n    };\n}\nexports.addTransactionSupport = addTransactionSupport;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,qBAAqB,GAAG,KAAK;AACrC,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,sBAAsB,KAAK;IAChC,MAAM,QAAQ,GAAG,SAAU,QAAQ;QAC/B,MAAM,WAAW,IAAI,WAAW,OAAO,CAAC,IAAI;QAC5C,IAAI,MAAM,OAAO,CAAC,WAAW;YACzB,SAAS,QAAQ,CAAC;QACtB;QACA,OAAO;IACX;IACA,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,MAAM,KAAK,GAAG,SAAU,QAAQ,EAAE,OAAO;QACrC,IAAI,OAAO,YAAY,eAAe,CAAC,MAAM,OAAO,CAAC,WAAW;YAC5D,UAAU;YACV,WAAW;QACf;QACA,IAAI,WAAW,QAAQ,QAAQ,KAAK,OAAO;YACvC,OAAO,MAAM,IAAI,CAAC,IAAI;QAC1B;QACA,MAAM,WAAW,IAAI,WAAW,OAAO,CAAC,IAAI;QAC5C,mBAAmB;QACnB,SAAS,KAAK;QACd,IAAI,MAAM,OAAO,CAAC,WAAW;YACzB,SAAS,QAAQ,CAAC;QACtB;QACA,MAAM,OAAO,SAAS,IAAI;QAC1B,SAAS,IAAI,GAAG,SAAU,QAAQ;YAC9B,0FAA0F;YAC1F,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE;gBAC5C,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,QACtB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,QAAQ,IAAI;gBAC3C,OAAO,CAAC,GAAG,uBAAuB,OAAO,EAAE,IAAI,QAAQ,CAAC,SAAS;oBAC7D,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;wBACxB,IAAI,KAAK;4BACL,OAAO;4BACP;wBACJ;wBACA,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,SAAS;oBACtC;gBACJ,IAAI;YACR;YACA,IAAI,IAAI,CAAC,aAAa,GAAG,GAAG;gBACxB,KAAK,IAAI,CAAC;YACd;YACA,qCAAqC;YACrC,4CAA4C;YAC5C,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,OAAO,KAAK,IAAI,CAAC;YACrB;YACA,MAAM,UAAU,KAAK,IAAI,CAAC;YAC1B,OAAO,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,IAAI,CAAC,SAAU,MAAM;gBACpE,MAAM,aAAa,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;gBAC5C,IAAI,OAAO,eAAe,aAAa;oBACnC,MAAM,IAAI,MAAM;gBACpB;gBACA,IAAI,UAAU,CAAC,EAAE,EAAE;oBACf,UAAU,CAAC,EAAE,CAAC,cAAc,GAAG,EAAE;oBACjC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,GAAG,GAAG,EAAE,EAAG;wBACxC,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE;4BACd,UAAU,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;wBAClD;oBACJ;oBACA,MAAM,UAAU,CAAC,EAAE;gBACvB;gBACA,OAAO,CAAC,GAAG,QAAQ,eAAe,EAAE,UAAU,CAAC,EAAE;YACrD,IAAI;QACR;QACA,mBAAmB;QACnB,MAAM,EAAE,UAAU,EAAE,GAAG;QACvB,mBAAmB;QACnB,SAAS,UAAU,GAAG,SAAU,QAAQ;YACpC,IAAI,IAAI,CAAC,aAAa,GAAG,GAAG;gBACxB,WAAW,IAAI,CAAC;YACpB;YACA,OAAO,SAAS,IAAI,CAAC;QACzB;QACA,OAAO;IACX;IACA,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,MAAM,IAAI,GAAG,SAAU,QAAQ;QAC3B,OAAO,CAAC,GAAG,uBAAuB,OAAO,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAU,OAAO;YAC7E,IAAI,MAAM,OAAO,CAAC,UAAU;gBACxB,UAAU,CAAC,GAAG,QAAQ,eAAe,EAAE;YAC3C;YACA,OAAO;QACX,IAAI;IACR;AACJ;AACA,QAAQ,qBAAqB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1785, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/utils/applyMixin.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nfunction applyMixin(derivedConstructor, mixinConstructor) {\n    Object.getOwnPropertyNames(mixinConstructor.prototype).forEach((name) => {\n        Object.defineProperty(derivedConstructor.prototype, name, Object.getOwnPropertyDescriptor(mixinConstructor.prototype, name));\n    });\n}\nexports.default = applyMixin;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,SAAS,WAAW,kBAAkB,EAAE,gBAAgB;IACpD,OAAO,mBAAmB,CAAC,iBAAiB,SAAS,EAAE,OAAO,CAAC,CAAC;QAC5D,OAAO,cAAc,CAAC,mBAAmB,SAAS,EAAE,MAAM,OAAO,wBAAwB,CAAC,iBAAiB,SAAS,EAAE;IAC1H;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1800, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/cluster/ClusterOptions.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DEFAULT_CLUSTER_OPTIONS = void 0;\nconst dns_1 = require(\"dns\");\nexports.DEFAULT_CLUSTER_OPTIONS = {\n    clusterRetryStrategy: (times) => Math.min(100 + times * 2, 2000),\n    enableOfflineQueue: true,\n    enableReadyCheck: true,\n    scaleReads: \"master\",\n    maxRedirections: 16,\n    retryDelayOnMoved: 0,\n    retryDelayOnFailover: 100,\n    retryDelayOnClusterDown: 100,\n    retryDelayOnTryAgain: 100,\n    slotsRefreshTimeout: 1000,\n    useSRVRecords: false,\n    resolveSrv: dns_1.resolveSrv,\n    dnsLookup: dns_1.lookup,\n    enableAutoPipelining: false,\n    autoPipeliningIgnoredCommands: [],\n    shardedSubscribers: false,\n};\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,uBAAuB,GAAG,KAAK;AACvC,MAAM;AACN,QAAQ,uBAAuB,GAAG;IAC9B,sBAAsB,CAAC,QAAU,KAAK,GAAG,CAAC,MAAM,QAAQ,GAAG;IAC3D,oBAAoB;IACpB,kBAAkB;IAClB,YAAY;IACZ,iBAAiB;IACjB,mBAAmB;IACnB,sBAAsB;IACtB,yBAAyB;IACzB,sBAAsB;IACtB,qBAAqB;IACrB,eAAe;IACf,YAAY,MAAM,UAAU;IAC5B,WAAW,MAAM,MAAM;IACvB,sBAAsB;IACtB,+BAA+B,EAAE;IACjC,oBAAoB;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1829, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/cluster/util.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getConnectionName = exports.weightSrvRecords = exports.groupSrvRecords = exports.getUniqueHostnamesFromOptions = exports.normalizeNodeOptions = exports.nodeKeyToRedisOptions = exports.getNodeKey = void 0;\nconst utils_1 = require(\"../utils\");\nconst net_1 = require(\"net\");\nfunction getNodeKey(node) {\n    node.port = node.port || 6379;\n    node.host = node.host || \"127.0.0.1\";\n    return node.host + \":\" + node.port;\n}\nexports.getNodeKey = getNodeKey;\nfunction nodeKeyToRedisOptions(nodeKey) {\n    const portIndex = nodeKey.lastIndexOf(\":\");\n    if (portIndex === -1) {\n        throw new Error(`Invalid node key ${nodeKey}`);\n    }\n    return {\n        host: nodeKey.slice(0, portIndex),\n        port: Number(nodeKey.slice(portIndex + 1)),\n    };\n}\nexports.nodeKeyToRedisOptions = nodeKeyToRedisOptions;\nfunction normalizeNodeOptions(nodes) {\n    return nodes.map((node) => {\n        const options = {};\n        if (typeof node === \"object\") {\n            Object.assign(options, node);\n        }\n        else if (typeof node === \"string\") {\n            Object.assign(options, (0, utils_1.parseURL)(node));\n        }\n        else if (typeof node === \"number\") {\n            options.port = node;\n        }\n        else {\n            throw new Error(\"Invalid argument \" + node);\n        }\n        if (typeof options.port === \"string\") {\n            options.port = parseInt(options.port, 10);\n        }\n        // Cluster mode only support db 0\n        delete options.db;\n        if (!options.port) {\n            options.port = 6379;\n        }\n        if (!options.host) {\n            options.host = \"127.0.0.1\";\n        }\n        return (0, utils_1.resolveTLSProfile)(options);\n    });\n}\nexports.normalizeNodeOptions = normalizeNodeOptions;\nfunction getUniqueHostnamesFromOptions(nodes) {\n    const uniqueHostsMap = {};\n    nodes.forEach((node) => {\n        uniqueHostsMap[node.host] = true;\n    });\n    return Object.keys(uniqueHostsMap).filter((host) => !(0, net_1.isIP)(host));\n}\nexports.getUniqueHostnamesFromOptions = getUniqueHostnamesFromOptions;\nfunction groupSrvRecords(records) {\n    const recordsByPriority = {};\n    for (const record of records) {\n        if (!recordsByPriority.hasOwnProperty(record.priority)) {\n            recordsByPriority[record.priority] = {\n                totalWeight: record.weight,\n                records: [record],\n            };\n        }\n        else {\n            recordsByPriority[record.priority].totalWeight += record.weight;\n            recordsByPriority[record.priority].records.push(record);\n        }\n    }\n    return recordsByPriority;\n}\nexports.groupSrvRecords = groupSrvRecords;\nfunction weightSrvRecords(recordsGroup) {\n    if (recordsGroup.records.length === 1) {\n        recordsGroup.totalWeight = 0;\n        return recordsGroup.records.shift();\n    }\n    // + `recordsGroup.records.length` to support `weight` 0\n    const random = Math.floor(Math.random() * (recordsGroup.totalWeight + recordsGroup.records.length));\n    let total = 0;\n    for (const [i, record] of recordsGroup.records.entries()) {\n        total += 1 + record.weight;\n        if (total > random) {\n            recordsGroup.totalWeight -= record.weight;\n            recordsGroup.records.splice(i, 1);\n            return record;\n        }\n    }\n}\nexports.weightSrvRecords = weightSrvRecords;\nfunction getConnectionName(component, nodeConnectionName) {\n    const prefix = `ioredis-cluster(${component})`;\n    return nodeConnectionName ? `${prefix}:${nodeConnectionName}` : prefix;\n}\nexports.getConnectionName = getConnectionName;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,eAAe,GAAG,QAAQ,6BAA6B,GAAG,QAAQ,oBAAoB,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,UAAU,GAAG,KAAK;AAClN,MAAM;AACN,MAAM;AACN,SAAS,WAAW,IAAI;IACpB,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI;IACzB,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI;IACzB,OAAO,KAAK,IAAI,GAAG,MAAM,KAAK,IAAI;AACtC;AACA,QAAQ,UAAU,GAAG;AACrB,SAAS,sBAAsB,OAAO;IAClC,MAAM,YAAY,QAAQ,WAAW,CAAC;IACtC,IAAI,cAAc,CAAC,GAAG;QAClB,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,SAAS;IACjD;IACA,OAAO;QACH,MAAM,QAAQ,KAAK,CAAC,GAAG;QACvB,MAAM,OAAO,QAAQ,KAAK,CAAC,YAAY;IAC3C;AACJ;AACA,QAAQ,qBAAqB,GAAG;AAChC,SAAS,qBAAqB,KAAK;IAC/B,OAAO,MAAM,GAAG,CAAC,CAAC;QACd,MAAM,UAAU,CAAC;QACjB,IAAI,OAAO,SAAS,UAAU;YAC1B,OAAO,MAAM,CAAC,SAAS;QAC3B,OACK,IAAI,OAAO,SAAS,UAAU;YAC/B,OAAO,MAAM,CAAC,SAAS,CAAC,GAAG,QAAQ,QAAQ,EAAE;QACjD,OACK,IAAI,OAAO,SAAS,UAAU;YAC/B,QAAQ,IAAI,GAAG;QACnB,OACK;YACD,MAAM,IAAI,MAAM,sBAAsB;QAC1C;QACA,IAAI,OAAO,QAAQ,IAAI,KAAK,UAAU;YAClC,QAAQ,IAAI,GAAG,SAAS,QAAQ,IAAI,EAAE;QAC1C;QACA,iCAAiC;QACjC,OAAO,QAAQ,EAAE;QACjB,IAAI,CAAC,QAAQ,IAAI,EAAE;YACf,QAAQ,IAAI,GAAG;QACnB;QACA,IAAI,CAAC,QAAQ,IAAI,EAAE;YACf,QAAQ,IAAI,GAAG;QACnB;QACA,OAAO,CAAC,GAAG,QAAQ,iBAAiB,EAAE;IAC1C;AACJ;AACA,QAAQ,oBAAoB,GAAG;AAC/B,SAAS,8BAA8B,KAAK;IACxC,MAAM,iBAAiB,CAAC;IACxB,MAAM,OAAO,CAAC,CAAC;QACX,cAAc,CAAC,KAAK,IAAI,CAAC,GAAG;IAChC;IACA,OAAO,OAAO,IAAI,CAAC,gBAAgB,MAAM,CAAC,CAAC,OAAS,CAAC,CAAC,GAAG,MAAM,IAAI,EAAE;AACzE;AACA,QAAQ,6BAA6B,GAAG;AACxC,SAAS,gBAAgB,OAAO;IAC5B,MAAM,oBAAoB,CAAC;IAC3B,KAAK,MAAM,UAAU,QAAS;QAC1B,IAAI,CAAC,kBAAkB,cAAc,CAAC,OAAO,QAAQ,GAAG;YACpD,iBAAiB,CAAC,OAAO,QAAQ,CAAC,GAAG;gBACjC,aAAa,OAAO,MAAM;gBAC1B,SAAS;oBAAC;iBAAO;YACrB;QACJ,OACK;YACD,iBAAiB,CAAC,OAAO,QAAQ,CAAC,CAAC,WAAW,IAAI,OAAO,MAAM;YAC/D,iBAAiB,CAAC,OAAO,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;QACpD;IACJ;IACA,OAAO;AACX;AACA,QAAQ,eAAe,GAAG;AAC1B,SAAS,iBAAiB,YAAY;IAClC,IAAI,aAAa,OAAO,CAAC,MAAM,KAAK,GAAG;QACnC,aAAa,WAAW,GAAG;QAC3B,OAAO,aAAa,OAAO,CAAC,KAAK;IACrC;IACA,wDAAwD;IACxD,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,aAAa,WAAW,GAAG,aAAa,OAAO,CAAC,MAAM;IACjG,IAAI,QAAQ;IACZ,KAAK,MAAM,CAAC,GAAG,OAAO,IAAI,aAAa,OAAO,CAAC,OAAO,GAAI;QACtD,SAAS,IAAI,OAAO,MAAM;QAC1B,IAAI,QAAQ,QAAQ;YAChB,aAAa,WAAW,IAAI,OAAO,MAAM;YACzC,aAAa,OAAO,CAAC,MAAM,CAAC,GAAG;YAC/B,OAAO;QACX;IACJ;AACJ;AACA,QAAQ,gBAAgB,GAAG;AAC3B,SAAS,kBAAkB,SAAS,EAAE,kBAAkB;IACpD,MAAM,SAAS,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;IAC9C,OAAO,qBAAqB,GAAG,OAAO,CAAC,EAAE,oBAAoB,GAAG;AACpE;AACA,QAAQ,iBAAiB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1934, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/cluster/ClusterSubscriber.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_1 = require(\"./util\");\nconst utils_1 = require(\"../utils\");\nconst Redis_1 = require(\"../Redis\");\nconst debug = (0, utils_1.Debug)(\"cluster:subscriber\");\nclass ClusterSubscriber {\n    constructor(connectionPool, emitter, isSharded = false) {\n        this.connectionPool = connectionPool;\n        this.emitter = emitter;\n        this.isSharded = isSharded;\n        this.started = false;\n        //There is only one connection for the entire pool\n        this.subscriber = null;\n        //The slot range for which this subscriber is responsible\n        this.slotRange = [];\n        this.onSubscriberEnd = () => {\n            if (!this.started) {\n                debug(\"subscriber has disconnected, but ClusterSubscriber is not started, so not reconnecting.\");\n                return;\n            }\n            // If the subscriber closes whilst it's still the active connection,\n            // we might as well try to connecting to a new node if possible to\n            // minimise the number of missed publishes.\n            debug(\"subscriber has disconnected, selecting a new one...\");\n            this.selectSubscriber();\n        };\n        // If the current node we're using as the subscriber disappears\n        // from the node pool for some reason, we will select a new one\n        // to connect to.\n        // Note that this event is only triggered if the connection to\n        // the node has been used; cluster subscriptions are setup with\n        // lazyConnect = true. It's possible for the subscriber node to\n        // disappear without this method being called!\n        // See https://github.com/luin/ioredis/pull/1589\n        this.connectionPool.on(\"-node\", (_, key) => {\n            if (!this.started || !this.subscriber) {\n                return;\n            }\n            if ((0, util_1.getNodeKey)(this.subscriber.options) === key) {\n                debug(\"subscriber has left, selecting a new one...\");\n                this.selectSubscriber();\n            }\n        });\n        this.connectionPool.on(\"+node\", () => {\n            if (!this.started || this.subscriber) {\n                return;\n            }\n            debug(\"a new node is discovered and there is no subscriber, selecting a new one...\");\n            this.selectSubscriber();\n        });\n    }\n    getInstance() {\n        return this.subscriber;\n    }\n    /**\n     * Associate this subscriber to a specific slot range.\n     *\n     * Returns the range or an empty array if the slot range couldn't be associated.\n     *\n     * BTW: This is more for debugging and testing purposes.\n     *\n     * @param range\n     */\n    associateSlotRange(range) {\n        if (this.isSharded) {\n            this.slotRange = range;\n        }\n        return this.slotRange;\n    }\n    start() {\n        this.started = true;\n        this.selectSubscriber();\n        debug(\"started\");\n    }\n    stop() {\n        this.started = false;\n        if (this.subscriber) {\n            this.subscriber.disconnect();\n            this.subscriber = null;\n        }\n    }\n    isStarted() {\n        return this.started;\n    }\n    selectSubscriber() {\n        const lastActiveSubscriber = this.lastActiveSubscriber;\n        // Disconnect the previous subscriber even if there\n        // will not be a new one.\n        if (lastActiveSubscriber) {\n            lastActiveSubscriber.off(\"end\", this.onSubscriberEnd);\n            lastActiveSubscriber.disconnect();\n        }\n        if (this.subscriber) {\n            this.subscriber.off(\"end\", this.onSubscriberEnd);\n            this.subscriber.disconnect();\n        }\n        const sampleNode = (0, utils_1.sample)(this.connectionPool.getNodes());\n        if (!sampleNode) {\n            debug(\"selecting subscriber failed since there is no node discovered in the cluster yet\");\n            this.subscriber = null;\n            return;\n        }\n        const { options } = sampleNode;\n        debug(\"selected a subscriber %s:%s\", options.host, options.port);\n        /*\n         * Create a specialized Redis connection for the subscription.\n         * Note that auto reconnection is enabled here.\n         *\n         * `enableReadyCheck` is also enabled because although subscription is allowed\n         * while redis is loading data from the disk, we can check if the password\n         * provided for the subscriber is correct, and if not, the current subscriber\n         * will be disconnected and a new subscriber will be selected.\n         */\n        let connectionPrefix = \"subscriber\";\n        if (this.isSharded)\n            connectionPrefix = \"ssubscriber\";\n        this.subscriber = new Redis_1.default({\n            port: options.port,\n            host: options.host,\n            username: options.username,\n            password: options.password,\n            enableReadyCheck: true,\n            connectionName: (0, util_1.getConnectionName)(connectionPrefix, options.connectionName),\n            lazyConnect: true,\n            tls: options.tls,\n            // Don't try to reconnect the subscriber connection. If the connection fails\n            // we will get an end event (handled below), at which point we'll pick a new\n            // node from the pool and try to connect to that as the subscriber connection.\n            retryStrategy: null,\n        });\n        // Ignore the errors since they're handled in the connection pool.\n        this.subscriber.on(\"error\", utils_1.noop);\n        // The node we lost connection to may not come back up in a\n        // reasonable amount of time (e.g. a slave that's taken down\n        // for maintainence), we could potentially miss many published\n        // messages so we should reconnect as quickly as possible, to\n        // a different node if needed.\n        this.subscriber.once(\"end\", this.onSubscriberEnd);\n        // Re-subscribe previous channels\n        const previousChannels = { subscribe: [], psubscribe: [], ssubscribe: [] };\n        if (lastActiveSubscriber) {\n            const condition = lastActiveSubscriber.condition || lastActiveSubscriber.prevCondition;\n            if (condition && condition.subscriber) {\n                previousChannels.subscribe = condition.subscriber.channels(\"subscribe\");\n                previousChannels.psubscribe =\n                    condition.subscriber.channels(\"psubscribe\");\n                previousChannels.ssubscribe =\n                    condition.subscriber.channels(\"ssubscribe\");\n            }\n        }\n        if (previousChannels.subscribe.length ||\n            previousChannels.psubscribe.length ||\n            previousChannels.ssubscribe.length) {\n            let pending = 0;\n            for (const type of [\"subscribe\", \"psubscribe\", \"ssubscribe\"]) {\n                const channels = previousChannels[type];\n                if (channels.length) {\n                    pending += 1;\n                    debug(\"%s %d channels\", type, channels.length);\n                    this.subscriber[type](channels)\n                        .then(() => {\n                        if (!--pending) {\n                            this.lastActiveSubscriber = this.subscriber;\n                        }\n                    })\n                        .catch(() => {\n                        // TODO: should probably disconnect the subscriber and try again.\n                        debug(\"failed to %s %d channels\", type, channels.length);\n                    });\n                }\n            }\n        }\n        else {\n            this.lastActiveSubscriber = this.subscriber;\n        }\n        for (const event of [\n            \"message\",\n            \"messageBuffer\",\n        ]) {\n            this.subscriber.on(event, (arg1, arg2) => {\n                this.emitter.emit(event, arg1, arg2);\n            });\n        }\n        for (const event of [\"pmessage\", \"pmessageBuffer\"]) {\n            this.subscriber.on(event, (arg1, arg2, arg3) => {\n                this.emitter.emit(event, arg1, arg2, arg3);\n            });\n        }\n        if (this.isSharded == true) {\n            for (const event of [\n                \"smessage\",\n                \"smessageBuffer\",\n            ]) {\n                this.subscriber.on(event, (arg1, arg2) => {\n                    this.emitter.emit(event, arg1, arg2);\n                });\n            }\n        }\n    }\n}\nexports.default = ClusterSubscriber;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAQ,KAAK,EAAE;AACjC,MAAM;IACF,YAAY,cAAc,EAAE,OAAO,EAAE,YAAY,KAAK,CAAE;QACpD,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,kDAAkD;QAClD,IAAI,CAAC,UAAU,GAAG;QAClB,yDAAyD;QACzD,IAAI,CAAC,SAAS,GAAG,EAAE;QACnB,IAAI,CAAC,eAAe,GAAG;YACnB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,MAAM;gBACN;YACJ;YACA,oEAAoE;YACpE,kEAAkE;YAClE,2CAA2C;YAC3C,MAAM;YACN,IAAI,CAAC,gBAAgB;QACzB;QACA,+DAA+D;QAC/D,+DAA+D;QAC/D,iBAAiB;QACjB,8DAA8D;QAC9D,+DAA+D;QAC/D,+DAA+D;QAC/D,8CAA8C;QAC9C,gDAAgD;QAChD,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG;YAChC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACnC;YACJ;YACA,IAAI,CAAC,GAAG,OAAO,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,KAAK;gBACzD,MAAM;gBACN,IAAI,CAAC,gBAAgB;YACzB;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,SAAS;YAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE;gBAClC;YACJ;YACA,MAAM;YACN,IAAI,CAAC,gBAAgB;QACzB;IACJ;IACA,cAAc;QACV,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA;;;;;;;;KAQC,GACD,mBAAmB,KAAK,EAAE;QACtB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,GAAG;QACrB;QACA,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,QAAQ;QACJ,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,gBAAgB;QACrB,MAAM;IACV;IACA,OAAO;QACH,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,UAAU;YAC1B,IAAI,CAAC,UAAU,GAAG;QACtB;IACJ;IACA,YAAY;QACR,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,mBAAmB;QACf,MAAM,uBAAuB,IAAI,CAAC,oBAAoB;QACtD,mDAAmD;QACnD,yBAAyB;QACzB,IAAI,sBAAsB;YACtB,qBAAqB,GAAG,CAAC,OAAO,IAAI,CAAC,eAAe;YACpD,qBAAqB,UAAU;QACnC;QACA,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,eAAe;YAC/C,IAAI,CAAC,UAAU,CAAC,UAAU;QAC9B;QACA,MAAM,aAAa,CAAC,GAAG,QAAQ,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ;QACnE,IAAI,CAAC,YAAY;YACb,MAAM;YACN,IAAI,CAAC,UAAU,GAAG;YAClB;QACJ;QACA,MAAM,EAAE,OAAO,EAAE,GAAG;QACpB,MAAM,+BAA+B,QAAQ,IAAI,EAAE,QAAQ,IAAI;QAC/D;;;;;;;;SAQC,GACD,IAAI,mBAAmB;QACvB,IAAI,IAAI,CAAC,SAAS,EACd,mBAAmB;QACvB,IAAI,CAAC,UAAU,GAAG,IAAI,QAAQ,OAAO,CAAC;YAClC,MAAM,QAAQ,IAAI;YAClB,MAAM,QAAQ,IAAI;YAClB,UAAU,QAAQ,QAAQ;YAC1B,UAAU,QAAQ,QAAQ;YAC1B,kBAAkB;YAClB,gBAAgB,CAAC,GAAG,OAAO,iBAAiB,EAAE,kBAAkB,QAAQ,cAAc;YACtF,aAAa;YACb,KAAK,QAAQ,GAAG;YAChB,4EAA4E;YAC5E,4EAA4E;YAC5E,8EAA8E;YAC9E,eAAe;QACnB;QACA,kEAAkE;QAClE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,QAAQ,IAAI;QACxC,2DAA2D;QAC3D,4DAA4D;QAC5D,8DAA8D;QAC9D,6DAA6D;QAC7D,8BAA8B;QAC9B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,eAAe;QAChD,iCAAiC;QACjC,MAAM,mBAAmB;YAAE,WAAW,EAAE;YAAE,YAAY,EAAE;YAAE,YAAY,EAAE;QAAC;QACzE,IAAI,sBAAsB;YACtB,MAAM,YAAY,qBAAqB,SAAS,IAAI,qBAAqB,aAAa;YACtF,IAAI,aAAa,UAAU,UAAU,EAAE;gBACnC,iBAAiB,SAAS,GAAG,UAAU,UAAU,CAAC,QAAQ,CAAC;gBAC3D,iBAAiB,UAAU,GACvB,UAAU,UAAU,CAAC,QAAQ,CAAC;gBAClC,iBAAiB,UAAU,GACvB,UAAU,UAAU,CAAC,QAAQ,CAAC;YACtC;QACJ;QACA,IAAI,iBAAiB,SAAS,CAAC,MAAM,IACjC,iBAAiB,UAAU,CAAC,MAAM,IAClC,iBAAiB,UAAU,CAAC,MAAM,EAAE;YACpC,IAAI,UAAU;YACd,KAAK,MAAM,QAAQ;gBAAC;gBAAa;gBAAc;aAAa,CAAE;gBAC1D,MAAM,WAAW,gBAAgB,CAAC,KAAK;gBACvC,IAAI,SAAS,MAAM,EAAE;oBACjB,WAAW;oBACX,MAAM,kBAAkB,MAAM,SAAS,MAAM;oBAC7C,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,UACjB,IAAI,CAAC;wBACN,IAAI,CAAC,EAAE,SAAS;4BACZ,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,UAAU;wBAC/C;oBACJ,GACK,KAAK,CAAC;wBACP,iEAAiE;wBACjE,MAAM,4BAA4B,MAAM,SAAS,MAAM;oBAC3D;gBACJ;YACJ;QACJ,OACK;YACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,UAAU;QAC/C;QACA,KAAK,MAAM,SAAS;YAChB;YACA;SACH,CAAE;YACC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM;gBAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,MAAM;YACnC;QACJ;QACA,KAAK,MAAM,SAAS;YAAC;YAAY;SAAiB,CAAE;YAChD,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,MAAM;gBACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,MAAM,MAAM;YACzC;QACJ;QACA,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM;YACxB,KAAK,MAAM,SAAS;gBAChB;gBACA;aACH,CAAE;gBACC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM;oBAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,MAAM;gBACnC;YACJ;QACJ;IACJ;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2144, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/cluster/ConnectionPool.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst events_1 = require(\"events\");\nconst utils_1 = require(\"../utils\");\nconst util_1 = require(\"./util\");\nconst Redis_1 = require(\"../Redis\");\nconst debug = (0, utils_1.Debug)(\"cluster:connectionPool\");\nclass ConnectionPool extends events_1.EventEmitter {\n    constructor(redisOptions) {\n        super();\n        this.redisOptions = redisOptions;\n        // master + slave = all\n        this.nodes = {\n            all: {},\n            master: {},\n            slave: {},\n        };\n        this.specifiedOptions = {};\n    }\n    getNodes(role = \"all\") {\n        const nodes = this.nodes[role];\n        return Object.keys(nodes).map((key) => nodes[key]);\n    }\n    getInstanceByKey(key) {\n        return this.nodes.all[key];\n    }\n    getSampleInstance(role) {\n        const keys = Object.keys(this.nodes[role]);\n        const sampleKey = (0, utils_1.sample)(keys);\n        return this.nodes[role][sampleKey];\n    }\n    /**\n     * Add a master node to the pool\n     * @param node\n     */\n    addMasterNode(node) {\n        const key = (0, util_1.getNodeKey)(node.options);\n        const redis = this.createRedisFromOptions(node, node.options.readOnly);\n        //Master nodes aren't read-only\n        if (!node.options.readOnly) {\n            this.nodes.all[key] = redis;\n            this.nodes.master[key] = redis;\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Creates a Redis connection instance from the node options\n     * @param node\n     * @param readOnly\n     */\n    createRedisFromOptions(node, readOnly) {\n        const redis = new Redis_1.default((0, utils_1.defaults)({\n            // Never try to reconnect when a node is lose,\n            // instead, waiting for a `MOVED` error and\n            // fetch the slots again.\n            retryStrategy: null,\n            // Offline queue should be enabled so that\n            // we don't need to wait for the `ready` event\n            // before sending commands to the node.\n            enableOfflineQueue: true,\n            readOnly: readOnly,\n        }, node, this.redisOptions, { lazyConnect: true }));\n        return redis;\n    }\n    /**\n     * Find or create a connection to the node\n     */\n    findOrCreate(node, readOnly = false) {\n        const key = (0, util_1.getNodeKey)(node);\n        readOnly = Boolean(readOnly);\n        if (this.specifiedOptions[key]) {\n            Object.assign(node, this.specifiedOptions[key]);\n        }\n        else {\n            this.specifiedOptions[key] = node;\n        }\n        let redis;\n        if (this.nodes.all[key]) {\n            redis = this.nodes.all[key];\n            if (redis.options.readOnly !== readOnly) {\n                redis.options.readOnly = readOnly;\n                debug(\"Change role of %s to %s\", key, readOnly ? \"slave\" : \"master\");\n                redis[readOnly ? \"readonly\" : \"readwrite\"]().catch(utils_1.noop);\n                if (readOnly) {\n                    delete this.nodes.master[key];\n                    this.nodes.slave[key] = redis;\n                }\n                else {\n                    delete this.nodes.slave[key];\n                    this.nodes.master[key] = redis;\n                }\n            }\n        }\n        else {\n            debug(\"Connecting to %s as %s\", key, readOnly ? \"slave\" : \"master\");\n            redis = this.createRedisFromOptions(node, readOnly);\n            this.nodes.all[key] = redis;\n            this.nodes[readOnly ? \"slave\" : \"master\"][key] = redis;\n            redis.once(\"end\", () => {\n                this.removeNode(key);\n                this.emit(\"-node\", redis, key);\n                if (!Object.keys(this.nodes.all).length) {\n                    this.emit(\"drain\");\n                }\n            });\n            this.emit(\"+node\", redis, key);\n            redis.on(\"error\", function (error) {\n                this.emit(\"nodeError\", error, key);\n            });\n        }\n        return redis;\n    }\n    /**\n     * Reset the pool with a set of nodes.\n     * The old node will be removed.\n     */\n    reset(nodes) {\n        debug(\"Reset with %O\", nodes);\n        const newNodes = {};\n        nodes.forEach((node) => {\n            const key = (0, util_1.getNodeKey)(node);\n            // Don't override the existing (master) node\n            // when the current one is slave.\n            if (!(node.readOnly && newNodes[key])) {\n                newNodes[key] = node;\n            }\n        });\n        Object.keys(this.nodes.all).forEach((key) => {\n            if (!newNodes[key]) {\n                debug(\"Disconnect %s because the node does not hold any slot\", key);\n                this.nodes.all[key].disconnect();\n                this.removeNode(key);\n            }\n        });\n        Object.keys(newNodes).forEach((key) => {\n            const node = newNodes[key];\n            this.findOrCreate(node, node.readOnly);\n        });\n    }\n    /**\n     * Remove a node from the pool.\n     */\n    removeNode(key) {\n        const { nodes } = this;\n        if (nodes.all[key]) {\n            debug(\"Remove %s from the pool\", key);\n            delete nodes.all[key];\n        }\n        delete nodes.master[key];\n        delete nodes.slave[key];\n    }\n}\nexports.default = ConnectionPool;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAQ,KAAK,EAAE;AACjC,MAAM,uBAAuB,SAAS,YAAY;IAC9C,YAAY,YAAY,CAAE;QACtB,KAAK;QACL,IAAI,CAAC,YAAY,GAAG;QACpB,uBAAuB;QACvB,IAAI,CAAC,KAAK,GAAG;YACT,KAAK,CAAC;YACN,QAAQ,CAAC;YACT,OAAO,CAAC;QACZ;QACA,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,SAAS,OAAO,KAAK,EAAE;QACnB,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK;QAC9B,OAAO,OAAO,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,MAAQ,KAAK,CAAC,IAAI;IACrD;IACA,iBAAiB,GAAG,EAAE;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;IAC9B;IACA,kBAAkB,IAAI,EAAE;QACpB,MAAM,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;QACzC,MAAM,YAAY,CAAC,GAAG,QAAQ,MAAM,EAAE;QACtC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU;IACtC;IACA;;;KAGC,GACD,cAAc,IAAI,EAAE;QAChB,MAAM,MAAM,CAAC,GAAG,OAAO,UAAU,EAAE,KAAK,OAAO;QAC/C,MAAM,QAAQ,IAAI,CAAC,sBAAsB,CAAC,MAAM,KAAK,OAAO,CAAC,QAAQ;QACrE,+BAA+B;QAC/B,IAAI,CAAC,KAAK,OAAO,CAAC,QAAQ,EAAE;YACxB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG;YACtB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,GAAG;YACzB,OAAO;QACX;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,uBAAuB,IAAI,EAAE,QAAQ,EAAE;QACnC,MAAM,QAAQ,IAAI,QAAQ,OAAO,CAAC,CAAC,GAAG,QAAQ,QAAQ,EAAE;YACpD,8CAA8C;YAC9C,2CAA2C;YAC3C,yBAAyB;YACzB,eAAe;YACf,0CAA0C;YAC1C,8CAA8C;YAC9C,uCAAuC;YACvC,oBAAoB;YACpB,UAAU;QACd,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE;YAAE,aAAa;QAAK;QAChD,OAAO;IACX;IACA;;KAEC,GACD,aAAa,IAAI,EAAE,WAAW,KAAK,EAAE;QACjC,MAAM,MAAM,CAAC,GAAG,OAAO,UAAU,EAAE;QACnC,WAAW,QAAQ;QACnB,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE;YAC5B,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI;QAClD,OACK;YACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAAG;QACjC;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE;YACrB,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;YAC3B,IAAI,MAAM,OAAO,CAAC,QAAQ,KAAK,UAAU;gBACrC,MAAM,OAAO,CAAC,QAAQ,GAAG;gBACzB,MAAM,2BAA2B,KAAK,WAAW,UAAU;gBAC3D,KAAK,CAAC,WAAW,aAAa,YAAY,GAAG,KAAK,CAAC,QAAQ,IAAI;gBAC/D,IAAI,UAAU;oBACV,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI;oBAC7B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG;gBAC5B,OACK;oBACD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI;oBAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,GAAG;gBAC7B;YACJ;QACJ,OACK;YACD,MAAM,0BAA0B,KAAK,WAAW,UAAU;YAC1D,QAAQ,IAAI,CAAC,sBAAsB,CAAC,MAAM;YAC1C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG;YACtB,IAAI,CAAC,KAAK,CAAC,WAAW,UAAU,SAAS,CAAC,IAAI,GAAG;YACjD,MAAM,IAAI,CAAC,OAAO;gBACd,IAAI,CAAC,UAAU,CAAC;gBAChB,IAAI,CAAC,IAAI,CAAC,SAAS,OAAO;gBAC1B,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE;oBACrC,IAAI,CAAC,IAAI,CAAC;gBACd;YACJ;YACA,IAAI,CAAC,IAAI,CAAC,SAAS,OAAO;YAC1B,MAAM,EAAE,CAAC,SAAS,SAAU,KAAK;gBAC7B,IAAI,CAAC,IAAI,CAAC,aAAa,OAAO;YAClC;QACJ;QACA,OAAO;IACX;IACA;;;KAGC,GACD,MAAM,KAAK,EAAE;QACT,MAAM,iBAAiB;QACvB,MAAM,WAAW,CAAC;QAClB,MAAM,OAAO,CAAC,CAAC;YACX,MAAM,MAAM,CAAC,GAAG,OAAO,UAAU,EAAE;YACnC,4CAA4C;YAC5C,iCAAiC;YACjC,IAAI,CAAC,CAAC,KAAK,QAAQ,IAAI,QAAQ,CAAC,IAAI,GAAG;gBACnC,QAAQ,CAAC,IAAI,GAAG;YACpB;QACJ;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACjC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;gBAChB,MAAM,yDAAyD;gBAC/D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU;gBAC9B,IAAI,CAAC,UAAU,CAAC;YACpB;QACJ;QACA,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,CAAC;YAC3B,MAAM,OAAO,QAAQ,CAAC,IAAI;YAC1B,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,QAAQ;QACzC;IACJ;IACA;;KAEC,GACD,WAAW,GAAG,EAAE;QACZ,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;QACtB,IAAI,MAAM,GAAG,CAAC,IAAI,EAAE;YAChB,MAAM,2BAA2B;YACjC,OAAO,MAAM,GAAG,CAAC,IAAI;QACzB;QACA,OAAO,MAAM,MAAM,CAAC,IAAI;QACxB,OAAO,MAAM,KAAK,CAAC,IAAI;IAC3B;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2299, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/cluster/DelayQueue.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst utils_1 = require(\"../utils\");\nconst Deque = require(\"denque\");\nconst debug = (0, utils_1.Debug)(\"delayqueue\");\n/**\n * Queue that runs items after specified duration\n */\nclass DelayQueue {\n    constructor() {\n        this.queues = {};\n        this.timeouts = {};\n    }\n    /**\n     * Add a new item to the queue\n     *\n     * @param bucket bucket name\n     * @param item function that will run later\n     * @param options\n     */\n    push(bucket, item, options) {\n        const callback = options.callback || process.nextTick;\n        if (!this.queues[bucket]) {\n            this.queues[bucket] = new Deque();\n        }\n        const queue = this.queues[bucket];\n        queue.push(item);\n        if (!this.timeouts[bucket]) {\n            this.timeouts[bucket] = setTimeout(() => {\n                callback(() => {\n                    this.timeouts[bucket] = null;\n                    this.execute(bucket);\n                });\n            }, options.timeout);\n        }\n    }\n    execute(bucket) {\n        const queue = this.queues[bucket];\n        if (!queue) {\n            return;\n        }\n        const { length } = queue;\n        if (!length) {\n            return;\n        }\n        debug(\"send %d commands in %s queue\", length, bucket);\n        this.queues[bucket] = null;\n        while (queue.length > 0) {\n            queue.shift()();\n        }\n    }\n}\nexports.default = DelayQueue;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAQ,KAAK,EAAE;AACjC;;CAEC,GACD,MAAM;IACF,aAAc;QACV,IAAI,CAAC,MAAM,GAAG,CAAC;QACf,IAAI,CAAC,QAAQ,GAAG,CAAC;IACrB;IACA;;;;;;KAMC,GACD,KAAK,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE;QACxB,MAAM,WAAW,QAAQ,QAAQ,IAAI,QAAQ,QAAQ;QACrD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI;QAC9B;QACA,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO;QACjC,MAAM,IAAI,CAAC;QACX,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;YACxB,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,WAAW;gBAC/B,SAAS;oBACL,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG;oBACxB,IAAI,CAAC,OAAO,CAAC;gBACjB;YACJ,GAAG,QAAQ,OAAO;QACtB;IACJ;IACA,QAAQ,MAAM,EAAE;QACZ,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO;QACjC,IAAI,CAAC,OAAO;YACR;QACJ;QACA,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,IAAI,CAAC,QAAQ;YACT;QACJ;QACA,MAAM,gCAAgC,QAAQ;QAC9C,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG;QACtB,MAAO,MAAM,MAAM,GAAG,EAAG;YACrB,MAAM,KAAK;QACf;IACJ;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2357, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/cluster/ClusterSubscriberGroup.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst utils_1 = require(\"../utils\");\nconst ClusterSubscriber_1 = require(\"./ClusterSubscriber\");\nconst ConnectionPool_1 = require(\"./ConnectionPool\");\nconst util_1 = require(\"./util\");\nconst calculateSlot = require(\"cluster-key-slot\");\nconst debug = (0, utils_1.Debug)(\"cluster:subscriberGroup\");\n/**\n * Redis differs between \"normal\" and sharded PubSub. If using the \"normal\" PubSub feature, exactly one\n * ClusterSubscriber exists per cluster instance. This works because the Redis cluster bus forwards m\n * messages between shards. However, this has scalability limitations, which is the reason why the sharded\n * PubSub feature was added to Redis. With sharded PubSub, each shard is responsible for its own messages.\n * Given that, we need at least one ClusterSubscriber per master endpoint/node.\n *\n * This class leverages the previously exising ClusterSubscriber by adding support for multiple such subscribers\n * in alignment to the master nodes of the cluster. The ClusterSubscriber class was extended in a non-breaking way\n * to support this feature.\n */\nclass ClusterSubscriberGroup {\n    /**\n     * Register callbacks\n     *\n     * @param cluster\n     */\n    constructor(cluster) {\n        this.cluster = cluster;\n        this.shardedSubscribers = new Map();\n        this.clusterSlots = [];\n        //Simple [min, max] slot ranges aren't enough because you can migrate single slots\n        this.subscriberToSlotsIndex = new Map();\n        this.channels = new Map();\n        cluster.on(\"+node\", (redis) => {\n            this._addSubscriber(redis);\n        });\n        cluster.on(\"-node\", (redis) => {\n            this._removeSubscriber(redis);\n        });\n        cluster.on(\"refresh\", () => {\n            this._refreshSlots(cluster);\n        });\n    }\n    /**\n     * Get the responsible subscriber.\n     *\n     * Returns null if no subscriber was found\n     *\n     * @param slot\n     */\n    getResponsibleSubscriber(slot) {\n        const nodeKey = this.clusterSlots[slot][0];\n        return this.shardedSubscribers.get(nodeKey);\n    }\n    /**\n     * Adds a channel for which this subscriber group is responsible\n     *\n     * @param channels\n     */\n    addChannels(channels) {\n        const slot = calculateSlot(channels[0]);\n        //Check if the all channels belong to the same slot and otherwise reject the operation\n        channels.forEach((c) => {\n            if (calculateSlot(c) != slot)\n                return -1;\n        });\n        const currChannels = this.channels.get(slot);\n        if (!currChannels) {\n            this.channels.set(slot, channels);\n        }\n        else {\n            this.channels.set(slot, currChannels.concat(channels));\n        }\n        return [...this.channels.values()].flatMap(v => v).length;\n    }\n    /**\n     * Removes channels for which the subscriber group is responsible by optionally unsubscribing\n     * @param channels\n     */\n    removeChannels(channels) {\n        const slot = calculateSlot(channels[0]);\n        //Check if the all channels belong to the same slot and otherwise reject the operation\n        channels.forEach((c) => {\n            if (calculateSlot(c) != slot)\n                return -1;\n        });\n        const slotChannels = this.channels.get(slot);\n        if (slotChannels) {\n            const updatedChannels = slotChannels.filter(c => !channels.includes(c));\n            this.channels.set(slot, updatedChannels);\n        }\n        return [...this.channels.values()].flatMap(v => v).length;\n    }\n    /**\n     * Disconnect all subscribers\n     */\n    stop() {\n        for (const s of this.shardedSubscribers.values()) {\n            s.stop();\n        }\n    }\n    /**\n     * Start all not yet started subscribers\n     */\n    start() {\n        for (const s of this.shardedSubscribers.values()) {\n            if (!s.isStarted()) {\n                s.start();\n            }\n        }\n    }\n    /**\n     * Add a subscriber to the group of subscribers\n     *\n     * @param redis\n     */\n    _addSubscriber(redis) {\n        const pool = new ConnectionPool_1.default(redis.options);\n        if (pool.addMasterNode(redis)) {\n            const sub = new ClusterSubscriber_1.default(pool, this.cluster, true);\n            const nodeKey = (0, util_1.getNodeKey)(redis.options);\n            this.shardedSubscribers.set(nodeKey, sub);\n            sub.start();\n            // We need to attempt to resubscribe them in case the new node serves their slot\n            this._resubscribe();\n            this.cluster.emit(\"+subscriber\");\n            return sub;\n        }\n        return null;\n    }\n    /**\n     * Removes a subscriber from the group\n     * @param redis\n     */\n    _removeSubscriber(redis) {\n        const nodeKey = (0, util_1.getNodeKey)(redis.options);\n        const sub = this.shardedSubscribers.get(nodeKey);\n        if (sub) {\n            sub.stop();\n            this.shardedSubscribers.delete(nodeKey);\n            // Even though the subscriber to this node is going down, we might have another subscriber\n            // handling the same slots, so we need to attempt to subscribe the orphaned channels\n            this._resubscribe();\n            this.cluster.emit(\"-subscriber\");\n        }\n        return this.shardedSubscribers;\n    }\n    /**\n     * Refreshes the subscriber-related slot ranges\n     *\n     * Returns false if no refresh was needed\n     *\n     * @param cluster\n     */\n    _refreshSlots(cluster) {\n        //If there was an actual change, then reassign the slot ranges\n        if (this._slotsAreEqual(cluster.slots)) {\n            debug(\"Nothing to refresh because the new cluster map is equal to the previous one.\");\n        }\n        else {\n            debug(\"Refreshing the slots of the subscriber group.\");\n            //Rebuild the slots index\n            this.subscriberToSlotsIndex = new Map();\n            for (let slot = 0; slot < cluster.slots.length; slot++) {\n                const node = cluster.slots[slot][0];\n                if (!this.subscriberToSlotsIndex.has(node)) {\n                    this.subscriberToSlotsIndex.set(node, []);\n                }\n                this.subscriberToSlotsIndex.get(node).push(Number(slot));\n            }\n            //Update the subscribers from the index\n            this._resubscribe();\n            //Update the cached slots map\n            this.clusterSlots = JSON.parse(JSON.stringify(cluster.slots));\n            this.cluster.emit(\"subscribersReady\");\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Resubscribes to the previous channels\n     *\n     * @private\n     */\n    _resubscribe() {\n        if (this.shardedSubscribers) {\n            this.shardedSubscribers.forEach((s, nodeKey) => {\n                const subscriberSlots = this.subscriberToSlotsIndex.get(nodeKey);\n                if (subscriberSlots) {\n                    //More for debugging purposes\n                    s.associateSlotRange(subscriberSlots);\n                    //Resubscribe on the underlying connection\n                    subscriberSlots.forEach((ss) => {\n                        //Might return null if being disconnected\n                        const redis = s.getInstance();\n                        const channels = this.channels.get(ss);\n                        if (channels && channels.length > 0) {\n                            //Try to subscribe now\n                            if (redis) {\n                                redis.ssubscribe(channels);\n                                //If the instance isn't ready yet, then register the re-subscription for later\n                                redis.on(\"ready\", () => {\n                                    redis.ssubscribe(channels);\n                                });\n                            }\n                        }\n                    });\n                }\n            });\n        }\n    }\n    /**\n     * Deep equality of the cluster slots objects\n     *\n     * @param other\n     * @private\n     */\n    _slotsAreEqual(other) {\n        if (this.clusterSlots === undefined)\n            return false;\n        else\n            return JSON.stringify(this.clusterSlots) === JSON.stringify(other);\n    }\n}\nexports.default = ClusterSubscriberGroup;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAQ,KAAK,EAAE;AACjC;;;;;;;;;;CAUC,GACD,MAAM;IACF;;;;KAIC,GACD,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAAC,YAAY,GAAG,EAAE;QACtB,kFAAkF;QAClF,IAAI,CAAC,sBAAsB,GAAG,IAAI;QAClC,IAAI,CAAC,QAAQ,GAAG,IAAI;QACpB,QAAQ,EAAE,CAAC,SAAS,CAAC;YACjB,IAAI,CAAC,cAAc,CAAC;QACxB;QACA,QAAQ,EAAE,CAAC,SAAS,CAAC;YACjB,IAAI,CAAC,iBAAiB,CAAC;QAC3B;QACA,QAAQ,EAAE,CAAC,WAAW;YAClB,IAAI,CAAC,aAAa,CAAC;QACvB;IACJ;IACA;;;;;;KAMC,GACD,yBAAyB,IAAI,EAAE;QAC3B,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;QAC1C,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;IACvC;IACA;;;;KAIC,GACD,YAAY,QAAQ,EAAE;QAClB,MAAM,OAAO,cAAc,QAAQ,CAAC,EAAE;QACtC,sFAAsF;QACtF,SAAS,OAAO,CAAC,CAAC;YACd,IAAI,cAAc,MAAM,MACpB,OAAO,CAAC;QAChB;QACA,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,cAAc;YACf,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM;QAC5B,OACK;YACD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,aAAa,MAAM,CAAC;QAChD;QACA,OAAO;eAAI,IAAI,CAAC,QAAQ,CAAC,MAAM;SAAG,CAAC,OAAO,CAAC,CAAA,IAAK,GAAG,MAAM;IAC7D;IACA;;;KAGC,GACD,eAAe,QAAQ,EAAE;QACrB,MAAM,OAAO,cAAc,QAAQ,CAAC,EAAE;QACtC,sFAAsF;QACtF,SAAS,OAAO,CAAC,CAAC;YACd,IAAI,cAAc,MAAM,MACpB,OAAO,CAAC;QAChB;QACA,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QACvC,IAAI,cAAc;YACd,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAA,IAAK,CAAC,SAAS,QAAQ,CAAC;YACpE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM;QAC5B;QACA,OAAO;eAAI,IAAI,CAAC,QAAQ,CAAC,MAAM;SAAG,CAAC,OAAO,CAAC,CAAA,IAAK,GAAG,MAAM;IAC7D;IACA;;KAEC,GACD,OAAO;QACH,KAAK,MAAM,KAAK,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAI;YAC9C,EAAE,IAAI;QACV;IACJ;IACA;;KAEC,GACD,QAAQ;QACJ,KAAK,MAAM,KAAK,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAI;YAC9C,IAAI,CAAC,EAAE,SAAS,IAAI;gBAChB,EAAE,KAAK;YACX;QACJ;IACJ;IACA;;;;KAIC,GACD,eAAe,KAAK,EAAE;QAClB,MAAM,OAAO,IAAI,iBAAiB,OAAO,CAAC,MAAM,OAAO;QACvD,IAAI,KAAK,aAAa,CAAC,QAAQ;YAC3B,MAAM,MAAM,IAAI,oBAAoB,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE;YAChE,MAAM,UAAU,CAAC,GAAG,OAAO,UAAU,EAAE,MAAM,OAAO;YACpD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS;YACrC,IAAI,KAAK;YACT,gFAAgF;YAChF,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YAClB,OAAO;QACX;QACA,OAAO;IACX;IACA;;;KAGC,GACD,kBAAkB,KAAK,EAAE;QACrB,MAAM,UAAU,CAAC,GAAG,OAAO,UAAU,EAAE,MAAM,OAAO;QACpD,MAAM,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;QACxC,IAAI,KAAK;YACL,IAAI,IAAI;YACR,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC/B,0FAA0F;YAC1F,oFAAoF;YACpF,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QACtB;QACA,OAAO,IAAI,CAAC,kBAAkB;IAClC;IACA;;;;;;KAMC,GACD,cAAc,OAAO,EAAE;QACnB,8DAA8D;QAC9D,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,KAAK,GAAG;YACpC,MAAM;QACV,OACK;YACD,MAAM;YACN,yBAAyB;YACzB,IAAI,CAAC,sBAAsB,GAAG,IAAI;YAClC,IAAK,IAAI,OAAO,GAAG,OAAO,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAQ;gBACpD,MAAM,OAAO,QAAQ,KAAK,CAAC,KAAK,CAAC,EAAE;gBACnC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,OAAO;oBACxC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,MAAM,EAAE;gBAC5C;gBACA,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,OAAO;YACtD;YACA,uCAAuC;YACvC,IAAI,CAAC,YAAY;YACjB,6BAA6B;YAC7B,IAAI,CAAC,YAAY,GAAG,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,QAAQ,KAAK;YAC3D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YAClB,OAAO;QACX;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,eAAe;QACX,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,GAAG;gBAChC,MAAM,kBAAkB,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC;gBACxD,IAAI,iBAAiB;oBACjB,6BAA6B;oBAC7B,EAAE,kBAAkB,CAAC;oBACrB,0CAA0C;oBAC1C,gBAAgB,OAAO,CAAC,CAAC;wBACrB,yCAAyC;wBACzC,MAAM,QAAQ,EAAE,WAAW;wBAC3B,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;wBACnC,IAAI,YAAY,SAAS,MAAM,GAAG,GAAG;4BACjC,sBAAsB;4BACtB,IAAI,OAAO;gCACP,MAAM,UAAU,CAAC;gCACjB,8EAA8E;gCAC9E,MAAM,EAAE,CAAC,SAAS;oCACd,MAAM,UAAU,CAAC;gCACrB;4BACJ;wBACJ;oBACJ;gBACJ;YACJ;QACJ;IACJ;IACA;;;;;KAKC,GACD,eAAe,KAAK,EAAE;QAClB,IAAI,IAAI,CAAC,YAAY,KAAK,WACtB,OAAO;aAEP,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,YAAY,MAAM,KAAK,SAAS,CAAC;IACpE;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2574, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/cluster/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst commands_1 = require(\"@ioredis/commands\");\nconst events_1 = require(\"events\");\nconst redis_errors_1 = require(\"redis-errors\");\nconst standard_as_callback_1 = require(\"standard-as-callback\");\nconst Command_1 = require(\"../Command\");\nconst ClusterAllFailedError_1 = require(\"../errors/ClusterAllFailedError\");\nconst Redis_1 = require(\"../Redis\");\nconst ScanStream_1 = require(\"../ScanStream\");\nconst transaction_1 = require(\"../transaction\");\nconst utils_1 = require(\"../utils\");\nconst applyMixin_1 = require(\"../utils/applyMixin\");\nconst Commander_1 = require(\"../utils/Commander\");\nconst ClusterOptions_1 = require(\"./ClusterOptions\");\nconst ClusterSubscriber_1 = require(\"./ClusterSubscriber\");\nconst ConnectionPool_1 = require(\"./ConnectionPool\");\nconst DelayQueue_1 = require(\"./DelayQueue\");\nconst util_1 = require(\"./util\");\nconst Deque = require(\"denque\");\nconst ClusterSubscriberGroup_1 = require(\"./ClusterSubscriberGroup\");\nconst debug = (0, utils_1.Debug)(\"cluster\");\nconst REJECT_OVERWRITTEN_COMMANDS = new WeakSet();\n/**\n * Client for the official Redis Cluster\n */\nclass Cluster extends Commander_1.default {\n    /**\n     * Creates an instance of Cluster.\n     */\n    //TODO: Add an option that enables or disables sharded PubSub\n    constructor(startupNodes, options = {}) {\n        super();\n        this.slots = [];\n        /**\n         * @ignore\n         */\n        this._groupsIds = {};\n        /**\n         * @ignore\n         */\n        this._groupsBySlot = Array(16384);\n        /**\n         * @ignore\n         */\n        this.isCluster = true;\n        this.retryAttempts = 0;\n        this.delayQueue = new DelayQueue_1.default();\n        this.offlineQueue = new Deque();\n        this.isRefreshing = false;\n        this._refreshSlotsCacheCallbacks = [];\n        this._autoPipelines = new Map();\n        this._runningAutoPipelines = new Set();\n        this._readyDelayedCallbacks = [];\n        /**\n         * Every time Cluster#connect() is called, this value will be\n         * auto-incrementing. The purpose of this value is used for\n         * discarding previous connect attampts when creating a new\n         * connection.\n         */\n        this.connectionEpoch = 0;\n        events_1.EventEmitter.call(this);\n        this.startupNodes = startupNodes;\n        this.options = (0, utils_1.defaults)({}, options, ClusterOptions_1.DEFAULT_CLUSTER_OPTIONS, this.options);\n        if (this.options.shardedSubscribers == true)\n            this.shardedSubscribers = new ClusterSubscriberGroup_1.default(this);\n        if (this.options.redisOptions &&\n            this.options.redisOptions.keyPrefix &&\n            !this.options.keyPrefix) {\n            this.options.keyPrefix = this.options.redisOptions.keyPrefix;\n        }\n        // validate options\n        if (typeof this.options.scaleReads !== \"function\" &&\n            [\"all\", \"master\", \"slave\"].indexOf(this.options.scaleReads) === -1) {\n            throw new Error('Invalid option scaleReads \"' +\n                this.options.scaleReads +\n                '\". Expected \"all\", \"master\", \"slave\" or a custom function');\n        }\n        this.connectionPool = new ConnectionPool_1.default(this.options.redisOptions);\n        this.connectionPool.on(\"-node\", (redis, key) => {\n            this.emit(\"-node\", redis);\n        });\n        this.connectionPool.on(\"+node\", (redis) => {\n            this.emit(\"+node\", redis);\n        });\n        this.connectionPool.on(\"drain\", () => {\n            this.setStatus(\"close\");\n        });\n        this.connectionPool.on(\"nodeError\", (error, key) => {\n            this.emit(\"node error\", error, key);\n        });\n        this.subscriber = new ClusterSubscriber_1.default(this.connectionPool, this);\n        if (this.options.scripts) {\n            Object.entries(this.options.scripts).forEach(([name, definition]) => {\n                this.defineCommand(name, definition);\n            });\n        }\n        if (this.options.lazyConnect) {\n            this.setStatus(\"wait\");\n        }\n        else {\n            this.connect().catch((err) => {\n                debug(\"connecting failed: %s\", err);\n            });\n        }\n    }\n    /**\n     * Connect to a cluster\n     */\n    connect() {\n        return new Promise((resolve, reject) => {\n            if (this.status === \"connecting\" ||\n                this.status === \"connect\" ||\n                this.status === \"ready\") {\n                reject(new Error(\"Redis is already connecting/connected\"));\n                return;\n            }\n            const epoch = ++this.connectionEpoch;\n            this.setStatus(\"connecting\");\n            this.resolveStartupNodeHostnames()\n                .then((nodes) => {\n                if (this.connectionEpoch !== epoch) {\n                    debug(\"discard connecting after resolving startup nodes because epoch not match: %d != %d\", epoch, this.connectionEpoch);\n                    reject(new redis_errors_1.RedisError(\"Connection is discarded because a new connection is made\"));\n                    return;\n                }\n                if (this.status !== \"connecting\") {\n                    debug(\"discard connecting after resolving startup nodes because the status changed to %s\", this.status);\n                    reject(new redis_errors_1.RedisError(\"Connection is aborted\"));\n                    return;\n                }\n                this.connectionPool.reset(nodes);\n                const readyHandler = () => {\n                    this.setStatus(\"ready\");\n                    this.retryAttempts = 0;\n                    this.executeOfflineCommands();\n                    this.resetNodesRefreshInterval();\n                    resolve();\n                };\n                let closeListener = undefined;\n                const refreshListener = () => {\n                    this.invokeReadyDelayedCallbacks(undefined);\n                    this.removeListener(\"close\", closeListener);\n                    this.manuallyClosing = false;\n                    this.setStatus(\"connect\");\n                    if (this.options.enableReadyCheck) {\n                        this.readyCheck((err, fail) => {\n                            if (err || fail) {\n                                debug(\"Ready check failed (%s). Reconnecting...\", err || fail);\n                                if (this.status === \"connect\") {\n                                    this.disconnect(true);\n                                }\n                            }\n                            else {\n                                readyHandler();\n                            }\n                        });\n                    }\n                    else {\n                        readyHandler();\n                    }\n                };\n                closeListener = () => {\n                    const error = new Error(\"None of startup nodes is available\");\n                    this.removeListener(\"refresh\", refreshListener);\n                    this.invokeReadyDelayedCallbacks(error);\n                    reject(error);\n                };\n                this.once(\"refresh\", refreshListener);\n                this.once(\"close\", closeListener);\n                this.once(\"close\", this.handleCloseEvent.bind(this));\n                this.refreshSlotsCache((err) => {\n                    if (err && err.message === ClusterAllFailedError_1.default.defaultMessage) {\n                        Redis_1.default.prototype.silentEmit.call(this, \"error\", err);\n                        this.connectionPool.reset([]);\n                    }\n                });\n                this.subscriber.start();\n                if (this.options.shardedSubscribers) {\n                    this.shardedSubscribers.start();\n                }\n            })\n                .catch((err) => {\n                this.setStatus(\"close\");\n                this.handleCloseEvent(err);\n                this.invokeReadyDelayedCallbacks(err);\n                reject(err);\n            });\n        });\n    }\n    /**\n     * Disconnect from every node in the cluster.\n     */\n    disconnect(reconnect = false) {\n        const status = this.status;\n        this.setStatus(\"disconnecting\");\n        if (!reconnect) {\n            this.manuallyClosing = true;\n        }\n        if (this.reconnectTimeout && !reconnect) {\n            clearTimeout(this.reconnectTimeout);\n            this.reconnectTimeout = null;\n            debug(\"Canceled reconnecting attempts\");\n        }\n        this.clearNodesRefreshInterval();\n        this.subscriber.stop();\n        if (this.options.shardedSubscribers) {\n            this.shardedSubscribers.stop();\n        }\n        if (status === \"wait\") {\n            this.setStatus(\"close\");\n            this.handleCloseEvent();\n        }\n        else {\n            this.connectionPool.reset([]);\n        }\n    }\n    /**\n     * Quit the cluster gracefully.\n     */\n    quit(callback) {\n        const status = this.status;\n        this.setStatus(\"disconnecting\");\n        this.manuallyClosing = true;\n        if (this.reconnectTimeout) {\n            clearTimeout(this.reconnectTimeout);\n            this.reconnectTimeout = null;\n        }\n        this.clearNodesRefreshInterval();\n        this.subscriber.stop();\n        if (this.options.shardedSubscribers) {\n            this.shardedSubscribers.stop();\n        }\n        if (status === \"wait\") {\n            const ret = (0, standard_as_callback_1.default)(Promise.resolve(\"OK\"), callback);\n            // use setImmediate to make sure \"close\" event\n            // being emitted after quit() is returned\n            setImmediate(function () {\n                this.setStatus(\"close\");\n                this.handleCloseEvent();\n            }.bind(this));\n            return ret;\n        }\n        return (0, standard_as_callback_1.default)(Promise.all(this.nodes().map((node) => node.quit().catch((err) => {\n            // Ignore the error caused by disconnecting since\n            // we're disconnecting...\n            if (err.message === utils_1.CONNECTION_CLOSED_ERROR_MSG) {\n                return \"OK\";\n            }\n            throw err;\n        }))).then(() => \"OK\"), callback);\n    }\n    /**\n     * Create a new instance with the same startup nodes and options as the current one.\n     *\n     * @example\n     * ```js\n     * var cluster = new Redis.Cluster([{ host: \"127.0.0.1\", port: \"30001\" }]);\n     * var anotherCluster = cluster.duplicate();\n     * ```\n     */\n    duplicate(overrideStartupNodes = [], overrideOptions = {}) {\n        const startupNodes = overrideStartupNodes.length > 0\n            ? overrideStartupNodes\n            : this.startupNodes.slice(0);\n        const options = Object.assign({}, this.options, overrideOptions);\n        return new Cluster(startupNodes, options);\n    }\n    /**\n     * Get nodes with the specified role\n     */\n    nodes(role = \"all\") {\n        if (role !== \"all\" && role !== \"master\" && role !== \"slave\") {\n            throw new Error('Invalid role \"' + role + '\". Expected \"all\", \"master\" or \"slave\"');\n        }\n        return this.connectionPool.getNodes(role);\n    }\n    /**\n     * This is needed in order not to install a listener for each auto pipeline\n     *\n     * @ignore\n     */\n    delayUntilReady(callback) {\n        this._readyDelayedCallbacks.push(callback);\n    }\n    /**\n     * Get the number of commands queued in automatic pipelines.\n     *\n     * This is not available (and returns 0) until the cluster is connected and slots information have been received.\n     */\n    get autoPipelineQueueSize() {\n        let queued = 0;\n        for (const pipeline of this._autoPipelines.values()) {\n            queued += pipeline.length;\n        }\n        return queued;\n    }\n    /**\n     * Refresh the slot cache\n     *\n     * @ignore\n     */\n    refreshSlotsCache(callback) {\n        if (callback) {\n            this._refreshSlotsCacheCallbacks.push(callback);\n        }\n        if (this.isRefreshing) {\n            return;\n        }\n        this.isRefreshing = true;\n        const _this = this;\n        const wrapper = (error) => {\n            this.isRefreshing = false;\n            for (const callback of this._refreshSlotsCacheCallbacks) {\n                callback(error);\n            }\n            this._refreshSlotsCacheCallbacks = [];\n        };\n        const nodes = (0, utils_1.shuffle)(this.connectionPool.getNodes());\n        let lastNodeError = null;\n        function tryNode(index) {\n            if (index === nodes.length) {\n                const error = new ClusterAllFailedError_1.default(ClusterAllFailedError_1.default.defaultMessage, lastNodeError);\n                return wrapper(error);\n            }\n            const node = nodes[index];\n            const key = `${node.options.host}:${node.options.port}`;\n            debug(\"getting slot cache from %s\", key);\n            _this.getInfoFromNode(node, function (err) {\n                switch (_this.status) {\n                    case \"close\":\n                    case \"end\":\n                        return wrapper(new Error(\"Cluster is disconnected.\"));\n                    case \"disconnecting\":\n                        return wrapper(new Error(\"Cluster is disconnecting.\"));\n                }\n                if (err) {\n                    _this.emit(\"node error\", err, key);\n                    lastNodeError = err;\n                    tryNode(index + 1);\n                }\n                else {\n                    _this.emit(\"refresh\");\n                    wrapper();\n                }\n            });\n        }\n        tryNode(0);\n    }\n    /**\n     * @ignore\n     */\n    sendCommand(command, stream, node) {\n        if (this.status === \"wait\") {\n            this.connect().catch(utils_1.noop);\n        }\n        if (this.status === \"end\") {\n            command.reject(new Error(utils_1.CONNECTION_CLOSED_ERROR_MSG));\n            return command.promise;\n        }\n        let to = this.options.scaleReads;\n        if (to !== \"master\") {\n            const isCommandReadOnly = command.isReadOnly ||\n                ((0, commands_1.exists)(command.name) && (0, commands_1.hasFlag)(command.name, \"readonly\"));\n            if (!isCommandReadOnly) {\n                to = \"master\";\n            }\n        }\n        let targetSlot = node ? node.slot : command.getSlot();\n        const ttl = {};\n        const _this = this;\n        if (!node && !REJECT_OVERWRITTEN_COMMANDS.has(command)) {\n            REJECT_OVERWRITTEN_COMMANDS.add(command);\n            const reject = command.reject;\n            command.reject = function (err) {\n                const partialTry = tryConnection.bind(null, true);\n                _this.handleError(err, ttl, {\n                    moved: function (slot, key) {\n                        debug(\"command %s is moved to %s\", command.name, key);\n                        targetSlot = Number(slot);\n                        if (_this.slots[slot]) {\n                            _this.slots[slot][0] = key;\n                        }\n                        else {\n                            _this.slots[slot] = [key];\n                        }\n                        _this._groupsBySlot[slot] =\n                            _this._groupsIds[_this.slots[slot].join(\";\")];\n                        _this.connectionPool.findOrCreate(_this.natMapper(key));\n                        tryConnection();\n                        debug(\"refreshing slot caches... (triggered by MOVED error)\");\n                        _this.refreshSlotsCache();\n                    },\n                    ask: function (slot, key) {\n                        debug(\"command %s is required to ask %s:%s\", command.name, key);\n                        const mapped = _this.natMapper(key);\n                        _this.connectionPool.findOrCreate(mapped);\n                        tryConnection(false, `${mapped.host}:${mapped.port}`);\n                    },\n                    tryagain: partialTry,\n                    clusterDown: partialTry,\n                    connectionClosed: partialTry,\n                    maxRedirections: function (redirectionError) {\n                        reject.call(command, redirectionError);\n                    },\n                    defaults: function () {\n                        reject.call(command, err);\n                    },\n                });\n            };\n        }\n        tryConnection();\n        function tryConnection(random, asking) {\n            if (_this.status === \"end\") {\n                command.reject(new redis_errors_1.AbortError(\"Cluster is ended.\"));\n                return;\n            }\n            let redis;\n            if (_this.status === \"ready\" || command.name === \"cluster\") {\n                if (node && node.redis) {\n                    redis = node.redis;\n                }\n                else if (Command_1.default.checkFlag(\"ENTER_SUBSCRIBER_MODE\", command.name) ||\n                    Command_1.default.checkFlag(\"EXIT_SUBSCRIBER_MODE\", command.name)) {\n                    if (_this.options.shardedSubscribers == true &&\n                        (command.name == \"ssubscribe\" || command.name == \"sunsubscribe\")) {\n                        const sub = _this.shardedSubscribers.getResponsibleSubscriber(targetSlot);\n                        let status = -1;\n                        if (command.name == \"ssubscribe\")\n                            status = _this.shardedSubscribers.addChannels(command.getKeys());\n                        if (command.name == \"sunsubscribe\")\n                            status = _this.shardedSubscribers.removeChannels(command.getKeys());\n                        if (status !== -1) {\n                            redis = sub.getInstance();\n                        }\n                        else {\n                            command.reject(new redis_errors_1.AbortError(\"Can't add or remove the given channels. Are they in the same slot?\"));\n                        }\n                    }\n                    else {\n                        redis = _this.subscriber.getInstance();\n                    }\n                    if (!redis) {\n                        command.reject(new redis_errors_1.AbortError(\"No subscriber for the cluster\"));\n                        return;\n                    }\n                }\n                else {\n                    if (!random) {\n                        if (typeof targetSlot === \"number\" && _this.slots[targetSlot]) {\n                            const nodeKeys = _this.slots[targetSlot];\n                            if (typeof to === \"function\") {\n                                const nodes = nodeKeys.map(function (key) {\n                                    return _this.connectionPool.getInstanceByKey(key);\n                                });\n                                redis = to(nodes, command);\n                                if (Array.isArray(redis)) {\n                                    redis = (0, utils_1.sample)(redis);\n                                }\n                                if (!redis) {\n                                    redis = nodes[0];\n                                }\n                            }\n                            else {\n                                let key;\n                                if (to === \"all\") {\n                                    key = (0, utils_1.sample)(nodeKeys);\n                                }\n                                else if (to === \"slave\" && nodeKeys.length > 1) {\n                                    key = (0, utils_1.sample)(nodeKeys, 1);\n                                }\n                                else {\n                                    key = nodeKeys[0];\n                                }\n                                redis = _this.connectionPool.getInstanceByKey(key);\n                            }\n                        }\n                        if (asking) {\n                            redis = _this.connectionPool.getInstanceByKey(asking);\n                            redis.asking();\n                        }\n                    }\n                    if (!redis) {\n                        redis =\n                            (typeof to === \"function\"\n                                ? null\n                                : _this.connectionPool.getSampleInstance(to)) ||\n                                _this.connectionPool.getSampleInstance(\"all\");\n                    }\n                }\n                if (node && !node.redis) {\n                    node.redis = redis;\n                }\n            }\n            if (redis) {\n                redis.sendCommand(command, stream);\n            }\n            else if (_this.options.enableOfflineQueue) {\n                _this.offlineQueue.push({\n                    command: command,\n                    stream: stream,\n                    node: node,\n                });\n            }\n            else {\n                command.reject(new Error(\"Cluster isn't ready and enableOfflineQueue options is false\"));\n            }\n        }\n        return command.promise;\n    }\n    sscanStream(key, options) {\n        return this.createScanStream(\"sscan\", { key, options });\n    }\n    sscanBufferStream(key, options) {\n        return this.createScanStream(\"sscanBuffer\", { key, options });\n    }\n    hscanStream(key, options) {\n        return this.createScanStream(\"hscan\", { key, options });\n    }\n    hscanBufferStream(key, options) {\n        return this.createScanStream(\"hscanBuffer\", { key, options });\n    }\n    zscanStream(key, options) {\n        return this.createScanStream(\"zscan\", { key, options });\n    }\n    zscanBufferStream(key, options) {\n        return this.createScanStream(\"zscanBuffer\", { key, options });\n    }\n    /**\n     * @ignore\n     */\n    handleError(error, ttl, handlers) {\n        if (typeof ttl.value === \"undefined\") {\n            ttl.value = this.options.maxRedirections;\n        }\n        else {\n            ttl.value -= 1;\n        }\n        if (ttl.value <= 0) {\n            handlers.maxRedirections(new Error(\"Too many Cluster redirections. Last error: \" + error));\n            return;\n        }\n        const errv = error.message.split(\" \");\n        if (errv[0] === \"MOVED\") {\n            const timeout = this.options.retryDelayOnMoved;\n            if (timeout && typeof timeout === \"number\") {\n                this.delayQueue.push(\"moved\", handlers.moved.bind(null, errv[1], errv[2]), { timeout });\n            }\n            else {\n                handlers.moved(errv[1], errv[2]);\n            }\n        }\n        else if (errv[0] === \"ASK\") {\n            handlers.ask(errv[1], errv[2]);\n        }\n        else if (errv[0] === \"TRYAGAIN\") {\n            this.delayQueue.push(\"tryagain\", handlers.tryagain, {\n                timeout: this.options.retryDelayOnTryAgain,\n            });\n        }\n        else if (errv[0] === \"CLUSTERDOWN\" &&\n            this.options.retryDelayOnClusterDown > 0) {\n            this.delayQueue.push(\"clusterdown\", handlers.connectionClosed, {\n                timeout: this.options.retryDelayOnClusterDown,\n                callback: this.refreshSlotsCache.bind(this),\n            });\n        }\n        else if (error.message === utils_1.CONNECTION_CLOSED_ERROR_MSG &&\n            this.options.retryDelayOnFailover > 0 &&\n            this.status === \"ready\") {\n            this.delayQueue.push(\"failover\", handlers.connectionClosed, {\n                timeout: this.options.retryDelayOnFailover,\n                callback: this.refreshSlotsCache.bind(this),\n            });\n        }\n        else {\n            handlers.defaults();\n        }\n    }\n    resetOfflineQueue() {\n        this.offlineQueue = new Deque();\n    }\n    clearNodesRefreshInterval() {\n        if (this.slotsTimer) {\n            clearTimeout(this.slotsTimer);\n            this.slotsTimer = null;\n        }\n    }\n    resetNodesRefreshInterval() {\n        if (this.slotsTimer || !this.options.slotsRefreshInterval) {\n            return;\n        }\n        const nextRound = () => {\n            this.slotsTimer = setTimeout(() => {\n                debug('refreshing slot caches... (triggered by \"slotsRefreshInterval\" option)');\n                this.refreshSlotsCache(() => {\n                    nextRound();\n                });\n            }, this.options.slotsRefreshInterval);\n        };\n        nextRound();\n    }\n    /**\n     * Change cluster instance's status\n     */\n    setStatus(status) {\n        debug(\"status: %s -> %s\", this.status || \"[empty]\", status);\n        this.status = status;\n        process.nextTick(() => {\n            this.emit(status);\n        });\n    }\n    /**\n     * Called when closed to check whether a reconnection should be made\n     */\n    handleCloseEvent(reason) {\n        if (reason) {\n            debug(\"closed because %s\", reason);\n        }\n        let retryDelay;\n        if (!this.manuallyClosing &&\n            typeof this.options.clusterRetryStrategy === \"function\") {\n            retryDelay = this.options.clusterRetryStrategy.call(this, ++this.retryAttempts, reason);\n        }\n        if (typeof retryDelay === \"number\") {\n            this.setStatus(\"reconnecting\");\n            this.reconnectTimeout = setTimeout(() => {\n                this.reconnectTimeout = null;\n                debug(\"Cluster is disconnected. Retrying after %dms\", retryDelay);\n                this.connect().catch(function (err) {\n                    debug(\"Got error %s when reconnecting. Ignoring...\", err);\n                });\n            }, retryDelay);\n        }\n        else {\n            this.setStatus(\"end\");\n            this.flushQueue(new Error(\"None of startup nodes is available\"));\n        }\n    }\n    /**\n     * Flush offline queue with error.\n     */\n    flushQueue(error) {\n        let item;\n        while ((item = this.offlineQueue.shift())) {\n            item.command.reject(error);\n        }\n    }\n    executeOfflineCommands() {\n        if (this.offlineQueue.length) {\n            debug(\"send %d commands in offline queue\", this.offlineQueue.length);\n            const offlineQueue = this.offlineQueue;\n            this.resetOfflineQueue();\n            let item;\n            while ((item = offlineQueue.shift())) {\n                this.sendCommand(item.command, item.stream, item.node);\n            }\n        }\n    }\n    natMapper(nodeKey) {\n        const key = typeof nodeKey === \"string\"\n            ? nodeKey\n            : `${nodeKey.host}:${nodeKey.port}`;\n        let mapped = null;\n        if (this.options.natMap && typeof this.options.natMap === \"function\") {\n            mapped = this.options.natMap(key);\n        }\n        else if (this.options.natMap && typeof this.options.natMap === \"object\") {\n            mapped = this.options.natMap[key];\n        }\n        if (mapped) {\n            debug(\"NAT mapping %s -> %O\", key, mapped);\n            return Object.assign({}, mapped);\n        }\n        return typeof nodeKey === \"string\"\n            ? (0, util_1.nodeKeyToRedisOptions)(nodeKey)\n            : nodeKey;\n    }\n    getInfoFromNode(redis, callback) {\n        if (!redis) {\n            return callback(new Error(\"Node is disconnected\"));\n        }\n        // Use a duplication of the connection to avoid\n        // timeouts when the connection is in the blocking\n        // mode (e.g. waiting for BLPOP).\n        const duplicatedConnection = redis.duplicate({\n            enableOfflineQueue: true,\n            enableReadyCheck: false,\n            retryStrategy: null,\n            connectionName: (0, util_1.getConnectionName)(\"refresher\", this.options.redisOptions && this.options.redisOptions.connectionName),\n        });\n        // Ignore error events since we will handle\n        // exceptions for the CLUSTER SLOTS command.\n        duplicatedConnection.on(\"error\", utils_1.noop);\n        duplicatedConnection.cluster(\"SLOTS\", (0, utils_1.timeout)((err, result) => {\n            duplicatedConnection.disconnect();\n            if (err) {\n                debug(\"error encountered running CLUSTER.SLOTS: %s\", err);\n                return callback(err);\n            }\n            if (this.status === \"disconnecting\" ||\n                this.status === \"close\" ||\n                this.status === \"end\") {\n                debug(\"ignore CLUSTER.SLOTS results (count: %d) since cluster status is %s\", result.length, this.status);\n                callback();\n                return;\n            }\n            const nodes = [];\n            debug(\"cluster slots result count: %d\", result.length);\n            for (let i = 0; i < result.length; ++i) {\n                const items = result[i];\n                const slotRangeStart = items[0];\n                const slotRangeEnd = items[1];\n                const keys = [];\n                for (let j = 2; j < items.length; j++) {\n                    if (!items[j][0]) {\n                        continue;\n                    }\n                    const node = this.natMapper({\n                        host: items[j][0],\n                        port: items[j][1],\n                    });\n                    node.readOnly = j !== 2;\n                    nodes.push(node);\n                    keys.push(node.host + \":\" + node.port);\n                }\n                debug(\"cluster slots result [%d]: slots %d~%d served by %s\", i, slotRangeStart, slotRangeEnd, keys);\n                for (let slot = slotRangeStart; slot <= slotRangeEnd; slot++) {\n                    this.slots[slot] = keys;\n                }\n            }\n            // Assign to each node keys a numeric value to make autopipeline comparison faster.\n            this._groupsIds = Object.create(null);\n            let j = 0;\n            for (let i = 0; i < 16384; i++) {\n                const target = (this.slots[i] || []).join(\";\");\n                if (!target.length) {\n                    this._groupsBySlot[i] = undefined;\n                    continue;\n                }\n                if (!this._groupsIds[target]) {\n                    this._groupsIds[target] = ++j;\n                }\n                this._groupsBySlot[i] = this._groupsIds[target];\n            }\n            this.connectionPool.reset(nodes);\n            callback();\n        }, this.options.slotsRefreshTimeout));\n    }\n    invokeReadyDelayedCallbacks(err) {\n        for (const c of this._readyDelayedCallbacks) {\n            process.nextTick(c, err);\n        }\n        this._readyDelayedCallbacks = [];\n    }\n    /**\n     * Check whether Cluster is able to process commands\n     */\n    readyCheck(callback) {\n        this.cluster(\"INFO\", (err, res) => {\n            if (err) {\n                return callback(err);\n            }\n            if (typeof res !== \"string\") {\n                return callback();\n            }\n            let state;\n            const lines = res.split(\"\\r\\n\");\n            for (let i = 0; i < lines.length; ++i) {\n                const parts = lines[i].split(\":\");\n                if (parts[0] === \"cluster_state\") {\n                    state = parts[1];\n                    break;\n                }\n            }\n            if (state === \"fail\") {\n                debug(\"cluster state not ok (%s)\", state);\n                callback(null, state);\n            }\n            else {\n                callback();\n            }\n        });\n    }\n    resolveSrv(hostname) {\n        return new Promise((resolve, reject) => {\n            this.options.resolveSrv(hostname, (err, records) => {\n                if (err) {\n                    return reject(err);\n                }\n                const self = this, groupedRecords = (0, util_1.groupSrvRecords)(records), sortedKeys = Object.keys(groupedRecords).sort((a, b) => parseInt(a) - parseInt(b));\n                function tryFirstOne(err) {\n                    if (!sortedKeys.length) {\n                        return reject(err);\n                    }\n                    const key = sortedKeys[0], group = groupedRecords[key], record = (0, util_1.weightSrvRecords)(group);\n                    if (!group.records.length) {\n                        sortedKeys.shift();\n                    }\n                    self.dnsLookup(record.name).then((host) => resolve({\n                        host,\n                        port: record.port,\n                    }), tryFirstOne);\n                }\n                tryFirstOne();\n            });\n        });\n    }\n    dnsLookup(hostname) {\n        return new Promise((resolve, reject) => {\n            this.options.dnsLookup(hostname, (err, address) => {\n                if (err) {\n                    debug(\"failed to resolve hostname %s to IP: %s\", hostname, err.message);\n                    reject(err);\n                }\n                else {\n                    debug(\"resolved hostname %s to IP %s\", hostname, address);\n                    resolve(address);\n                }\n            });\n        });\n    }\n    /**\n     * Normalize startup nodes, and resolving hostnames to IPs.\n     *\n     * This process happens every time when #connect() is called since\n     * #startupNodes and DNS records may chanage.\n     */\n    async resolveStartupNodeHostnames() {\n        if (!Array.isArray(this.startupNodes) || this.startupNodes.length === 0) {\n            throw new Error(\"`startupNodes` should contain at least one node.\");\n        }\n        const startupNodes = (0, util_1.normalizeNodeOptions)(this.startupNodes);\n        const hostnames = (0, util_1.getUniqueHostnamesFromOptions)(startupNodes);\n        if (hostnames.length === 0) {\n            return startupNodes;\n        }\n        const configs = await Promise.all(hostnames.map((this.options.useSRVRecords ? this.resolveSrv : this.dnsLookup).bind(this)));\n        const hostnameToConfig = (0, utils_1.zipMap)(hostnames, configs);\n        return startupNodes.map((node) => {\n            const config = hostnameToConfig.get(node.host);\n            if (!config) {\n                return node;\n            }\n            if (this.options.useSRVRecords) {\n                return Object.assign({}, node, config);\n            }\n            return Object.assign({}, node, { host: config });\n        });\n    }\n    createScanStream(command, { key, options = {} }) {\n        return new ScanStream_1.default({\n            objectMode: true,\n            key: key,\n            redis: this,\n            command: command,\n            ...options,\n        });\n    }\n}\n(0, applyMixin_1.default)(Cluster, events_1.EventEmitter);\n(0, transaction_1.addTransactionSupport)(Cluster.prototype);\nexports.default = Cluster;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAQ,KAAK,EAAE;AACjC,MAAM,8BAA8B,IAAI;AACxC;;CAEC,GACD,MAAM,gBAAgB,YAAY,OAAO;IACrC;;KAEC,GACD,6DAA6D;IAC7D,YAAY,YAAY,EAAE,UAAU,CAAC,CAAC,CAAE;QACpC,KAAK;QACL,IAAI,CAAC,KAAK,GAAG,EAAE;QACf;;SAEC,GACD,IAAI,CAAC,UAAU,GAAG,CAAC;QACnB;;SAEC,GACD,IAAI,CAAC,aAAa,GAAG,MAAM;QAC3B;;SAEC,GACD,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,aAAa,OAAO;QAC1C,IAAI,CAAC,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,2BAA2B,GAAG,EAAE;QACrC,IAAI,CAAC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAAC,qBAAqB,GAAG,IAAI;QACjC,IAAI,CAAC,sBAAsB,GAAG,EAAE;QAChC;;;;;SAKC,GACD,IAAI,CAAC,eAAe,GAAG;QACvB,SAAS,YAAY,CAAC,IAAI,CAAC,IAAI;QAC/B,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,QAAQ,QAAQ,EAAE,CAAC,GAAG,SAAS,iBAAiB,uBAAuB,EAAE,IAAI,CAAC,OAAO;QACxG,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,MACnC,IAAI,CAAC,kBAAkB,GAAG,IAAI,yBAAyB,OAAO,CAAC,IAAI;QACvE,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,IACzB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,IACnC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS;QAChE;QACA,mBAAmB;QACnB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,cACnC;YAAC;YAAO;YAAU;SAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,MAAM,CAAC,GAAG;YACpE,MAAM,IAAI,MAAM,gCACZ,IAAI,CAAC,OAAO,CAAC,UAAU,GACvB;QACR;QACA,IAAI,CAAC,cAAc,GAAG,IAAI,iBAAiB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY;QAC5E,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO;YACpC,IAAI,CAAC,IAAI,CAAC,SAAS;QACvB;QACA,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,SAAS,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,SAAS;QACvB;QACA,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,SAAS;YAC5B,IAAI,CAAC,SAAS,CAAC;QACnB;QACA,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,aAAa,CAAC,OAAO;YACxC,IAAI,CAAC,IAAI,CAAC,cAAc,OAAO;QACnC;QACA,IAAI,CAAC,UAAU,GAAG,IAAI,oBAAoB,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI;QAC3E,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACtB,OAAO,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,WAAW;gBAC5D,IAAI,CAAC,aAAa,CAAC,MAAM;YAC7B;QACJ;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC1B,IAAI,CAAC,SAAS,CAAC;QACnB,OACK;YACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;gBAClB,MAAM,yBAAyB;YACnC;QACJ;IACJ;IACA;;KAEC,GACD,UAAU;QACN,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,IAAI,CAAC,MAAM,KAAK,gBAChB,IAAI,CAAC,MAAM,KAAK,aAChB,IAAI,CAAC,MAAM,KAAK,SAAS;gBACzB,OAAO,IAAI,MAAM;gBACjB;YACJ;YACA,MAAM,QAAQ,EAAE,IAAI,CAAC,eAAe;YACpC,IAAI,CAAC,SAAS,CAAC;YACf,IAAI,CAAC,2BAA2B,GAC3B,IAAI,CAAC,CAAC;gBACP,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO;oBAChC,MAAM,sFAAsF,OAAO,IAAI,CAAC,eAAe;oBACvH,OAAO,IAAI,eAAe,UAAU,CAAC;oBACrC;gBACJ;gBACA,IAAI,IAAI,CAAC,MAAM,KAAK,cAAc;oBAC9B,MAAM,qFAAqF,IAAI,CAAC,MAAM;oBACtG,OAAO,IAAI,eAAe,UAAU,CAAC;oBACrC;gBACJ;gBACA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;gBAC1B,MAAM,eAAe;oBACjB,IAAI,CAAC,SAAS,CAAC;oBACf,IAAI,CAAC,aAAa,GAAG;oBACrB,IAAI,CAAC,sBAAsB;oBAC3B,IAAI,CAAC,yBAAyB;oBAC9B;gBACJ;gBACA,IAAI,gBAAgB;gBACpB,MAAM,kBAAkB;oBACpB,IAAI,CAAC,2BAA2B,CAAC;oBACjC,IAAI,CAAC,cAAc,CAAC,SAAS;oBAC7B,IAAI,CAAC,eAAe,GAAG;oBACvB,IAAI,CAAC,SAAS,CAAC;oBACf,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;wBAC/B,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK;4BAClB,IAAI,OAAO,MAAM;gCACb,MAAM,4CAA4C,OAAO;gCACzD,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW;oCAC3B,IAAI,CAAC,UAAU,CAAC;gCACpB;4BACJ,OACK;gCACD;4BACJ;wBACJ;oBACJ,OACK;wBACD;oBACJ;gBACJ;gBACA,gBAAgB;oBACZ,MAAM,QAAQ,IAAI,MAAM;oBACxB,IAAI,CAAC,cAAc,CAAC,WAAW;oBAC/B,IAAI,CAAC,2BAA2B,CAAC;oBACjC,OAAO;gBACX;gBACA,IAAI,CAAC,IAAI,CAAC,WAAW;gBACrB,IAAI,CAAC,IAAI,CAAC,SAAS;gBACnB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI;gBAClD,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBACpB,IAAI,OAAO,IAAI,OAAO,KAAK,wBAAwB,OAAO,CAAC,cAAc,EAAE;wBACvE,QAAQ,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS;wBACzD,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;oBAChC;gBACJ;gBACA,IAAI,CAAC,UAAU,CAAC,KAAK;gBACrB,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;oBACjC,IAAI,CAAC,kBAAkB,CAAC,KAAK;gBACjC;YACJ,GACK,KAAK,CAAC,CAAC;gBACR,IAAI,CAAC,SAAS,CAAC;gBACf,IAAI,CAAC,gBAAgB,CAAC;gBACtB,IAAI,CAAC,2BAA2B,CAAC;gBACjC,OAAO;YACX;QACJ;IACJ;IACA;;KAEC,GACD,WAAW,YAAY,KAAK,EAAE;QAC1B,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,IAAI,CAAC,SAAS,CAAC;QACf,IAAI,CAAC,WAAW;YACZ,IAAI,CAAC,eAAe,GAAG;QAC3B;QACA,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,WAAW;YACrC,aAAa,IAAI,CAAC,gBAAgB;YAClC,IAAI,CAAC,gBAAgB,GAAG;YACxB,MAAM;QACV;QACA,IAAI,CAAC,yBAAyB;QAC9B,IAAI,CAAC,UAAU,CAAC,IAAI;QACpB,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;YACjC,IAAI,CAAC,kBAAkB,CAAC,IAAI;QAChC;QACA,IAAI,WAAW,QAAQ;YACnB,IAAI,CAAC,SAAS,CAAC;YACf,IAAI,CAAC,gBAAgB;QACzB,OACK;YACD,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;QAChC;IACJ;IACA;;KAEC,GACD,KAAK,QAAQ,EAAE;QACX,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,IAAI,CAAC,SAAS,CAAC;QACf,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,aAAa,IAAI,CAAC,gBAAgB;YAClC,IAAI,CAAC,gBAAgB,GAAG;QAC5B;QACA,IAAI,CAAC,yBAAyB;QAC9B,IAAI,CAAC,UAAU,CAAC,IAAI;QACpB,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;YACjC,IAAI,CAAC,kBAAkB,CAAC,IAAI;QAChC;QACA,IAAI,WAAW,QAAQ;YACnB,MAAM,MAAM,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,OAAO,CAAC,OAAO;YACvE,8CAA8C;YAC9C,yCAAyC;YACzC,aAAa,CAAA;gBACT,IAAI,CAAC,SAAS,CAAC;gBACf,IAAI,CAAC,gBAAgB;YACzB,CAAA,EAAE,IAAI,CAAC,IAAI;YACX,OAAO;QACX;QACA,OAAO,CAAC,GAAG,uBAAuB,OAAO,EAAE,QAAQ,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI,GAAG,KAAK,CAAC,CAAC;gBACjG,iDAAiD;gBACjD,yBAAyB;gBACzB,IAAI,IAAI,OAAO,KAAK,QAAQ,2BAA2B,EAAE;oBACrD,OAAO;gBACX;gBACA,MAAM;YACV,KAAK,IAAI,CAAC,IAAM,OAAO;IAC3B;IACA;;;;;;;;KAQC,GACD,UAAU,uBAAuB,EAAE,EAAE,kBAAkB,CAAC,CAAC,EAAE;QACvD,MAAM,eAAe,qBAAqB,MAAM,GAAG,IAC7C,uBACA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;QAC9B,MAAM,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE;QAChD,OAAO,IAAI,QAAQ,cAAc;IACrC;IACA;;KAEC,GACD,MAAM,OAAO,KAAK,EAAE;QAChB,IAAI,SAAS,SAAS,SAAS,YAAY,SAAS,SAAS;YACzD,MAAM,IAAI,MAAM,mBAAmB,OAAO;QAC9C;QACA,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;IACxC;IACA;;;;KAIC,GACD,gBAAgB,QAAQ,EAAE;QACtB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;IACrC;IACA;;;;KAIC,GACD,IAAI,wBAAwB;QACxB,IAAI,SAAS;QACb,KAAK,MAAM,YAAY,IAAI,CAAC,cAAc,CAAC,MAAM,GAAI;YACjD,UAAU,SAAS,MAAM;QAC7B;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,kBAAkB,QAAQ,EAAE;QACxB,IAAI,UAAU;YACV,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC;QAC1C;QACA,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB;QACJ;QACA,IAAI,CAAC,YAAY,GAAG;QACpB,MAAM,QAAQ,IAAI;QAClB,MAAM,UAAU,CAAC;YACb,IAAI,CAAC,YAAY,GAAG;YACpB,KAAK,MAAM,YAAY,IAAI,CAAC,2BAA2B,CAAE;gBACrD,SAAS;YACb;YACA,IAAI,CAAC,2BAA2B,GAAG,EAAE;QACzC;QACA,MAAM,QAAQ,CAAC,GAAG,QAAQ,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ;QAC/D,IAAI,gBAAgB;QACpB,SAAS,QAAQ,KAAK;YAClB,IAAI,UAAU,MAAM,MAAM,EAAE;gBACxB,MAAM,QAAQ,IAAI,wBAAwB,OAAO,CAAC,wBAAwB,OAAO,CAAC,cAAc,EAAE;gBAClG,OAAO,QAAQ;YACnB;YACA,MAAM,OAAO,KAAK,CAAC,MAAM;YACzB,MAAM,MAAM,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,EAAE;YACvD,MAAM,8BAA8B;YACpC,MAAM,eAAe,CAAC,MAAM,SAAU,GAAG;gBACrC,OAAQ,MAAM,MAAM;oBAChB,KAAK;oBACL,KAAK;wBACD,OAAO,QAAQ,IAAI,MAAM;oBAC7B,KAAK;wBACD,OAAO,QAAQ,IAAI,MAAM;gBACjC;gBACA,IAAI,KAAK;oBACL,MAAM,IAAI,CAAC,cAAc,KAAK;oBAC9B,gBAAgB;oBAChB,QAAQ,QAAQ;gBACpB,OACK;oBACD,MAAM,IAAI,CAAC;oBACX;gBACJ;YACJ;QACJ;QACA,QAAQ;IACZ;IACA;;KAEC,GACD,YAAY,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE;QAC/B,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YACxB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,QAAQ,IAAI;QACrC;QACA,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO;YACvB,QAAQ,MAAM,CAAC,IAAI,MAAM,QAAQ,2BAA2B;YAC5D,OAAO,QAAQ,OAAO;QAC1B;QACA,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,UAAU;QAChC,IAAI,OAAO,UAAU;YACjB,MAAM,oBAAoB,QAAQ,UAAU,IACvC,CAAC,GAAG,WAAW,MAAM,EAAE,QAAQ,IAAI,KAAK,CAAC,GAAG,WAAW,OAAO,EAAE,QAAQ,IAAI,EAAE;YACnF,IAAI,CAAC,mBAAmB;gBACpB,KAAK;YACT;QACJ;QACA,IAAI,aAAa,OAAO,KAAK,IAAI,GAAG,QAAQ,OAAO;QACnD,MAAM,MAAM,CAAC;QACb,MAAM,QAAQ,IAAI;QAClB,IAAI,CAAC,QAAQ,CAAC,4BAA4B,GAAG,CAAC,UAAU;YACpD,4BAA4B,GAAG,CAAC;YAChC,MAAM,SAAS,QAAQ,MAAM;YAC7B,QAAQ,MAAM,GAAG,SAAU,GAAG;gBAC1B,MAAM,aAAa,cAAc,IAAI,CAAC,MAAM;gBAC5C,MAAM,WAAW,CAAC,KAAK,KAAK;oBACxB,OAAO,SAAU,IAAI,EAAE,GAAG;wBACtB,MAAM,6BAA6B,QAAQ,IAAI,EAAE;wBACjD,aAAa,OAAO;wBACpB,IAAI,MAAM,KAAK,CAAC,KAAK,EAAE;4BACnB,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG;wBAC3B,OACK;4BACD,MAAM,KAAK,CAAC,KAAK,GAAG;gCAAC;6BAAI;wBAC7B;wBACA,MAAM,aAAa,CAAC,KAAK,GACrB,MAAM,UAAU,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK;wBACjD,MAAM,cAAc,CAAC,YAAY,CAAC,MAAM,SAAS,CAAC;wBAClD;wBACA,MAAM;wBACN,MAAM,iBAAiB;oBAC3B;oBACA,KAAK,SAAU,IAAI,EAAE,GAAG;wBACpB,MAAM,uCAAuC,QAAQ,IAAI,EAAE;wBAC3D,MAAM,SAAS,MAAM,SAAS,CAAC;wBAC/B,MAAM,cAAc,CAAC,YAAY,CAAC;wBAClC,cAAc,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI,EAAE;oBACxD;oBACA,UAAU;oBACV,aAAa;oBACb,kBAAkB;oBAClB,iBAAiB,SAAU,gBAAgB;wBACvC,OAAO,IAAI,CAAC,SAAS;oBACzB;oBACA,UAAU;wBACN,OAAO,IAAI,CAAC,SAAS;oBACzB;gBACJ;YACJ;QACJ;QACA;QACA,SAAS,cAAc,MAAM,EAAE,MAAM;YACjC,IAAI,MAAM,MAAM,KAAK,OAAO;gBACxB,QAAQ,MAAM,CAAC,IAAI,eAAe,UAAU,CAAC;gBAC7C;YACJ;YACA,IAAI;YACJ,IAAI,MAAM,MAAM,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;gBACxD,IAAI,QAAQ,KAAK,KAAK,EAAE;oBACpB,QAAQ,KAAK,KAAK;gBACtB,OACK,IAAI,UAAU,OAAO,CAAC,SAAS,CAAC,yBAAyB,QAAQ,IAAI,KACtE,UAAU,OAAO,CAAC,SAAS,CAAC,wBAAwB,QAAQ,IAAI,GAAG;oBACnE,IAAI,MAAM,OAAO,CAAC,kBAAkB,IAAI,QACpC,CAAC,QAAQ,IAAI,IAAI,gBAAgB,QAAQ,IAAI,IAAI,cAAc,GAAG;wBAClE,MAAM,MAAM,MAAM,kBAAkB,CAAC,wBAAwB,CAAC;wBAC9D,IAAI,SAAS,CAAC;wBACd,IAAI,QAAQ,IAAI,IAAI,cAChB,SAAS,MAAM,kBAAkB,CAAC,WAAW,CAAC,QAAQ,OAAO;wBACjE,IAAI,QAAQ,IAAI,IAAI,gBAChB,SAAS,MAAM,kBAAkB,CAAC,cAAc,CAAC,QAAQ,OAAO;wBACpE,IAAI,WAAW,CAAC,GAAG;4BACf,QAAQ,IAAI,WAAW;wBAC3B,OACK;4BACD,QAAQ,MAAM,CAAC,IAAI,eAAe,UAAU,CAAC;wBACjD;oBACJ,OACK;wBACD,QAAQ,MAAM,UAAU,CAAC,WAAW;oBACxC;oBACA,IAAI,CAAC,OAAO;wBACR,QAAQ,MAAM,CAAC,IAAI,eAAe,UAAU,CAAC;wBAC7C;oBACJ;gBACJ,OACK;oBACD,IAAI,CAAC,QAAQ;wBACT,IAAI,OAAO,eAAe,YAAY,MAAM,KAAK,CAAC,WAAW,EAAE;4BAC3D,MAAM,WAAW,MAAM,KAAK,CAAC,WAAW;4BACxC,IAAI,OAAO,OAAO,YAAY;gCAC1B,MAAM,QAAQ,SAAS,GAAG,CAAC,SAAU,GAAG;oCACpC,OAAO,MAAM,cAAc,CAAC,gBAAgB,CAAC;gCACjD;gCACA,QAAQ,GAAG,OAAO;gCAClB,IAAI,MAAM,OAAO,CAAC,QAAQ;oCACtB,QAAQ,CAAC,GAAG,QAAQ,MAAM,EAAE;gCAChC;gCACA,IAAI,CAAC,OAAO;oCACR,QAAQ,KAAK,CAAC,EAAE;gCACpB;4BACJ,OACK;gCACD,IAAI;gCACJ,IAAI,OAAO,OAAO;oCACd,MAAM,CAAC,GAAG,QAAQ,MAAM,EAAE;gCAC9B,OACK,IAAI,OAAO,WAAW,SAAS,MAAM,GAAG,GAAG;oCAC5C,MAAM,CAAC,GAAG,QAAQ,MAAM,EAAE,UAAU;gCACxC,OACK;oCACD,MAAM,QAAQ,CAAC,EAAE;gCACrB;gCACA,QAAQ,MAAM,cAAc,CAAC,gBAAgB,CAAC;4BAClD;wBACJ;wBACA,IAAI,QAAQ;4BACR,QAAQ,MAAM,cAAc,CAAC,gBAAgB,CAAC;4BAC9C,MAAM,MAAM;wBAChB;oBACJ;oBACA,IAAI,CAAC,OAAO;wBACR,QACI,CAAC,OAAO,OAAO,aACT,OACA,MAAM,cAAc,CAAC,iBAAiB,CAAC,GAAG,KAC5C,MAAM,cAAc,CAAC,iBAAiB,CAAC;oBACnD;gBACJ;gBACA,IAAI,QAAQ,CAAC,KAAK,KAAK,EAAE;oBACrB,KAAK,KAAK,GAAG;gBACjB;YACJ;YACA,IAAI,OAAO;gBACP,MAAM,WAAW,CAAC,SAAS;YAC/B,OACK,IAAI,MAAM,OAAO,CAAC,kBAAkB,EAAE;gBACvC,MAAM,YAAY,CAAC,IAAI,CAAC;oBACpB,SAAS;oBACT,QAAQ;oBACR,MAAM;gBACV;YACJ,OACK;gBACD,QAAQ,MAAM,CAAC,IAAI,MAAM;YAC7B;QACJ;QACA,OAAO,QAAQ,OAAO;IAC1B;IACA,YAAY,GAAG,EAAE,OAAO,EAAE;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS;YAAE;YAAK;QAAQ;IACzD;IACA,kBAAkB,GAAG,EAAE,OAAO,EAAE;QAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe;YAAE;YAAK;QAAQ;IAC/D;IACA,YAAY,GAAG,EAAE,OAAO,EAAE;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS;YAAE;YAAK;QAAQ;IACzD;IACA,kBAAkB,GAAG,EAAE,OAAO,EAAE;QAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe;YAAE;YAAK;QAAQ;IAC/D;IACA,YAAY,GAAG,EAAE,OAAO,EAAE;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS;YAAE;YAAK;QAAQ;IACzD;IACA,kBAAkB,GAAG,EAAE,OAAO,EAAE;QAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe;YAAE;YAAK;QAAQ;IAC/D;IACA;;KAEC,GACD,YAAY,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE;QAC9B,IAAI,OAAO,IAAI,KAAK,KAAK,aAAa;YAClC,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe;QAC5C,OACK;YACD,IAAI,KAAK,IAAI;QACjB;QACA,IAAI,IAAI,KAAK,IAAI,GAAG;YAChB,SAAS,eAAe,CAAC,IAAI,MAAM,gDAAgD;YACnF;QACJ;QACA,MAAM,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC;QACjC,IAAI,IAAI,CAAC,EAAE,KAAK,SAAS;YACrB,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,iBAAiB;YAC9C,IAAI,WAAW,OAAO,YAAY,UAAU;gBACxC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,SAAS,KAAK,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG;oBAAE;gBAAQ;YACzF,OACK;gBACD,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;YACnC;QACJ,OACK,IAAI,IAAI,CAAC,EAAE,KAAK,OAAO;YACxB,SAAS,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;QACjC,OACK,IAAI,IAAI,CAAC,EAAE,KAAK,YAAY;YAC7B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,SAAS,QAAQ,EAAE;gBAChD,SAAS,IAAI,CAAC,OAAO,CAAC,oBAAoB;YAC9C;QACJ,OACK,IAAI,IAAI,CAAC,EAAE,KAAK,iBACjB,IAAI,CAAC,OAAO,CAAC,uBAAuB,GAAG,GAAG;YAC1C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,SAAS,gBAAgB,EAAE;gBAC3D,SAAS,IAAI,CAAC,OAAO,CAAC,uBAAuB;gBAC7C,UAAU,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI;YAC9C;QACJ,OACK,IAAI,MAAM,OAAO,KAAK,QAAQ,2BAA2B,IAC1D,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,KACpC,IAAI,CAAC,MAAM,KAAK,SAAS;YACzB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,SAAS,gBAAgB,EAAE;gBACxD,SAAS,IAAI,CAAC,OAAO,CAAC,oBAAoB;gBAC1C,UAAU,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI;YAC9C;QACJ,OACK;YACD,SAAS,QAAQ;QACrB;IACJ;IACA,oBAAoB;QAChB,IAAI,CAAC,YAAY,GAAG,IAAI;IAC5B;IACA,4BAA4B;QACxB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,aAAa,IAAI,CAAC,UAAU;YAC5B,IAAI,CAAC,UAAU,GAAG;QACtB;IACJ;IACA,4BAA4B;QACxB,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;YACvD;QACJ;QACA,MAAM,YAAY;YACd,IAAI,CAAC,UAAU,GAAG,WAAW;gBACzB,MAAM;gBACN,IAAI,CAAC,iBAAiB,CAAC;oBACnB;gBACJ;YACJ,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB;QACxC;QACA;IACJ;IACA;;KAEC,GACD,UAAU,MAAM,EAAE;QACd,MAAM,oBAAoB,IAAI,CAAC,MAAM,IAAI,WAAW;QACpD,IAAI,CAAC,MAAM,GAAG;QACd,QAAQ,QAAQ,CAAC;YACb,IAAI,CAAC,IAAI,CAAC;QACd;IACJ;IACA;;KAEC,GACD,iBAAiB,MAAM,EAAE;QACrB,IAAI,QAAQ;YACR,MAAM,qBAAqB;QAC/B;QACA,IAAI;QACJ,IAAI,CAAC,IAAI,CAAC,eAAe,IACrB,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,KAAK,YAAY;YACzD,aAAa,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE;QACpF;QACA,IAAI,OAAO,eAAe,UAAU;YAChC,IAAI,CAAC,SAAS,CAAC;YACf,IAAI,CAAC,gBAAgB,GAAG,WAAW;gBAC/B,IAAI,CAAC,gBAAgB,GAAG;gBACxB,MAAM,gDAAgD;gBACtD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,SAAU,GAAG;oBAC9B,MAAM,+CAA+C;gBACzD;YACJ,GAAG;QACP,OACK;YACD,IAAI,CAAC,SAAS,CAAC;YACf,IAAI,CAAC,UAAU,CAAC,IAAI,MAAM;QAC9B;IACJ;IACA;;KAEC,GACD,WAAW,KAAK,EAAE;QACd,IAAI;QACJ,MAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,GAAK;YACvC,KAAK,OAAO,CAAC,MAAM,CAAC;QACxB;IACJ;IACA,yBAAyB;QACrB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YAC1B,MAAM,qCAAqC,IAAI,CAAC,YAAY,CAAC,MAAM;YACnE,MAAM,eAAe,IAAI,CAAC,YAAY;YACtC,IAAI,CAAC,iBAAiB;YACtB,IAAI;YACJ,MAAQ,OAAO,aAAa,KAAK,GAAK;gBAClC,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE,KAAK,MAAM,EAAE,KAAK,IAAI;YACzD;QACJ;IACJ;IACA,UAAU,OAAO,EAAE;QACf,MAAM,MAAM,OAAO,YAAY,WACzB,UACA,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,IAAI,EAAE;QACvC,IAAI,SAAS;QACb,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,YAAY;YAClE,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACjC,OACK,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,UAAU;YACrE,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI;QACrC;QACA,IAAI,QAAQ;YACR,MAAM,wBAAwB,KAAK;YACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG;QAC7B;QACA,OAAO,OAAO,YAAY,WACpB,CAAC,GAAG,OAAO,qBAAqB,EAAE,WAClC;IACV;IACA,gBAAgB,KAAK,EAAE,QAAQ,EAAE;QAC7B,IAAI,CAAC,OAAO;YACR,OAAO,SAAS,IAAI,MAAM;QAC9B;QACA,+CAA+C;QAC/C,kDAAkD;QAClD,iCAAiC;QACjC,MAAM,uBAAuB,MAAM,SAAS,CAAC;YACzC,oBAAoB;YACpB,kBAAkB;YAClB,eAAe;YACf,gBAAgB,CAAC,GAAG,OAAO,iBAAiB,EAAE,aAAa,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc;QACpI;QACA,2CAA2C;QAC3C,4CAA4C;QAC5C,qBAAqB,EAAE,CAAC,SAAS,QAAQ,IAAI;QAC7C,qBAAqB,OAAO,CAAC,SAAS,CAAC,GAAG,QAAQ,OAAO,EAAE,CAAC,KAAK;YAC7D,qBAAqB,UAAU;YAC/B,IAAI,KAAK;gBACL,MAAM,+CAA+C;gBACrD,OAAO,SAAS;YACpB;YACA,IAAI,IAAI,CAAC,MAAM,KAAK,mBAChB,IAAI,CAAC,MAAM,KAAK,WAChB,IAAI,CAAC,MAAM,KAAK,OAAO;gBACvB,MAAM,uEAAuE,OAAO,MAAM,EAAE,IAAI,CAAC,MAAM;gBACvG;gBACA;YACJ;YACA,MAAM,QAAQ,EAAE;YAChB,MAAM,kCAAkC,OAAO,MAAM;YACrD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;gBACpC,MAAM,QAAQ,MAAM,CAAC,EAAE;gBACvB,MAAM,iBAAiB,KAAK,CAAC,EAAE;gBAC/B,MAAM,eAAe,KAAK,CAAC,EAAE;gBAC7B,MAAM,OAAO,EAAE;gBACf,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACnC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE;wBACd;oBACJ;oBACA,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC;wBACxB,MAAM,KAAK,CAAC,EAAE,CAAC,EAAE;wBACjB,MAAM,KAAK,CAAC,EAAE,CAAC,EAAE;oBACrB;oBACA,KAAK,QAAQ,GAAG,MAAM;oBACtB,MAAM,IAAI,CAAC;oBACX,KAAK,IAAI,CAAC,KAAK,IAAI,GAAG,MAAM,KAAK,IAAI;gBACzC;gBACA,MAAM,uDAAuD,GAAG,gBAAgB,cAAc;gBAC9F,IAAK,IAAI,OAAO,gBAAgB,QAAQ,cAAc,OAAQ;oBAC1D,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;gBACvB;YACJ;YACA,mFAAmF;YACnF,IAAI,CAAC,UAAU,GAAG,OAAO,MAAM,CAAC;YAChC,IAAI,IAAI;YACR,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC5B,MAAM,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE,IAAI,CAAC;gBAC1C,IAAI,CAAC,OAAO,MAAM,EAAE;oBAChB,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG;oBACxB;gBACJ;gBACA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;oBAC1B,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE;gBAChC;gBACA,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO;YACnD;YACA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;YAC1B;QACJ,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB;IACvC;IACA,4BAA4B,GAAG,EAAE;QAC7B,KAAK,MAAM,KAAK,IAAI,CAAC,sBAAsB,CAAE;YACzC,QAAQ,QAAQ,CAAC,GAAG;QACxB;QACA,IAAI,CAAC,sBAAsB,GAAG,EAAE;IACpC;IACA;;KAEC,GACD,WAAW,QAAQ,EAAE;QACjB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK;YACvB,IAAI,KAAK;gBACL,OAAO,SAAS;YACpB;YACA,IAAI,OAAO,QAAQ,UAAU;gBACzB,OAAO;YACX;YACA,IAAI;YACJ,MAAM,QAAQ,IAAI,KAAK,CAAC;YACxB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;gBACnC,MAAM,QAAQ,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;gBAC7B,IAAI,KAAK,CAAC,EAAE,KAAK,iBAAiB;oBAC9B,QAAQ,KAAK,CAAC,EAAE;oBAChB;gBACJ;YACJ;YACA,IAAI,UAAU,QAAQ;gBAClB,MAAM,6BAA6B;gBACnC,SAAS,MAAM;YACnB,OACK;gBACD;YACJ;QACJ;IACJ;IACA,WAAW,QAAQ,EAAE;QACjB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK;gBACpC,IAAI,KAAK;oBACL,OAAO,OAAO;gBAClB;gBACA,MAAM,OAAO,IAAI,EAAE,iBAAiB,CAAC,GAAG,OAAO,eAAe,EAAE,UAAU,aAAa,OAAO,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,GAAG,IAAM,SAAS,KAAK,SAAS;gBACzJ,SAAS,YAAY,GAAG;oBACpB,IAAI,CAAC,WAAW,MAAM,EAAE;wBACpB,OAAO,OAAO;oBAClB;oBACA,MAAM,MAAM,UAAU,CAAC,EAAE,EAAE,QAAQ,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,OAAO,gBAAgB,EAAE;oBAC9F,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,EAAE;wBACvB,WAAW,KAAK;oBACpB;oBACA,KAAK,SAAS,CAAC,OAAO,IAAI,EAAE,IAAI,CAAC,CAAC,OAAS,QAAQ;4BAC/C;4BACA,MAAM,OAAO,IAAI;wBACrB,IAAI;gBACR;gBACA;YACJ;QACJ;IACJ;IACA,UAAU,QAAQ,EAAE;QAChB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK;gBACnC,IAAI,KAAK;oBACL,MAAM,2CAA2C,UAAU,IAAI,OAAO;oBACtE,OAAO;gBACX,OACK;oBACD,MAAM,iCAAiC,UAAU;oBACjD,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA;;;;;KAKC,GACD,MAAM,8BAA8B;QAChC,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,GAAG;YACrE,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,eAAe,CAAC,GAAG,OAAO,oBAAoB,EAAE,IAAI,CAAC,YAAY;QACvE,MAAM,YAAY,CAAC,GAAG,OAAO,6BAA6B,EAAE;QAC5D,IAAI,UAAU,MAAM,KAAK,GAAG;YACxB,OAAO;QACX;QACA,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI;QACzH,MAAM,mBAAmB,CAAC,GAAG,QAAQ,MAAM,EAAE,WAAW;QACxD,OAAO,aAAa,GAAG,CAAC,CAAC;YACrB,MAAM,SAAS,iBAAiB,GAAG,CAAC,KAAK,IAAI;YAC7C,IAAI,CAAC,QAAQ;gBACT,OAAO;YACX;YACA,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;gBAC5B,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;YACnC;YACA,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;gBAAE,MAAM;YAAO;QAClD;IACJ;IACA,iBAAiB,OAAO,EAAE,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE,EAAE;QAC7C,OAAO,IAAI,aAAa,OAAO,CAAC;YAC5B,YAAY;YACZ,KAAK;YACL,OAAO,IAAI;YACX,SAAS;YACT,GAAG,OAAO;QACd;IACJ;AACJ;AACA,CAAC,GAAG,aAAa,OAAO,EAAE,SAAS,SAAS,YAAY;AACxD,CAAC,GAAG,cAAc,qBAAqB,EAAE,QAAQ,SAAS;AAC1D,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3393, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/connectors/AbstractConnector.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst utils_1 = require(\"../utils\");\nconst debug = (0, utils_1.Debug)(\"AbstractConnector\");\nclass AbstractConnector {\n    constructor(disconnectTimeout) {\n        this.connecting = false;\n        this.disconnectTimeout = disconnectTimeout;\n    }\n    check(info) {\n        return true;\n    }\n    disconnect() {\n        this.connecting = false;\n        if (this.stream) {\n            const stream = this.stream; // Make sure callbacks refer to the same instance\n            const timeout = setTimeout(() => {\n                debug(\"stream %s:%s still open, destroying it\", stream.remoteAddress, stream.remotePort);\n                stream.destroy();\n            }, this.disconnectTimeout);\n            stream.on(\"close\", () => clearTimeout(timeout));\n            stream.end();\n        }\n    }\n}\nexports.default = AbstractConnector;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAQ,KAAK,EAAE;AACjC,MAAM;IACF,YAAY,iBAAiB,CAAE;QAC3B,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,iBAAiB,GAAG;IAC7B;IACA,MAAM,IAAI,EAAE;QACR,OAAO;IACX;IACA,aAAa;QACT,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,MAAM,SAAS,IAAI,CAAC,MAAM,EAAE,iDAAiD;YAC7E,MAAM,UAAU,WAAW;gBACvB,MAAM,0CAA0C,OAAO,aAAa,EAAE,OAAO,UAAU;gBACvF,OAAO,OAAO;YAClB,GAAG,IAAI,CAAC,iBAAiB;YACzB,OAAO,EAAE,CAAC,SAAS,IAAM,aAAa;YACtC,OAAO,GAAG;QACd;IACJ;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3426, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/connectors/StandaloneConnector.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst net_1 = require(\"net\");\nconst tls_1 = require(\"tls\");\nconst utils_1 = require(\"../utils\");\nconst AbstractConnector_1 = require(\"./AbstractConnector\");\nclass StandaloneConnector extends AbstractConnector_1.default {\n    constructor(options) {\n        super(options.disconnectTimeout);\n        this.options = options;\n    }\n    connect(_) {\n        const { options } = this;\n        this.connecting = true;\n        let connectionOptions;\n        if (\"path\" in options && options.path) {\n            connectionOptions = {\n                path: options.path,\n            };\n        }\n        else {\n            connectionOptions = {};\n            if (\"port\" in options && options.port != null) {\n                connectionOptions.port = options.port;\n            }\n            if (\"host\" in options && options.host != null) {\n                connectionOptions.host = options.host;\n            }\n            if (\"family\" in options && options.family != null) {\n                connectionOptions.family = options.family;\n            }\n        }\n        if (options.tls) {\n            Object.assign(connectionOptions, options.tls);\n        }\n        // TODO:\n        // We use native Promise here since other Promise\n        // implementation may use different schedulers that\n        // cause issue when the stream is resolved in the\n        // next tick.\n        // Should use the provided promise in the next major\n        // version and do not connect before resolved.\n        return new Promise((resolve, reject) => {\n            process.nextTick(() => {\n                if (!this.connecting) {\n                    reject(new Error(utils_1.CONNECTION_CLOSED_ERROR_MSG));\n                    return;\n                }\n                try {\n                    if (options.tls) {\n                        this.stream = (0, tls_1.connect)(connectionOptions);\n                    }\n                    else {\n                        this.stream = (0, net_1.createConnection)(connectionOptions);\n                    }\n                }\n                catch (err) {\n                    reject(err);\n                    return;\n                }\n                this.stream.once(\"error\", (err) => {\n                    this.firstError = err;\n                });\n                resolve(this.stream);\n            });\n        });\n    }\n}\nexports.default = StandaloneConnector;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,4BAA4B,oBAAoB,OAAO;IACzD,YAAY,OAAO,CAAE;QACjB,KAAK,CAAC,QAAQ,iBAAiB;QAC/B,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,QAAQ,CAAC,EAAE;QACP,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI;QACxB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI;QACJ,IAAI,UAAU,WAAW,QAAQ,IAAI,EAAE;YACnC,oBAAoB;gBAChB,MAAM,QAAQ,IAAI;YACtB;QACJ,OACK;YACD,oBAAoB,CAAC;YACrB,IAAI,UAAU,WAAW,QAAQ,IAAI,IAAI,MAAM;gBAC3C,kBAAkB,IAAI,GAAG,QAAQ,IAAI;YACzC;YACA,IAAI,UAAU,WAAW,QAAQ,IAAI,IAAI,MAAM;gBAC3C,kBAAkB,IAAI,GAAG,QAAQ,IAAI;YACzC;YACA,IAAI,YAAY,WAAW,QAAQ,MAAM,IAAI,MAAM;gBAC/C,kBAAkB,MAAM,GAAG,QAAQ,MAAM;YAC7C;QACJ;QACA,IAAI,QAAQ,GAAG,EAAE;YACb,OAAO,MAAM,CAAC,mBAAmB,QAAQ,GAAG;QAChD;QACA,QAAQ;QACR,iDAAiD;QACjD,mDAAmD;QACnD,iDAAiD;QACjD,aAAa;QACb,oDAAoD;QACpD,8CAA8C;QAC9C,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,QAAQ,QAAQ,CAAC;gBACb,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBAClB,OAAO,IAAI,MAAM,QAAQ,2BAA2B;oBACpD;gBACJ;gBACA,IAAI;oBACA,IAAI,QAAQ,GAAG,EAAE;wBACb,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,OAAO,EAAE;oBACrC,OACK;wBACD,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,gBAAgB,EAAE;oBAC9C;gBACJ,EACA,OAAO,KAAK;oBACR,OAAO;oBACP;gBACJ;gBACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;oBACvB,IAAI,CAAC,UAAU,GAAG;gBACtB;gBACA,QAAQ,IAAI,CAAC,MAAM;YACvB;QACJ;IACJ;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3499, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/connectors/SentinelConnector/SentinelIterator.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nfunction isSentinelEql(a, b) {\n    return ((a.host || \"127.0.0.1\") === (b.host || \"127.0.0.1\") &&\n        (a.port || 26379) === (b.port || 26379));\n}\nclass SentinelIterator {\n    constructor(sentinels) {\n        this.cursor = 0;\n        this.sentinels = sentinels.slice(0);\n    }\n    next() {\n        const done = this.cursor >= this.sentinels.length;\n        return { done, value: done ? undefined : this.sentinels[this.cursor++] };\n    }\n    reset(moveCurrentEndpointToFirst) {\n        if (moveCurrentEndpointToFirst &&\n            this.sentinels.length > 1 &&\n            this.cursor !== 1) {\n            this.sentinels.unshift(...this.sentinels.splice(this.cursor - 1));\n        }\n        this.cursor = 0;\n    }\n    add(sentinel) {\n        for (let i = 0; i < this.sentinels.length; i++) {\n            if (isSentinelEql(sentinel, this.sentinels[i])) {\n                return false;\n            }\n        }\n        this.sentinels.push(sentinel);\n        return true;\n    }\n    toString() {\n        return `${JSON.stringify(this.sentinels)} @${this.cursor}`;\n    }\n}\nexports.default = SentinelIterator;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,SAAS,cAAc,CAAC,EAAE,CAAC;IACvB,OAAQ,CAAC,EAAE,IAAI,IAAI,WAAW,MAAM,CAAC,EAAE,IAAI,IAAI,WAAW,KACtD,CAAC,EAAE,IAAI,IAAI,KAAK,MAAM,CAAC,EAAE,IAAI,IAAI,KAAK;AAC9C;AACA,MAAM;IACF,YAAY,SAAS,CAAE;QACnB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG,UAAU,KAAK,CAAC;IACrC;IACA,OAAO;QACH,MAAM,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM;QACjD,OAAO;YAAE;YAAM,OAAO,OAAO,YAAY,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG;QAAC;IAC3E;IACA,MAAM,0BAA0B,EAAE;QAC9B,IAAI,8BACA,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KACxB,IAAI,CAAC,MAAM,KAAK,GAAG;YACnB,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG;QAClE;QACA,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,IAAI,QAAQ,EAAE;QACV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAK;YAC5C,IAAI,cAAc,UAAU,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG;gBAC5C,OAAO;YACX;QACJ;QACA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACpB,OAAO;IACX;IACA,WAAW;QACP,OAAO,GAAG,KAAK,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE;IAC9D;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3543, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/connectors/SentinelConnector/FailoverDetector.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.FailoverDetector = void 0;\nconst utils_1 = require(\"../../utils\");\nconst debug = (0, utils_1.Debug)(\"FailoverDetector\");\nconst CHANNEL_NAME = \"+switch-master\";\nclass FailoverDetector {\n    // sentinels can't be used for regular commands after this\n    constructor(connector, sentinels) {\n        this.isDisconnected = false;\n        this.connector = connector;\n        this.sentinels = sentinels;\n    }\n    cleanup() {\n        this.isDisconnected = true;\n        for (const sentinel of this.sentinels) {\n            sentinel.client.disconnect();\n        }\n    }\n    async subscribe() {\n        debug(\"Starting FailoverDetector\");\n        const promises = [];\n        for (const sentinel of this.sentinels) {\n            const promise = sentinel.client.subscribe(CHANNEL_NAME).catch((err) => {\n                debug(\"Failed to subscribe to failover messages on sentinel %s:%s (%s)\", sentinel.address.host || \"127.0.0.1\", sentinel.address.port || 26739, err.message);\n            });\n            promises.push(promise);\n            sentinel.client.on(\"message\", (channel) => {\n                if (!this.isDisconnected && channel === CHANNEL_NAME) {\n                    this.disconnect();\n                }\n            });\n        }\n        await Promise.all(promises);\n    }\n    disconnect() {\n        // Avoid disconnecting more than once per failover.\n        // A new FailoverDetector will be created after reconnecting.\n        this.isDisconnected = true;\n        debug(\"Failover detected, disconnecting\");\n        // Will call this.cleanup()\n        this.connector.disconnect();\n    }\n}\nexports.FailoverDetector = FailoverDetector;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,KAAK;AAChC,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAQ,KAAK,EAAE;AACjC,MAAM,eAAe;AACrB,MAAM;IACF,0DAA0D;IAC1D,YAAY,SAAS,EAAE,SAAS,CAAE;QAC9B,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,UAAU;QACN,IAAI,CAAC,cAAc,GAAG;QACtB,KAAK,MAAM,YAAY,IAAI,CAAC,SAAS,CAAE;YACnC,SAAS,MAAM,CAAC,UAAU;QAC9B;IACJ;IACA,MAAM,YAAY;QACd,MAAM;QACN,MAAM,WAAW,EAAE;QACnB,KAAK,MAAM,YAAY,IAAI,CAAC,SAAS,CAAE;YACnC,MAAM,UAAU,SAAS,MAAM,CAAC,SAAS,CAAC,cAAc,KAAK,CAAC,CAAC;gBAC3D,MAAM,mEAAmE,SAAS,OAAO,CAAC,IAAI,IAAI,aAAa,SAAS,OAAO,CAAC,IAAI,IAAI,OAAO,IAAI,OAAO;YAC9J;YACA,SAAS,IAAI,CAAC;YACd,SAAS,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC;gBAC3B,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,YAAY,cAAc;oBAClD,IAAI,CAAC,UAAU;gBACnB;YACJ;QACJ;QACA,MAAM,QAAQ,GAAG,CAAC;IACtB;IACA,aAAa;QACT,mDAAmD;QACnD,6DAA6D;QAC7D,IAAI,CAAC,cAAc,GAAG;QACtB,MAAM;QACN,2BAA2B;QAC3B,IAAI,CAAC,SAAS,CAAC,UAAU;IAC7B;AACJ;AACA,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3595, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/connectors/SentinelConnector/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SentinelIterator = void 0;\nconst net_1 = require(\"net\");\nconst utils_1 = require(\"../../utils\");\nconst tls_1 = require(\"tls\");\nconst SentinelIterator_1 = require(\"./SentinelIterator\");\nexports.SentinelIterator = SentinelIterator_1.default;\nconst AbstractConnector_1 = require(\"../AbstractConnector\");\nconst Redis_1 = require(\"../../Redis\");\nconst FailoverDetector_1 = require(\"./FailoverDetector\");\nconst debug = (0, utils_1.Debug)(\"SentinelConnector\");\nclass SentinelConnector extends AbstractConnector_1.default {\n    constructor(options) {\n        super(options.disconnectTimeout);\n        this.options = options;\n        this.emitter = null;\n        this.failoverDetector = null;\n        if (!this.options.sentinels.length) {\n            throw new Error(\"Requires at least one sentinel to connect to.\");\n        }\n        if (!this.options.name) {\n            throw new Error(\"Requires the name of master.\");\n        }\n        this.sentinelIterator = new SentinelIterator_1.default(this.options.sentinels);\n    }\n    check(info) {\n        const roleMatches = !info.role || this.options.role === info.role;\n        if (!roleMatches) {\n            debug(\"role invalid, expected %s, but got %s\", this.options.role, info.role);\n            // Start from the next item.\n            // Note that `reset` will move the cursor to the previous element,\n            // so we advance two steps here.\n            this.sentinelIterator.next();\n            this.sentinelIterator.next();\n            this.sentinelIterator.reset(true);\n        }\n        return roleMatches;\n    }\n    disconnect() {\n        super.disconnect();\n        if (this.failoverDetector) {\n            this.failoverDetector.cleanup();\n        }\n    }\n    connect(eventEmitter) {\n        this.connecting = true;\n        this.retryAttempts = 0;\n        let lastError;\n        const connectToNext = async () => {\n            const endpoint = this.sentinelIterator.next();\n            if (endpoint.done) {\n                this.sentinelIterator.reset(false);\n                const retryDelay = typeof this.options.sentinelRetryStrategy === \"function\"\n                    ? this.options.sentinelRetryStrategy(++this.retryAttempts)\n                    : null;\n                let errorMsg = typeof retryDelay !== \"number\"\n                    ? \"All sentinels are unreachable and retry is disabled.\"\n                    : `All sentinels are unreachable. Retrying from scratch after ${retryDelay}ms.`;\n                if (lastError) {\n                    errorMsg += ` Last error: ${lastError.message}`;\n                }\n                debug(errorMsg);\n                const error = new Error(errorMsg);\n                if (typeof retryDelay === \"number\") {\n                    eventEmitter(\"error\", error);\n                    await new Promise((resolve) => setTimeout(resolve, retryDelay));\n                    return connectToNext();\n                }\n                else {\n                    throw error;\n                }\n            }\n            let resolved = null;\n            let err = null;\n            try {\n                resolved = await this.resolve(endpoint.value);\n            }\n            catch (error) {\n                err = error;\n            }\n            if (!this.connecting) {\n                throw new Error(utils_1.CONNECTION_CLOSED_ERROR_MSG);\n            }\n            const endpointAddress = endpoint.value.host + \":\" + endpoint.value.port;\n            if (resolved) {\n                debug(\"resolved: %s:%s from sentinel %s\", resolved.host, resolved.port, endpointAddress);\n                if (this.options.enableTLSForSentinelMode && this.options.tls) {\n                    Object.assign(resolved, this.options.tls);\n                    this.stream = (0, tls_1.connect)(resolved);\n                    this.stream.once(\"secureConnect\", this.initFailoverDetector.bind(this));\n                }\n                else {\n                    this.stream = (0, net_1.createConnection)(resolved);\n                    this.stream.once(\"connect\", this.initFailoverDetector.bind(this));\n                }\n                this.stream.once(\"error\", (err) => {\n                    this.firstError = err;\n                });\n                return this.stream;\n            }\n            else {\n                const errorMsg = err\n                    ? \"failed to connect to sentinel \" +\n                        endpointAddress +\n                        \" because \" +\n                        err.message\n                    : \"connected to sentinel \" +\n                        endpointAddress +\n                        \" successfully, but got an invalid reply: \" +\n                        resolved;\n                debug(errorMsg);\n                eventEmitter(\"sentinelError\", new Error(errorMsg));\n                if (err) {\n                    lastError = err;\n                }\n                return connectToNext();\n            }\n        };\n        return connectToNext();\n    }\n    async updateSentinels(client) {\n        if (!this.options.updateSentinels) {\n            return;\n        }\n        const result = await client.sentinel(\"sentinels\", this.options.name);\n        if (!Array.isArray(result)) {\n            return;\n        }\n        result\n            .map(utils_1.packObject)\n            .forEach((sentinel) => {\n            const flags = sentinel.flags ? sentinel.flags.split(\",\") : [];\n            if (flags.indexOf(\"disconnected\") === -1 &&\n                sentinel.ip &&\n                sentinel.port) {\n                const endpoint = this.sentinelNatResolve(addressResponseToAddress(sentinel));\n                if (this.sentinelIterator.add(endpoint)) {\n                    debug(\"adding sentinel %s:%s\", endpoint.host, endpoint.port);\n                }\n            }\n        });\n        debug(\"Updated internal sentinels: %s\", this.sentinelIterator);\n    }\n    async resolveMaster(client) {\n        const result = await client.sentinel(\"get-master-addr-by-name\", this.options.name);\n        await this.updateSentinels(client);\n        return this.sentinelNatResolve(Array.isArray(result)\n            ? { host: result[0], port: Number(result[1]) }\n            : null);\n    }\n    async resolveSlave(client) {\n        const result = await client.sentinel(\"slaves\", this.options.name);\n        if (!Array.isArray(result)) {\n            return null;\n        }\n        const availableSlaves = result\n            .map(utils_1.packObject)\n            .filter((slave) => slave.flags && !slave.flags.match(/(disconnected|s_down|o_down)/));\n        return this.sentinelNatResolve(selectPreferredSentinel(availableSlaves, this.options.preferredSlaves));\n    }\n    sentinelNatResolve(item) {\n        if (!item || !this.options.natMap)\n            return item;\n        const key = `${item.host}:${item.port}`;\n        let result = item;\n        if (typeof this.options.natMap === \"function\") {\n            result = this.options.natMap(key) || item;\n        }\n        else if (typeof this.options.natMap === \"object\") {\n            result = this.options.natMap[key] || item;\n        }\n        return result;\n    }\n    connectToSentinel(endpoint, options) {\n        const redis = new Redis_1.default({\n            port: endpoint.port || 26379,\n            host: endpoint.host,\n            username: this.options.sentinelUsername || null,\n            password: this.options.sentinelPassword || null,\n            family: endpoint.family ||\n                // @ts-expect-error\n                (\"path\" in this.options && this.options.path\n                    ? undefined\n                    : // @ts-expect-error\n                        this.options.family),\n            tls: this.options.sentinelTLS,\n            retryStrategy: null,\n            enableReadyCheck: false,\n            connectTimeout: this.options.connectTimeout,\n            commandTimeout: this.options.sentinelCommandTimeout,\n            ...options,\n        });\n        // @ts-expect-error\n        return redis;\n    }\n    async resolve(endpoint) {\n        const client = this.connectToSentinel(endpoint);\n        // ignore the errors since resolve* methods will handle them\n        client.on(\"error\", noop);\n        try {\n            if (this.options.role === \"slave\") {\n                return await this.resolveSlave(client);\n            }\n            else {\n                return await this.resolveMaster(client);\n            }\n        }\n        finally {\n            client.disconnect();\n        }\n    }\n    async initFailoverDetector() {\n        var _a;\n        if (!this.options.failoverDetector) {\n            return;\n        }\n        // Move the current sentinel to the first position\n        this.sentinelIterator.reset(true);\n        const sentinels = [];\n        // In case of a large amount of sentinels, limit the number of concurrent connections\n        while (sentinels.length < this.options.sentinelMaxConnections) {\n            const { done, value } = this.sentinelIterator.next();\n            if (done) {\n                break;\n            }\n            const client = this.connectToSentinel(value, {\n                lazyConnect: true,\n                retryStrategy: this.options.sentinelReconnectStrategy,\n            });\n            client.on(\"reconnecting\", () => {\n                var _a;\n                // Tests listen to this event\n                (_a = this.emitter) === null || _a === void 0 ? void 0 : _a.emit(\"sentinelReconnecting\");\n            });\n            sentinels.push({ address: value, client });\n        }\n        this.sentinelIterator.reset(false);\n        if (this.failoverDetector) {\n            // Clean up previous detector\n            this.failoverDetector.cleanup();\n        }\n        this.failoverDetector = new FailoverDetector_1.FailoverDetector(this, sentinels);\n        await this.failoverDetector.subscribe();\n        // Tests listen to this event\n        (_a = this.emitter) === null || _a === void 0 ? void 0 : _a.emit(\"failoverSubscribed\");\n    }\n}\nexports.default = SentinelConnector;\nfunction selectPreferredSentinel(availableSlaves, preferredSlaves) {\n    if (availableSlaves.length === 0) {\n        return null;\n    }\n    let selectedSlave;\n    if (typeof preferredSlaves === \"function\") {\n        selectedSlave = preferredSlaves(availableSlaves);\n    }\n    else if (preferredSlaves !== null && typeof preferredSlaves === \"object\") {\n        const preferredSlavesArray = Array.isArray(preferredSlaves)\n            ? preferredSlaves\n            : [preferredSlaves];\n        // sort by priority\n        preferredSlavesArray.sort((a, b) => {\n            // default the priority to 1\n            if (!a.prio) {\n                a.prio = 1;\n            }\n            if (!b.prio) {\n                b.prio = 1;\n            }\n            // lowest priority first\n            if (a.prio < b.prio) {\n                return -1;\n            }\n            if (a.prio > b.prio) {\n                return 1;\n            }\n            return 0;\n        });\n        // loop over preferred slaves and return the first match\n        for (let p = 0; p < preferredSlavesArray.length; p++) {\n            for (let a = 0; a < availableSlaves.length; a++) {\n                const slave = availableSlaves[a];\n                if (slave.ip === preferredSlavesArray[p].ip) {\n                    if (slave.port === preferredSlavesArray[p].port) {\n                        selectedSlave = slave;\n                        break;\n                    }\n                }\n            }\n            if (selectedSlave) {\n                break;\n            }\n        }\n    }\n    // if none of the preferred slaves are available, a random available slave is returned\n    if (!selectedSlave) {\n        selectedSlave = (0, utils_1.sample)(availableSlaves);\n    }\n    return addressResponseToAddress(selectedSlave);\n}\nfunction addressResponseToAddress(input) {\n    return { host: input.ip, port: Number(input.port) };\n}\nfunction noop() { }\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,gBAAgB,GAAG,KAAK;AAChC,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,QAAQ,gBAAgB,GAAG,mBAAmB,OAAO;AACrD,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAQ,KAAK,EAAE;AACjC,MAAM,0BAA0B,oBAAoB,OAAO;IACvD,YAAY,OAAO,CAAE;QACjB,KAAK,CAAC,QAAQ,iBAAiB;QAC/B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE;YAChC,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACpB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,gBAAgB,GAAG,IAAI,mBAAmB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS;IACjF;IACA,MAAM,IAAI,EAAE;QACR,MAAM,cAAc,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,IAAI;QACjE,IAAI,CAAC,aAAa;YACd,MAAM,yCAAyC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI;YAC3E,4BAA4B;YAC5B,kEAAkE;YAClE,gCAAgC;YAChC,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAC1B,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAC1B,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAChC;QACA,OAAO;IACX;IACA,aAAa;QACT,KAAK,CAAC;QACN,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,OAAO;QACjC;IACJ;IACA,QAAQ,YAAY,EAAE;QAClB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI;QACJ,MAAM,gBAAgB;YAClB,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAC3C,IAAI,SAAS,IAAI,EAAE;gBACf,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;gBAC5B,MAAM,aAAa,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,KAAK,aAC3D,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC,aAAa,IACvD;gBACN,IAAI,WAAW,OAAO,eAAe,WAC/B,yDACA,CAAC,2DAA2D,EAAE,WAAW,GAAG,CAAC;gBACnF,IAAI,WAAW;oBACX,YAAY,CAAC,aAAa,EAAE,UAAU,OAAO,EAAE;gBACnD;gBACA,MAAM;gBACN,MAAM,QAAQ,IAAI,MAAM;gBACxB,IAAI,OAAO,eAAe,UAAU;oBAChC,aAAa,SAAS;oBACtB,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;oBACnD,OAAO;gBACX,OACK;oBACD,MAAM;gBACV;YACJ;YACA,IAAI,WAAW;YACf,IAAI,MAAM;YACV,IAAI;gBACA,WAAW,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK;YAChD,EACA,OAAO,OAAO;gBACV,MAAM;YACV;YACA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClB,MAAM,IAAI,MAAM,QAAQ,2BAA2B;YACvD;YACA,MAAM,kBAAkB,SAAS,KAAK,CAAC,IAAI,GAAG,MAAM,SAAS,KAAK,CAAC,IAAI;YACvE,IAAI,UAAU;gBACV,MAAM,oCAAoC,SAAS,IAAI,EAAE,SAAS,IAAI,EAAE;gBACxE,IAAI,IAAI,CAAC,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;oBAC3D,OAAO,MAAM,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,GAAG;oBACxC,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,OAAO,EAAE;oBACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI;gBACzE,OACK;oBACD,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,gBAAgB,EAAE;oBAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI;gBACnE;gBACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;oBACvB,IAAI,CAAC,UAAU,GAAG;gBACtB;gBACA,OAAO,IAAI,CAAC,MAAM;YACtB,OACK;gBACD,MAAM,WAAW,MACX,mCACE,kBACA,cACA,IAAI,OAAO,GACb,2BACE,kBACA,8CACA;gBACR,MAAM;gBACN,aAAa,iBAAiB,IAAI,MAAM;gBACxC,IAAI,KAAK;oBACL,YAAY;gBAChB;gBACA,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,gBAAgB,MAAM,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC/B;QACJ;QACA,MAAM,SAAS,MAAM,OAAO,QAAQ,CAAC,aAAa,IAAI,CAAC,OAAO,CAAC,IAAI;QACnE,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS;YACxB;QACJ;QACA,OACK,GAAG,CAAC,QAAQ,UAAU,EACtB,OAAO,CAAC,CAAC;YACV,MAAM,QAAQ,SAAS,KAAK,GAAG,SAAS,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE;YAC7D,IAAI,MAAM,OAAO,CAAC,oBAAoB,CAAC,KACnC,SAAS,EAAE,IACX,SAAS,IAAI,EAAE;gBACf,MAAM,WAAW,IAAI,CAAC,kBAAkB,CAAC,yBAAyB;gBAClE,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW;oBACrC,MAAM,yBAAyB,SAAS,IAAI,EAAE,SAAS,IAAI;gBAC/D;YACJ;QACJ;QACA,MAAM,kCAAkC,IAAI,CAAC,gBAAgB;IACjE;IACA,MAAM,cAAc,MAAM,EAAE;QACxB,MAAM,SAAS,MAAM,OAAO,QAAQ,CAAC,2BAA2B,IAAI,CAAC,OAAO,CAAC,IAAI;QACjF,MAAM,IAAI,CAAC,eAAe,CAAC;QAC3B,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO,CAAC,UACvC;YAAE,MAAM,MAAM,CAAC,EAAE;YAAE,MAAM,OAAO,MAAM,CAAC,EAAE;QAAE,IAC3C;IACV;IACA,MAAM,aAAa,MAAM,EAAE;QACvB,MAAM,SAAS,MAAM,OAAO,QAAQ,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI;QAChE,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS;YACxB,OAAO;QACX;QACA,MAAM,kBAAkB,OACnB,GAAG,CAAC,QAAQ,UAAU,EACtB,MAAM,CAAC,CAAC,QAAU,MAAM,KAAK,IAAI,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC;QACzD,OAAO,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,iBAAiB,IAAI,CAAC,OAAO,CAAC,eAAe;IACxG;IACA,mBAAmB,IAAI,EAAE;QACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAC7B,OAAO;QACX,MAAM,MAAM,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE;QACvC,IAAI,SAAS;QACb,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,YAAY;YAC3C,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ;QACzC,OACK,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,UAAU;YAC9C,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI;QACzC;QACA,OAAO;IACX;IACA,kBAAkB,QAAQ,EAAE,OAAO,EAAE;QACjC,MAAM,QAAQ,IAAI,QAAQ,OAAO,CAAC;YAC9B,MAAM,SAAS,IAAI,IAAI;YACvB,MAAM,SAAS,IAAI;YACnB,UAAU,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI;YAC3C,UAAU,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI;YAC3C,QAAQ,SAAS,MAAM,IACnB,mBAAmB;YACnB,CAAC,UAAU,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,GACtC,YAEE,IAAI,CAAC,OAAO,CAAC,MAAM;YAC/B,KAAK,IAAI,CAAC,OAAO,CAAC,WAAW;YAC7B,eAAe;YACf,kBAAkB;YAClB,gBAAgB,IAAI,CAAC,OAAO,CAAC,cAAc;YAC3C,gBAAgB,IAAI,CAAC,OAAO,CAAC,sBAAsB;YACnD,GAAG,OAAO;QACd;QACA,mBAAmB;QACnB,OAAO;IACX;IACA,MAAM,QAAQ,QAAQ,EAAE;QACpB,MAAM,SAAS,IAAI,CAAC,iBAAiB,CAAC;QACtC,4DAA4D;QAC5D,OAAO,EAAE,CAAC,SAAS;QACnB,IAAI;YACA,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;gBAC/B,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC;YACnC,OACK;gBACD,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC;YACpC;QACJ,SACQ;YACJ,OAAO,UAAU;QACrB;IACJ;IACA,MAAM,uBAAuB;QACzB,IAAI;QACJ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YAChC;QACJ;QACA,kDAAkD;QAClD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAC5B,MAAM,YAAY,EAAE;QACpB,qFAAqF;QACrF,MAAO,UAAU,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAE;YAC3D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAClD,IAAI,MAAM;gBACN;YACJ;YACA,MAAM,SAAS,IAAI,CAAC,iBAAiB,CAAC,OAAO;gBACzC,aAAa;gBACb,eAAe,IAAI,CAAC,OAAO,CAAC,yBAAyB;YACzD;YACA,OAAO,EAAE,CAAC,gBAAgB;gBACtB,IAAI;gBACJ,6BAA6B;gBAC7B,CAAC,KAAK,IAAI,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;YACrE;YACA,UAAU,IAAI,CAAC;gBAAE,SAAS;gBAAO;YAAO;QAC5C;QACA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAC5B,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,6BAA6B;YAC7B,IAAI,CAAC,gBAAgB,CAAC,OAAO;QACjC;QACA,IAAI,CAAC,gBAAgB,GAAG,IAAI,mBAAmB,gBAAgB,CAAC,IAAI,EAAE;QACtE,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS;QACrC,6BAA6B;QAC7B,CAAC,KAAK,IAAI,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC;IACrE;AACJ;AACA,QAAQ,OAAO,GAAG;AAClB,SAAS,wBAAwB,eAAe,EAAE,eAAe;IAC7D,IAAI,gBAAgB,MAAM,KAAK,GAAG;QAC9B,OAAO;IACX;IACA,IAAI;IACJ,IAAI,OAAO,oBAAoB,YAAY;QACvC,gBAAgB,gBAAgB;IACpC,OACK,IAAI,oBAAoB,QAAQ,OAAO,oBAAoB,UAAU;QACtE,MAAM,uBAAuB,MAAM,OAAO,CAAC,mBACrC,kBACA;YAAC;SAAgB;QACvB,mBAAmB;QACnB,qBAAqB,IAAI,CAAC,CAAC,GAAG;YAC1B,4BAA4B;YAC5B,IAAI,CAAC,EAAE,IAAI,EAAE;gBACT,EAAE,IAAI,GAAG;YACb;YACA,IAAI,CAAC,EAAE,IAAI,EAAE;gBACT,EAAE,IAAI,GAAG;YACb;YACA,wBAAwB;YACxB,IAAI,EAAE,IAAI,GAAG,EAAE,IAAI,EAAE;gBACjB,OAAO,CAAC;YACZ;YACA,IAAI,EAAE,IAAI,GAAG,EAAE,IAAI,EAAE;gBACjB,OAAO;YACX;YACA,OAAO;QACX;QACA,wDAAwD;QACxD,IAAK,IAAI,IAAI,GAAG,IAAI,qBAAqB,MAAM,EAAE,IAAK;YAClD,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;gBAC7C,MAAM,QAAQ,eAAe,CAAC,EAAE;gBAChC,IAAI,MAAM,EAAE,KAAK,oBAAoB,CAAC,EAAE,CAAC,EAAE,EAAE;oBACzC,IAAI,MAAM,IAAI,KAAK,oBAAoB,CAAC,EAAE,CAAC,IAAI,EAAE;wBAC7C,gBAAgB;wBAChB;oBACJ;gBACJ;YACJ;YACA,IAAI,eAAe;gBACf;YACJ;QACJ;IACJ;IACA,sFAAsF;IACtF,IAAI,CAAC,eAAe;QAChB,gBAAgB,CAAC,GAAG,QAAQ,MAAM,EAAE;IACxC;IACA,OAAO,yBAAyB;AACpC;AACA,SAAS,yBAAyB,KAAK;IACnC,OAAO;QAAE,MAAM,MAAM,EAAE;QAAE,MAAM,OAAO,MAAM,IAAI;IAAE;AACtD;AACA,SAAS,QAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3883, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/connectors/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SentinelConnector = exports.StandaloneConnector = void 0;\nconst StandaloneConnector_1 = require(\"./StandaloneConnector\");\nexports.StandaloneConnector = StandaloneConnector_1.default;\nconst SentinelConnector_1 = require(\"./SentinelConnector\");\nexports.SentinelConnector = SentinelConnector_1.default;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,QAAQ,mBAAmB,GAAG,KAAK;AAC/D,MAAM;AACN,QAAQ,mBAAmB,GAAG,sBAAsB,OAAO;AAC3D,MAAM;AACN,QAAQ,iBAAiB,GAAG,oBAAoB,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3897, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/errors/MaxRetriesPerRequestError.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst redis_errors_1 = require(\"redis-errors\");\nclass MaxRetriesPerRequestError extends redis_errors_1.AbortError {\n    constructor(maxRetriesPerRequest) {\n        const message = `Reached the max retries per request limit (which is ${maxRetriesPerRequest}). Refer to \"maxRetriesPerRequest\" option for details.`;\n        super(message);\n        Error.captureStackTrace(this, this.constructor);\n    }\n    get name() {\n        return this.constructor.name;\n    }\n}\nexports.default = MaxRetriesPerRequestError;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM,kCAAkC,eAAe,UAAU;IAC7D,YAAY,oBAAoB,CAAE;QAC9B,MAAM,UAAU,CAAC,oDAAoD,EAAE,qBAAqB,sDAAsD,CAAC;QACnJ,KAAK,CAAC;QACN,MAAM,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW;IAClD;IACA,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;IAChC;AACJ;AACA,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3918, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/errors/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MaxRetriesPerRequestError = void 0;\nconst MaxRetriesPerRequestError_1 = require(\"./MaxRetriesPerRequestError\");\nexports.MaxRetriesPerRequestError = MaxRetriesPerRequestError_1.default;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,yBAAyB,GAAG,KAAK;AACzC,MAAM;AACN,QAAQ,yBAAyB,GAAG,4BAA4B,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3930, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/SubscriptionSet.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * Tiny class to simplify dealing with subscription set\n */\nclass SubscriptionSet {\n    constructor() {\n        this.set = {\n            subscribe: {},\n            psubscribe: {},\n            ssubscribe: {},\n        };\n    }\n    add(set, channel) {\n        this.set[mapSet(set)][channel] = true;\n    }\n    del(set, channel) {\n        delete this.set[mapSet(set)][channel];\n    }\n    channels(set) {\n        return Object.keys(this.set[mapSet(set)]);\n    }\n    isEmpty() {\n        return (this.channels(\"subscribe\").length === 0 &&\n            this.channels(\"psubscribe\").length === 0 &&\n            this.channels(\"ssubscribe\").length === 0);\n    }\n}\nexports.default = SubscriptionSet;\nfunction mapSet(set) {\n    if (set === \"unsubscribe\") {\n        return \"subscribe\";\n    }\n    if (set === \"punsubscribe\") {\n        return \"psubscribe\";\n    }\n    if (set === \"sunsubscribe\") {\n        return \"ssubscribe\";\n    }\n    return set;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D;;CAEC,GACD,MAAM;IACF,aAAc;QACV,IAAI,CAAC,GAAG,GAAG;YACP,WAAW,CAAC;YACZ,YAAY,CAAC;YACb,YAAY,CAAC;QACjB;IACJ;IACA,IAAI,GAAG,EAAE,OAAO,EAAE;QACd,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,CAAC,QAAQ,GAAG;IACrC;IACA,IAAI,GAAG,EAAE,OAAO,EAAE;QACd,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,CAAC,QAAQ;IACzC;IACA,SAAS,GAAG,EAAE;QACV,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK;IAC5C;IACA,UAAU;QACN,OAAQ,IAAI,CAAC,QAAQ,CAAC,aAAa,MAAM,KAAK,KAC1C,IAAI,CAAC,QAAQ,CAAC,cAAc,MAAM,KAAK,KACvC,IAAI,CAAC,QAAQ,CAAC,cAAc,MAAM,KAAK;IAC/C;AACJ;AACA,QAAQ,OAAO,GAAG;AAClB,SAAS,OAAO,GAAG;IACf,IAAI,QAAQ,eAAe;QACvB,OAAO;IACX;IACA,IAAI,QAAQ,gBAAgB;QACxB,OAAO;IACX;IACA,IAAI,QAAQ,gBAAgB;QACxB,OAAO;IACX;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3975, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/DataHandler.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst Command_1 = require(\"./Command\");\nconst utils_1 = require(\"./utils\");\nconst RedisParser = require(\"redis-parser\");\nconst SubscriptionSet_1 = require(\"./SubscriptionSet\");\nconst debug = (0, utils_1.Debug)(\"dataHandler\");\nclass DataHandler {\n    constructor(redis, parserOptions) {\n        this.redis = redis;\n        const parser = new RedisParser({\n            stringNumbers: parserOptions.stringNumbers,\n            returnBuffers: true,\n            returnError: (err) => {\n                this.returnError(err);\n            },\n            returnFatalError: (err) => {\n                this.returnFatalError(err);\n            },\n            returnReply: (reply) => {\n                this.returnReply(reply);\n            },\n        });\n        // prependListener ensures the parser receives and processes data before socket timeout checks are performed\n        redis.stream.prependListener(\"data\", (data) => {\n            parser.execute(data);\n        });\n        // prependListener() doesn't enable flowing mode automatically - we need to resume the stream manually\n        redis.stream.resume();\n    }\n    returnFatalError(err) {\n        err.message += \". Please report this.\";\n        this.redis.recoverFromFatalError(err, err, { offlineQueue: false });\n    }\n    returnError(err) {\n        const item = this.shiftCommand(err);\n        if (!item) {\n            return;\n        }\n        err.command = {\n            name: item.command.name,\n            args: item.command.args,\n        };\n        this.redis.handleReconnection(err, item);\n    }\n    returnReply(reply) {\n        if (this.handleMonitorReply(reply)) {\n            return;\n        }\n        if (this.handleSubscriberReply(reply)) {\n            return;\n        }\n        const item = this.shiftCommand(reply);\n        if (!item) {\n            return;\n        }\n        if (Command_1.default.checkFlag(\"ENTER_SUBSCRIBER_MODE\", item.command.name)) {\n            this.redis.condition.subscriber = new SubscriptionSet_1.default();\n            this.redis.condition.subscriber.add(item.command.name, reply[1].toString());\n            if (!fillSubCommand(item.command, reply[2])) {\n                this.redis.commandQueue.unshift(item);\n            }\n        }\n        else if (Command_1.default.checkFlag(\"EXIT_SUBSCRIBER_MODE\", item.command.name)) {\n            if (!fillUnsubCommand(item.command, reply[2])) {\n                this.redis.commandQueue.unshift(item);\n            }\n        }\n        else {\n            item.command.resolve(reply);\n        }\n    }\n    handleSubscriberReply(reply) {\n        if (!this.redis.condition.subscriber) {\n            return false;\n        }\n        const replyType = Array.isArray(reply) ? reply[0].toString() : null;\n        debug('receive reply \"%s\" in subscriber mode', replyType);\n        switch (replyType) {\n            case \"message\":\n                if (this.redis.listeners(\"message\").length > 0) {\n                    // Check if there're listeners to avoid unnecessary `toString()`.\n                    this.redis.emit(\"message\", reply[1].toString(), reply[2] ? reply[2].toString() : \"\");\n                }\n                this.redis.emit(\"messageBuffer\", reply[1], reply[2]);\n                break;\n            case \"pmessage\": {\n                const pattern = reply[1].toString();\n                if (this.redis.listeners(\"pmessage\").length > 0) {\n                    this.redis.emit(\"pmessage\", pattern, reply[2].toString(), reply[3].toString());\n                }\n                this.redis.emit(\"pmessageBuffer\", pattern, reply[2], reply[3]);\n                break;\n            }\n            case \"smessage\": {\n                if (this.redis.listeners(\"smessage\").length > 0) {\n                    this.redis.emit(\"smessage\", reply[1].toString(), reply[2] ? reply[2].toString() : \"\");\n                }\n                this.redis.emit(\"smessageBuffer\", reply[1], reply[2]);\n                break;\n            }\n            case \"ssubscribe\":\n            case \"subscribe\":\n            case \"psubscribe\": {\n                const channel = reply[1].toString();\n                this.redis.condition.subscriber.add(replyType, channel);\n                const item = this.shiftCommand(reply);\n                if (!item) {\n                    return;\n                }\n                if (!fillSubCommand(item.command, reply[2])) {\n                    this.redis.commandQueue.unshift(item);\n                }\n                break;\n            }\n            case \"sunsubscribe\":\n            case \"unsubscribe\":\n            case \"punsubscribe\": {\n                const channel = reply[1] ? reply[1].toString() : null;\n                if (channel) {\n                    this.redis.condition.subscriber.del(replyType, channel);\n                }\n                const count = reply[2];\n                if (Number(count) === 0) {\n                    this.redis.condition.subscriber = false;\n                }\n                const item = this.shiftCommand(reply);\n                if (!item) {\n                    return;\n                }\n                if (!fillUnsubCommand(item.command, count)) {\n                    this.redis.commandQueue.unshift(item);\n                }\n                break;\n            }\n            default: {\n                const item = this.shiftCommand(reply);\n                if (!item) {\n                    return;\n                }\n                item.command.resolve(reply);\n            }\n        }\n        return true;\n    }\n    handleMonitorReply(reply) {\n        if (this.redis.status !== \"monitoring\") {\n            return false;\n        }\n        const replyStr = reply.toString();\n        if (replyStr === \"OK\") {\n            // Valid commands in the monitoring mode are AUTH and MONITOR,\n            // both of which always reply with 'OK'.\n            // So if we got an 'OK', we can make certain that\n            // the reply is made to AUTH & MONITOR.\n            return false;\n        }\n        // Since commands sent in the monitoring mode will trigger an exception,\n        // any replies we received in the monitoring mode should consider to be\n        // realtime monitor data instead of result of commands.\n        const len = replyStr.indexOf(\" \");\n        const timestamp = replyStr.slice(0, len);\n        const argIndex = replyStr.indexOf('\"');\n        const args = replyStr\n            .slice(argIndex + 1, -1)\n            .split('\" \"')\n            .map((elem) => elem.replace(/\\\\\"/g, '\"'));\n        const dbAndSource = replyStr.slice(len + 2, argIndex - 2).split(\" \");\n        this.redis.emit(\"monitor\", timestamp, args, dbAndSource[1], dbAndSource[0]);\n        return true;\n    }\n    shiftCommand(reply) {\n        const item = this.redis.commandQueue.shift();\n        if (!item) {\n            const message = \"Command queue state error. If you can reproduce this, please report it.\";\n            const error = new Error(message +\n                (reply instanceof Error\n                    ? ` Last error: ${reply.message}`\n                    : ` Last reply: ${reply.toString()}`));\n            this.redis.emit(\"error\", error);\n            return null;\n        }\n        return item;\n    }\n}\nexports.default = DataHandler;\nconst remainingRepliesMap = new WeakMap();\nfunction fillSubCommand(command, count) {\n    let remainingReplies = remainingRepliesMap.has(command)\n        ? remainingRepliesMap.get(command)\n        : command.args.length;\n    remainingReplies -= 1;\n    if (remainingReplies <= 0) {\n        command.resolve(count);\n        remainingRepliesMap.delete(command);\n        return true;\n    }\n    remainingRepliesMap.set(command, remainingReplies);\n    return false;\n}\nfunction fillUnsubCommand(command, count) {\n    let remainingReplies = remainingRepliesMap.has(command)\n        ? remainingRepliesMap.get(command)\n        : command.args.length;\n    if (remainingReplies === 0) {\n        if (Number(count) === 0) {\n            remainingRepliesMap.delete(command);\n            command.resolve(count);\n            return true;\n        }\n        return false;\n    }\n    remainingReplies -= 1;\n    if (remainingReplies <= 0) {\n        command.resolve(count);\n        return true;\n    }\n    remainingRepliesMap.set(command, remainingReplies);\n    return false;\n}\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAQ,KAAK,EAAE;AACjC,MAAM;IACF,YAAY,KAAK,EAAE,aAAa,CAAE;QAC9B,IAAI,CAAC,KAAK,GAAG;QACb,MAAM,SAAS,IAAI,YAAY;YAC3B,eAAe,cAAc,aAAa;YAC1C,eAAe;YACf,aAAa,CAAC;gBACV,IAAI,CAAC,WAAW,CAAC;YACrB;YACA,kBAAkB,CAAC;gBACf,IAAI,CAAC,gBAAgB,CAAC;YAC1B;YACA,aAAa,CAAC;gBACV,IAAI,CAAC,WAAW,CAAC;YACrB;QACJ;QACA,4GAA4G;QAC5G,MAAM,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;YAClC,OAAO,OAAO,CAAC;QACnB;QACA,sGAAsG;QACtG,MAAM,MAAM,CAAC,MAAM;IACvB;IACA,iBAAiB,GAAG,EAAE;QAClB,IAAI,OAAO,IAAI;QACf,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,KAAK,KAAK;YAAE,cAAc;QAAM;IACrE;IACA,YAAY,GAAG,EAAE;QACb,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;QAC/B,IAAI,CAAC,MAAM;YACP;QACJ;QACA,IAAI,OAAO,GAAG;YACV,MAAM,KAAK,OAAO,CAAC,IAAI;YACvB,MAAM,KAAK,OAAO,CAAC,IAAI;QAC3B;QACA,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,KAAK;IACvC;IACA,YAAY,KAAK,EAAE;QACf,IAAI,IAAI,CAAC,kBAAkB,CAAC,QAAQ;YAChC;QACJ;QACA,IAAI,IAAI,CAAC,qBAAqB,CAAC,QAAQ;YACnC;QACJ;QACA,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;QAC/B,IAAI,CAAC,MAAM;YACP;QACJ;QACA,IAAI,UAAU,OAAO,CAAC,SAAS,CAAC,yBAAyB,KAAK,OAAO,CAAC,IAAI,GAAG;YACzE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,GAAG,IAAI,kBAAkB,OAAO;YAC/D,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ;YACxE,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE,KAAK,CAAC,EAAE,GAAG;gBACzC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC;YACpC;QACJ,OACK,IAAI,UAAU,OAAO,CAAC,SAAS,CAAC,wBAAwB,KAAK,OAAO,CAAC,IAAI,GAAG;YAC7E,IAAI,CAAC,iBAAiB,KAAK,OAAO,EAAE,KAAK,CAAC,EAAE,GAAG;gBAC3C,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC;YACpC;QACJ,OACK;YACD,KAAK,OAAO,CAAC,OAAO,CAAC;QACzB;IACJ;IACA,sBAAsB,KAAK,EAAE;QACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE;YAClC,OAAO;QACX;QACA,MAAM,YAAY,MAAM,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC,QAAQ,KAAK;QAC/D,MAAM,yCAAyC;QAC/C,OAAQ;YACJ,KAAK;gBACD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,WAAW,MAAM,GAAG,GAAG;oBAC5C,iEAAiE;oBACjE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE,CAAC,QAAQ,IAAI,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,QAAQ,KAAK;gBACrF;gBACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;gBACnD;YACJ,KAAK;gBAAY;oBACb,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,QAAQ;oBACjC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,YAAY,MAAM,GAAG,GAAG;wBAC7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,SAAS,KAAK,CAAC,EAAE,CAAC,QAAQ,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ;oBAC/E;oBACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,SAAS,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;oBAC7D;gBACJ;YACA,KAAK;gBAAY;oBACb,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,YAAY,MAAM,GAAG,GAAG;wBAC7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC,QAAQ,IAAI,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,QAAQ,KAAK;oBACtF;oBACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;oBACpD;gBACJ;YACA,KAAK;YACL,KAAK;YACL,KAAK;gBAAc;oBACf,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,QAAQ;oBACjC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW;oBAC/C,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;oBAC/B,IAAI,CAAC,MAAM;wBACP;oBACJ;oBACA,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE,KAAK,CAAC,EAAE,GAAG;wBACzC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC;oBACpC;oBACA;gBACJ;YACA,KAAK;YACL,KAAK;YACL,KAAK;gBAAgB;oBACjB,MAAM,UAAU,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,QAAQ,KAAK;oBACjD,IAAI,SAAS;wBACT,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW;oBACnD;oBACA,MAAM,QAAQ,KAAK,CAAC,EAAE;oBACtB,IAAI,OAAO,WAAW,GAAG;wBACrB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,GAAG;oBACtC;oBACA,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;oBAC/B,IAAI,CAAC,MAAM;wBACP;oBACJ;oBACA,IAAI,CAAC,iBAAiB,KAAK,OAAO,EAAE,QAAQ;wBACxC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC;oBACpC;oBACA;gBACJ;YACA;gBAAS;oBACL,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;oBAC/B,IAAI,CAAC,MAAM;wBACP;oBACJ;oBACA,KAAK,OAAO,CAAC,OAAO,CAAC;gBACzB;QACJ;QACA,OAAO;IACX;IACA,mBAAmB,KAAK,EAAE;QACtB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,cAAc;YACpC,OAAO;QACX;QACA,MAAM,WAAW,MAAM,QAAQ;QAC/B,IAAI,aAAa,MAAM;YACnB,8DAA8D;YAC9D,wCAAwC;YACxC,iDAAiD;YACjD,uCAAuC;YACvC,OAAO;QACX;QACA,wEAAwE;QACxE,uEAAuE;QACvE,uDAAuD;QACvD,MAAM,MAAM,SAAS,OAAO,CAAC;QAC7B,MAAM,YAAY,SAAS,KAAK,CAAC,GAAG;QACpC,MAAM,WAAW,SAAS,OAAO,CAAC;QAClC,MAAM,OAAO,SACR,KAAK,CAAC,WAAW,GAAG,CAAC,GACrB,KAAK,CAAC,OACN,GAAG,CAAC,CAAC,OAAS,KAAK,OAAO,CAAC,QAAQ;QACxC,MAAM,cAAc,SAAS,KAAK,CAAC,MAAM,GAAG,WAAW,GAAG,KAAK,CAAC;QAChE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,WAAW,MAAM,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE;QAC1E,OAAO;IACX;IACA,aAAa,KAAK,EAAE;QAChB,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK;QAC1C,IAAI,CAAC,MAAM;YACP,MAAM,UAAU;YAChB,MAAM,QAAQ,IAAI,MAAM,UACpB,CAAC,iBAAiB,QACZ,CAAC,aAAa,EAAE,MAAM,OAAO,EAAE,GAC/B,CAAC,aAAa,EAAE,MAAM,QAAQ,IAAI;YAC5C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS;YACzB,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,QAAQ,OAAO,GAAG;AAClB,MAAM,sBAAsB,IAAI;AAChC,SAAS,eAAe,OAAO,EAAE,KAAK;IAClC,IAAI,mBAAmB,oBAAoB,GAAG,CAAC,WACzC,oBAAoB,GAAG,CAAC,WACxB,QAAQ,IAAI,CAAC,MAAM;IACzB,oBAAoB;IACpB,IAAI,oBAAoB,GAAG;QACvB,QAAQ,OAAO,CAAC;QAChB,oBAAoB,MAAM,CAAC;QAC3B,OAAO;IACX;IACA,oBAAoB,GAAG,CAAC,SAAS;IACjC,OAAO;AACX;AACA,SAAS,iBAAiB,OAAO,EAAE,KAAK;IACpC,IAAI,mBAAmB,oBAAoB,GAAG,CAAC,WACzC,oBAAoB,GAAG,CAAC,WACxB,QAAQ,IAAI,CAAC,MAAM;IACzB,IAAI,qBAAqB,GAAG;QACxB,IAAI,OAAO,WAAW,GAAG;YACrB,oBAAoB,MAAM,CAAC;YAC3B,QAAQ,OAAO,CAAC;YAChB,OAAO;QACX;QACA,OAAO;IACX;IACA,oBAAoB;IACpB,IAAI,oBAAoB,GAAG;QACvB,QAAQ,OAAO,CAAC;QAChB,OAAO;IACX;IACA,oBAAoB,GAAG,CAAC,SAAS;IACjC,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4197, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/redis/event_handler.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.readyHandler = exports.errorHandler = exports.closeHandler = exports.connectHandler = void 0;\nconst redis_errors_1 = require(\"redis-errors\");\nconst Command_1 = require(\"../Command\");\nconst errors_1 = require(\"../errors\");\nconst utils_1 = require(\"../utils\");\nconst DataHandler_1 = require(\"../DataHandler\");\nconst debug = (0, utils_1.Debug)(\"connection\");\nfunction connectHandler(self) {\n    return function () {\n        self.setStatus(\"connect\");\n        self.resetCommandQueue();\n        // AUTH command should be processed before any other commands\n        let flushed = false;\n        const { connectionEpoch } = self;\n        if (self.condition.auth) {\n            self.auth(self.condition.auth, function (err) {\n                if (connectionEpoch !== self.connectionEpoch) {\n                    return;\n                }\n                if (err) {\n                    if (err.message.indexOf(\"no password is set\") !== -1) {\n                        console.warn(\"[WARN] Redis server does not require a password, but a password was supplied.\");\n                    }\n                    else if (err.message.indexOf(\"without any password configured for the default user\") !== -1) {\n                        console.warn(\"[WARN] This Redis server's `default` user does not require a password, but a password was supplied\");\n                    }\n                    else if (err.message.indexOf(\"wrong number of arguments for 'auth' command\") !== -1) {\n                        console.warn(`[ERROR] The server returned \"wrong number of arguments for 'auth' command\". You are probably passing both username and password to Redis version 5 or below. You should only pass the 'password' option for Redis version 5 and under.`);\n                    }\n                    else {\n                        flushed = true;\n                        self.recoverFromFatalError(err, err);\n                    }\n                }\n            });\n        }\n        if (self.condition.select) {\n            self.select(self.condition.select).catch((err) => {\n                // If the node is in cluster mode, select is disallowed.\n                // In this case, reconnect won't help.\n                self.silentEmit(\"error\", err);\n            });\n        }\n        if (!self.options.enableReadyCheck) {\n            exports.readyHandler(self)();\n        }\n        /*\n          No need to keep the reference of DataHandler here\n          because we don't need to do the cleanup.\n          `Stream#end()` will remove all listeners for us.\n        */\n        new DataHandler_1.default(self, {\n            stringNumbers: self.options.stringNumbers,\n        });\n        if (self.options.enableReadyCheck) {\n            self._readyCheck(function (err, info) {\n                if (connectionEpoch !== self.connectionEpoch) {\n                    return;\n                }\n                if (err) {\n                    if (!flushed) {\n                        self.recoverFromFatalError(new Error(\"Ready check failed: \" + err.message), err);\n                    }\n                }\n                else {\n                    if (self.connector.check(info)) {\n                        exports.readyHandler(self)();\n                    }\n                    else {\n                        self.disconnect(true);\n                    }\n                }\n            });\n        }\n    };\n}\nexports.connectHandler = connectHandler;\nfunction abortError(command) {\n    const err = new redis_errors_1.AbortError(\"Command aborted due to connection close\");\n    err.command = {\n        name: command.name,\n        args: command.args,\n    };\n    return err;\n}\n// If a contiguous set of pipeline commands starts from index zero then they\n// can be safely reattempted. If however we have a chain of pipelined commands\n// starting at index 1 or more it means we received a partial response before\n// the connection close and those pipelined commands must be aborted. For\n// example, if the queue looks like this: [2, 3, 4, 0, 1, 2] then after\n// aborting and purging we'll have a queue that looks like this: [0, 1, 2]\nfunction abortIncompletePipelines(commandQueue) {\n    var _a;\n    let expectedIndex = 0;\n    for (let i = 0; i < commandQueue.length;) {\n        const command = (_a = commandQueue.peekAt(i)) === null || _a === void 0 ? void 0 : _a.command;\n        const pipelineIndex = command.pipelineIndex;\n        if (pipelineIndex === undefined || pipelineIndex === 0) {\n            expectedIndex = 0;\n        }\n        if (pipelineIndex !== undefined && pipelineIndex !== expectedIndex++) {\n            commandQueue.remove(i, 1);\n            command.reject(abortError(command));\n            continue;\n        }\n        i++;\n    }\n}\n// If only a partial transaction result was received before connection close,\n// we have to abort any transaction fragments that may have ended up in the\n// offline queue\nfunction abortTransactionFragments(commandQueue) {\n    var _a;\n    for (let i = 0; i < commandQueue.length;) {\n        const command = (_a = commandQueue.peekAt(i)) === null || _a === void 0 ? void 0 : _a.command;\n        if (command.name === \"multi\") {\n            break;\n        }\n        if (command.name === \"exec\") {\n            commandQueue.remove(i, 1);\n            command.reject(abortError(command));\n            break;\n        }\n        if (command.inTransaction) {\n            commandQueue.remove(i, 1);\n            command.reject(abortError(command));\n        }\n        else {\n            i++;\n        }\n    }\n}\nfunction closeHandler(self) {\n    return function () {\n        const prevStatus = self.status;\n        self.setStatus(\"close\");\n        if (self.commandQueue.length) {\n            abortIncompletePipelines(self.commandQueue);\n        }\n        if (self.offlineQueue.length) {\n            abortTransactionFragments(self.offlineQueue);\n        }\n        if (prevStatus === \"ready\") {\n            if (!self.prevCondition) {\n                self.prevCondition = self.condition;\n            }\n            if (self.commandQueue.length) {\n                self.prevCommandQueue = self.commandQueue;\n            }\n        }\n        if (self.manuallyClosing) {\n            self.manuallyClosing = false;\n            debug(\"skip reconnecting since the connection is manually closed.\");\n            return close();\n        }\n        if (typeof self.options.retryStrategy !== \"function\") {\n            debug(\"skip reconnecting because `retryStrategy` is not a function\");\n            return close();\n        }\n        const retryDelay = self.options.retryStrategy(++self.retryAttempts);\n        if (typeof retryDelay !== \"number\") {\n            debug(\"skip reconnecting because `retryStrategy` doesn't return a number\");\n            return close();\n        }\n        debug(\"reconnect in %sms\", retryDelay);\n        self.setStatus(\"reconnecting\", retryDelay);\n        self.reconnectTimeout = setTimeout(function () {\n            self.reconnectTimeout = null;\n            self.connect().catch(utils_1.noop);\n        }, retryDelay);\n        const { maxRetriesPerRequest } = self.options;\n        if (typeof maxRetriesPerRequest === \"number\") {\n            if (maxRetriesPerRequest < 0) {\n                debug(\"maxRetriesPerRequest is negative, ignoring...\");\n            }\n            else {\n                const remainder = self.retryAttempts % (maxRetriesPerRequest + 1);\n                if (remainder === 0) {\n                    debug(\"reach maxRetriesPerRequest limitation, flushing command queue...\");\n                    self.flushQueue(new errors_1.MaxRetriesPerRequestError(maxRetriesPerRequest));\n                }\n            }\n        }\n    };\n    function close() {\n        self.setStatus(\"end\");\n        self.flushQueue(new Error(utils_1.CONNECTION_CLOSED_ERROR_MSG));\n    }\n}\nexports.closeHandler = closeHandler;\nfunction errorHandler(self) {\n    return function (error) {\n        debug(\"error: %s\", error);\n        self.silentEmit(\"error\", error);\n    };\n}\nexports.errorHandler = errorHandler;\nfunction readyHandler(self) {\n    return function () {\n        self.setStatus(\"ready\");\n        self.retryAttempts = 0;\n        if (self.options.monitor) {\n            self.call(\"monitor\").then(() => self.setStatus(\"monitoring\"), (error) => self.emit(\"error\", error));\n            const { sendCommand } = self;\n            self.sendCommand = function (command) {\n                if (Command_1.default.checkFlag(\"VALID_IN_MONITOR_MODE\", command.name)) {\n                    return sendCommand.call(self, command);\n                }\n                command.reject(new Error(\"Connection is in monitoring mode, can't process commands.\"));\n                return command.promise;\n            };\n            self.once(\"close\", function () {\n                delete self.sendCommand;\n            });\n            return;\n        }\n        const finalSelect = self.prevCondition\n            ? self.prevCondition.select\n            : self.condition.select;\n        if (self.options.connectionName) {\n            debug(\"set the connection name [%s]\", self.options.connectionName);\n            self.client(\"setname\", self.options.connectionName).catch(utils_1.noop);\n        }\n        if (self.options.readOnly) {\n            debug(\"set the connection to readonly mode\");\n            self.readonly().catch(utils_1.noop);\n        }\n        if (self.prevCondition) {\n            const condition = self.prevCondition;\n            self.prevCondition = null;\n            if (condition.subscriber && self.options.autoResubscribe) {\n                // We re-select the previous db first since\n                // `SELECT` command is not valid in sub mode.\n                if (self.condition.select !== finalSelect) {\n                    debug(\"connect to db [%d]\", finalSelect);\n                    self.select(finalSelect);\n                }\n                const subscribeChannels = condition.subscriber.channels(\"subscribe\");\n                if (subscribeChannels.length) {\n                    debug(\"subscribe %d channels\", subscribeChannels.length);\n                    self.subscribe(subscribeChannels);\n                }\n                const psubscribeChannels = condition.subscriber.channels(\"psubscribe\");\n                if (psubscribeChannels.length) {\n                    debug(\"psubscribe %d channels\", psubscribeChannels.length);\n                    self.psubscribe(psubscribeChannels);\n                }\n                const ssubscribeChannels = condition.subscriber.channels(\"ssubscribe\");\n                if (ssubscribeChannels.length) {\n                    debug(\"ssubscribe %d channels\", ssubscribeChannels.length);\n                    self.ssubscribe(ssubscribeChannels);\n                }\n            }\n        }\n        if (self.prevCommandQueue) {\n            if (self.options.autoResendUnfulfilledCommands) {\n                debug(\"resend %d unfulfilled commands\", self.prevCommandQueue.length);\n                while (self.prevCommandQueue.length > 0) {\n                    const item = self.prevCommandQueue.shift();\n                    if (item.select !== self.condition.select &&\n                        item.command.name !== \"select\") {\n                        self.select(item.select);\n                    }\n                    self.sendCommand(item.command, item.stream);\n                }\n            }\n            else {\n                self.prevCommandQueue = null;\n            }\n        }\n        if (self.offlineQueue.length) {\n            debug(\"send %d commands in offline queue\", self.offlineQueue.length);\n            const offlineQueue = self.offlineQueue;\n            self.resetOfflineQueue();\n            while (offlineQueue.length > 0) {\n                const item = offlineQueue.shift();\n                if (item.select !== self.condition.select &&\n                    item.command.name !== \"select\") {\n                    self.select(item.select);\n                }\n                self.sendCommand(item.command, item.stream);\n            }\n        }\n        if (self.condition.select !== finalSelect) {\n            debug(\"connect to db [%d]\", finalSelect);\n            self.select(finalSelect);\n        }\n    };\n}\nexports.readyHandler = readyHandler;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,YAAY,GAAG,QAAQ,YAAY,GAAG,QAAQ,YAAY,GAAG,QAAQ,cAAc,GAAG,KAAK;AACnG,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAQ,KAAK,EAAE;AACjC,SAAS,eAAe,IAAI;IACxB,OAAO;QACH,KAAK,SAAS,CAAC;QACf,KAAK,iBAAiB;QACtB,6DAA6D;QAC7D,IAAI,UAAU;QACd,MAAM,EAAE,eAAe,EAAE,GAAG;QAC5B,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE;YACrB,KAAK,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,EAAE,SAAU,GAAG;gBACxC,IAAI,oBAAoB,KAAK,eAAe,EAAE;oBAC1C;gBACJ;gBACA,IAAI,KAAK;oBACL,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,0BAA0B,CAAC,GAAG;wBAClD,QAAQ,IAAI,CAAC;oBACjB,OACK,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,4DAA4D,CAAC,GAAG;wBACzF,QAAQ,IAAI,CAAC;oBACjB,OACK,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,oDAAoD,CAAC,GAAG;wBACjF,QAAQ,IAAI,CAAC,CAAC,sOAAsO,CAAC;oBACzP,OACK;wBACD,UAAU;wBACV,KAAK,qBAAqB,CAAC,KAAK;oBACpC;gBACJ;YACJ;QACJ;QACA,IAAI,KAAK,SAAS,CAAC,MAAM,EAAE;YACvB,KAAK,MAAM,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACtC,wDAAwD;gBACxD,sCAAsC;gBACtC,KAAK,UAAU,CAAC,SAAS;YAC7B;QACJ;QACA,IAAI,CAAC,KAAK,OAAO,CAAC,gBAAgB,EAAE;YAChC,QAAQ,YAAY,CAAC;QACzB;QACA;;;;QAIA,GACA,IAAI,cAAc,OAAO,CAAC,MAAM;YAC5B,eAAe,KAAK,OAAO,CAAC,aAAa;QAC7C;QACA,IAAI,KAAK,OAAO,CAAC,gBAAgB,EAAE;YAC/B,KAAK,WAAW,CAAC,SAAU,GAAG,EAAE,IAAI;gBAChC,IAAI,oBAAoB,KAAK,eAAe,EAAE;oBAC1C;gBACJ;gBACA,IAAI,KAAK;oBACL,IAAI,CAAC,SAAS;wBACV,KAAK,qBAAqB,CAAC,IAAI,MAAM,yBAAyB,IAAI,OAAO,GAAG;oBAChF;gBACJ,OACK;oBACD,IAAI,KAAK,SAAS,CAAC,KAAK,CAAC,OAAO;wBAC5B,QAAQ,YAAY,CAAC;oBACzB,OACK;wBACD,KAAK,UAAU,CAAC;oBACpB;gBACJ;YACJ;QACJ;IACJ;AACJ;AACA,QAAQ,cAAc,GAAG;AACzB,SAAS,WAAW,OAAO;IACvB,MAAM,MAAM,IAAI,eAAe,UAAU,CAAC;IAC1C,IAAI,OAAO,GAAG;QACV,MAAM,QAAQ,IAAI;QAClB,MAAM,QAAQ,IAAI;IACtB;IACA,OAAO;AACX;AACA,4EAA4E;AAC5E,8EAA8E;AAC9E,6EAA6E;AAC7E,yEAAyE;AACzE,uEAAuE;AACvE,0EAA0E;AAC1E,SAAS,yBAAyB,YAAY;IAC1C,IAAI;IACJ,IAAI,gBAAgB;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAG;QACtC,MAAM,UAAU,CAAC,KAAK,aAAa,MAAM,CAAC,EAAE,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO;QAC7F,MAAM,gBAAgB,QAAQ,aAAa;QAC3C,IAAI,kBAAkB,aAAa,kBAAkB,GAAG;YACpD,gBAAgB;QACpB;QACA,IAAI,kBAAkB,aAAa,kBAAkB,iBAAiB;YAClE,aAAa,MAAM,CAAC,GAAG;YACvB,QAAQ,MAAM,CAAC,WAAW;YAC1B;QACJ;QACA;IACJ;AACJ;AACA,6EAA6E;AAC7E,2EAA2E;AAC3E,gBAAgB;AAChB,SAAS,0BAA0B,YAAY;IAC3C,IAAI;IACJ,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAG;QACtC,MAAM,UAAU,CAAC,KAAK,aAAa,MAAM,CAAC,EAAE,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO;QAC7F,IAAI,QAAQ,IAAI,KAAK,SAAS;YAC1B;QACJ;QACA,IAAI,QAAQ,IAAI,KAAK,QAAQ;YACzB,aAAa,MAAM,CAAC,GAAG;YACvB,QAAQ,MAAM,CAAC,WAAW;YAC1B;QACJ;QACA,IAAI,QAAQ,aAAa,EAAE;YACvB,aAAa,MAAM,CAAC,GAAG;YACvB,QAAQ,MAAM,CAAC,WAAW;QAC9B,OACK;YACD;QACJ;IACJ;AACJ;AACA,SAAS,aAAa,IAAI;IACtB,OAAO;QACH,MAAM,aAAa,KAAK,MAAM;QAC9B,KAAK,SAAS,CAAC;QACf,IAAI,KAAK,YAAY,CAAC,MAAM,EAAE;YAC1B,yBAAyB,KAAK,YAAY;QAC9C;QACA,IAAI,KAAK,YAAY,CAAC,MAAM,EAAE;YAC1B,0BAA0B,KAAK,YAAY;QAC/C;QACA,IAAI,eAAe,SAAS;YACxB,IAAI,CAAC,KAAK,aAAa,EAAE;gBACrB,KAAK,aAAa,GAAG,KAAK,SAAS;YACvC;YACA,IAAI,KAAK,YAAY,CAAC,MAAM,EAAE;gBAC1B,KAAK,gBAAgB,GAAG,KAAK,YAAY;YAC7C;QACJ;QACA,IAAI,KAAK,eAAe,EAAE;YACtB,KAAK,eAAe,GAAG;YACvB,MAAM;YACN,OAAO;QACX;QACA,IAAI,OAAO,KAAK,OAAO,CAAC,aAAa,KAAK,YAAY;YAClD,MAAM;YACN,OAAO;QACX;QACA,MAAM,aAAa,KAAK,OAAO,CAAC,aAAa,CAAC,EAAE,KAAK,aAAa;QAClE,IAAI,OAAO,eAAe,UAAU;YAChC,MAAM;YACN,OAAO;QACX;QACA,MAAM,qBAAqB;QAC3B,KAAK,SAAS,CAAC,gBAAgB;QAC/B,KAAK,gBAAgB,GAAG,WAAW;YAC/B,KAAK,gBAAgB,GAAG;YACxB,KAAK,OAAO,GAAG,KAAK,CAAC,QAAQ,IAAI;QACrC,GAAG;QACH,MAAM,EAAE,oBAAoB,EAAE,GAAG,KAAK,OAAO;QAC7C,IAAI,OAAO,yBAAyB,UAAU;YAC1C,IAAI,uBAAuB,GAAG;gBAC1B,MAAM;YACV,OACK;gBACD,MAAM,YAAY,KAAK,aAAa,GAAG,CAAC,uBAAuB,CAAC;gBAChE,IAAI,cAAc,GAAG;oBACjB,MAAM;oBACN,KAAK,UAAU,CAAC,IAAI,SAAS,yBAAyB,CAAC;gBAC3D;YACJ;QACJ;IACJ;;;IACA,SAAS;QACL,KAAK,SAAS,CAAC;QACf,KAAK,UAAU,CAAC,IAAI,MAAM,QAAQ,2BAA2B;IACjE;AACJ;AACA,QAAQ,YAAY,GAAG;AACvB,SAAS,aAAa,IAAI;IACtB,OAAO,SAAU,KAAK;QAClB,MAAM,aAAa;QACnB,KAAK,UAAU,CAAC,SAAS;IAC7B;AACJ;AACA,QAAQ,YAAY,GAAG;AACvB,SAAS,aAAa,IAAI;IACtB,OAAO;QACH,KAAK,SAAS,CAAC;QACf,KAAK,aAAa,GAAG;QACrB,IAAI,KAAK,OAAO,CAAC,OAAO,EAAE;YACtB,KAAK,IAAI,CAAC,WAAW,IAAI,CAAC,IAAM,KAAK,SAAS,CAAC,eAAe,CAAC,QAAU,KAAK,IAAI,CAAC,SAAS;YAC5F,MAAM,EAAE,WAAW,EAAE,GAAG;YACxB,KAAK,WAAW,GAAG,SAAU,OAAO;gBAChC,IAAI,UAAU,OAAO,CAAC,SAAS,CAAC,yBAAyB,QAAQ,IAAI,GAAG;oBACpE,OAAO,YAAY,IAAI,CAAC,MAAM;gBAClC;gBACA,QAAQ,MAAM,CAAC,IAAI,MAAM;gBACzB,OAAO,QAAQ,OAAO;YAC1B;YACA,KAAK,IAAI,CAAC,SAAS;gBACf,OAAO,KAAK,WAAW;YAC3B;YACA;QACJ;QACA,MAAM,cAAc,KAAK,aAAa,GAChC,KAAK,aAAa,CAAC,MAAM,GACzB,KAAK,SAAS,CAAC,MAAM;QAC3B,IAAI,KAAK,OAAO,CAAC,cAAc,EAAE;YAC7B,MAAM,gCAAgC,KAAK,OAAO,CAAC,cAAc;YACjE,KAAK,MAAM,CAAC,WAAW,KAAK,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC,QAAQ,IAAI;QAC1E;QACA,IAAI,KAAK,OAAO,CAAC,QAAQ,EAAE;YACvB,MAAM;YACN,KAAK,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI;QACtC;QACA,IAAI,KAAK,aAAa,EAAE;YACpB,MAAM,YAAY,KAAK,aAAa;YACpC,KAAK,aAAa,GAAG;YACrB,IAAI,UAAU,UAAU,IAAI,KAAK,OAAO,CAAC,eAAe,EAAE;gBACtD,2CAA2C;gBAC3C,6CAA6C;gBAC7C,IAAI,KAAK,SAAS,CAAC,MAAM,KAAK,aAAa;oBACvC,MAAM,sBAAsB;oBAC5B,KAAK,MAAM,CAAC;gBAChB;gBACA,MAAM,oBAAoB,UAAU,UAAU,CAAC,QAAQ,CAAC;gBACxD,IAAI,kBAAkB,MAAM,EAAE;oBAC1B,MAAM,yBAAyB,kBAAkB,MAAM;oBACvD,KAAK,SAAS,CAAC;gBACnB;gBACA,MAAM,qBAAqB,UAAU,UAAU,CAAC,QAAQ,CAAC;gBACzD,IAAI,mBAAmB,MAAM,EAAE;oBAC3B,MAAM,0BAA0B,mBAAmB,MAAM;oBACzD,KAAK,UAAU,CAAC;gBACpB;gBACA,MAAM,qBAAqB,UAAU,UAAU,CAAC,QAAQ,CAAC;gBACzD,IAAI,mBAAmB,MAAM,EAAE;oBAC3B,MAAM,0BAA0B,mBAAmB,MAAM;oBACzD,KAAK,UAAU,CAAC;gBACpB;YACJ;QACJ;QACA,IAAI,KAAK,gBAAgB,EAAE;YACvB,IAAI,KAAK,OAAO,CAAC,6BAA6B,EAAE;gBAC5C,MAAM,kCAAkC,KAAK,gBAAgB,CAAC,MAAM;gBACpE,MAAO,KAAK,gBAAgB,CAAC,MAAM,GAAG,EAAG;oBACrC,MAAM,OAAO,KAAK,gBAAgB,CAAC,KAAK;oBACxC,IAAI,KAAK,MAAM,KAAK,KAAK,SAAS,CAAC,MAAM,IACrC,KAAK,OAAO,CAAC,IAAI,KAAK,UAAU;wBAChC,KAAK,MAAM,CAAC,KAAK,MAAM;oBAC3B;oBACA,KAAK,WAAW,CAAC,KAAK,OAAO,EAAE,KAAK,MAAM;gBAC9C;YACJ,OACK;gBACD,KAAK,gBAAgB,GAAG;YAC5B;QACJ;QACA,IAAI,KAAK,YAAY,CAAC,MAAM,EAAE;YAC1B,MAAM,qCAAqC,KAAK,YAAY,CAAC,MAAM;YACnE,MAAM,eAAe,KAAK,YAAY;YACtC,KAAK,iBAAiB;YACtB,MAAO,aAAa,MAAM,GAAG,EAAG;gBAC5B,MAAM,OAAO,aAAa,KAAK;gBAC/B,IAAI,KAAK,MAAM,KAAK,KAAK,SAAS,CAAC,MAAM,IACrC,KAAK,OAAO,CAAC,IAAI,KAAK,UAAU;oBAChC,KAAK,MAAM,CAAC,KAAK,MAAM;gBAC3B;gBACA,KAAK,WAAW,CAAC,KAAK,OAAO,EAAE,KAAK,MAAM;YAC9C;QACJ;QACA,IAAI,KAAK,SAAS,CAAC,MAAM,KAAK,aAAa;YACvC,MAAM,sBAAsB;YAC5B,KAAK,MAAM,CAAC;QAChB;IACJ;AACJ;AACA,QAAQ,YAAY,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4485, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/redis/RedisOptions.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DEFAULT_REDIS_OPTIONS = void 0;\nexports.DEFAULT_REDIS_OPTIONS = {\n    // Connection\n    port: 6379,\n    host: \"localhost\",\n    family: 4,\n    connectTimeout: 10000,\n    disconnectTimeout: 2000,\n    retryStrategy: function (times) {\n        return Math.min(times * 50, 2000);\n    },\n    keepAlive: 0,\n    noDelay: true,\n    connectionName: null,\n    // Sentinel\n    sentinels: null,\n    name: null,\n    role: \"master\",\n    sentinelRetryStrategy: function (times) {\n        return Math.min(times * 10, 1000);\n    },\n    sentinelReconnectStrategy: function () {\n        // This strategy only applies when sentinels are used for detecting\n        // a failover, not during initial master resolution.\n        // The deployment can still function when some of the sentinels are down\n        // for a long period of time, so we may not want to attempt reconnection\n        // very often. Therefore the default interval is fairly long (1 minute).\n        return 60000;\n    },\n    natMap: null,\n    enableTLSForSentinelMode: false,\n    updateSentinels: true,\n    failoverDetector: false,\n    // Status\n    username: null,\n    password: null,\n    db: 0,\n    // Others\n    enableOfflineQueue: true,\n    enableReadyCheck: true,\n    autoResubscribe: true,\n    autoResendUnfulfilledCommands: true,\n    lazyConnect: false,\n    keyPrefix: \"\",\n    reconnectOnError: null,\n    readOnly: false,\n    stringNumbers: false,\n    maxRetriesPerRequest: 20,\n    maxLoadingRetryTime: 10000,\n    enableAutoPipelining: false,\n    autoPipeliningIgnoredCommands: [],\n    sentinelMaxConnections: 10,\n};\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,qBAAqB,GAAG,KAAK;AACrC,QAAQ,qBAAqB,GAAG;IAC5B,aAAa;IACb,MAAM;IACN,MAAM;IACN,QAAQ;IACR,gBAAgB;IAChB,mBAAmB;IACnB,eAAe,SAAU,KAAK;QAC1B,OAAO,KAAK,GAAG,CAAC,QAAQ,IAAI;IAChC;IACA,WAAW;IACX,SAAS;IACT,gBAAgB;IAChB,WAAW;IACX,WAAW;IACX,MAAM;IACN,MAAM;IACN,uBAAuB,SAAU,KAAK;QAClC,OAAO,KAAK,GAAG,CAAC,QAAQ,IAAI;IAChC;IACA,2BAA2B;QACvB,mEAAmE;QACnE,oDAAoD;QACpD,wEAAwE;QACxE,wEAAwE;QACxE,wEAAwE;QACxE,OAAO;IACX;IACA,QAAQ;IACR,0BAA0B;IAC1B,iBAAiB;IACjB,kBAAkB;IAClB,SAAS;IACT,UAAU;IACV,UAAU;IACV,IAAI;IACJ,SAAS;IACT,oBAAoB;IACpB,kBAAkB;IAClB,iBAAiB;IACjB,+BAA+B;IAC/B,aAAa;IACb,WAAW;IACX,kBAAkB;IAClB,UAAU;IACV,eAAe;IACf,sBAAsB;IACtB,qBAAqB;IACrB,sBAAsB;IACtB,+BAA+B,EAAE;IACjC,wBAAwB;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4547, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/Redis.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst commands_1 = require(\"@ioredis/commands\");\nconst events_1 = require(\"events\");\nconst standard_as_callback_1 = require(\"standard-as-callback\");\nconst cluster_1 = require(\"./cluster\");\nconst Command_1 = require(\"./Command\");\nconst connectors_1 = require(\"./connectors\");\nconst SentinelConnector_1 = require(\"./connectors/SentinelConnector\");\nconst eventHandler = require(\"./redis/event_handler\");\nconst RedisOptions_1 = require(\"./redis/RedisOptions\");\nconst ScanStream_1 = require(\"./ScanStream\");\nconst transaction_1 = require(\"./transaction\");\nconst utils_1 = require(\"./utils\");\nconst applyMixin_1 = require(\"./utils/applyMixin\");\nconst Commander_1 = require(\"./utils/Commander\");\nconst lodash_1 = require(\"./utils/lodash\");\nconst Deque = require(\"denque\");\nconst debug = (0, utils_1.Debug)(\"redis\");\n/**\n * This is the major component of ioredis.\n * Use it to connect to a standalone Redis server or Sentinels.\n *\n * ```typescript\n * const redis = new Redis(); // Default port is 6379\n * async function main() {\n *   redis.set(\"foo\", \"bar\");\n *   redis.get(\"foo\", (err, result) => {\n *     // `result` should be \"bar\"\n *     console.log(err, result);\n *   });\n *   // Or use Promise\n *   const result = await redis.get(\"foo\");\n * }\n * ```\n */\nclass Redis extends Commander_1.default {\n    constructor(arg1, arg2, arg3) {\n        super();\n        this.status = \"wait\";\n        /**\n         * @ignore\n         */\n        this.isCluster = false;\n        this.reconnectTimeout = null;\n        this.connectionEpoch = 0;\n        this.retryAttempts = 0;\n        this.manuallyClosing = false;\n        // Prepare autopipelines structures\n        this._autoPipelines = new Map();\n        this._runningAutoPipelines = new Set();\n        this.parseOptions(arg1, arg2, arg3);\n        events_1.EventEmitter.call(this);\n        this.resetCommandQueue();\n        this.resetOfflineQueue();\n        if (this.options.Connector) {\n            this.connector = new this.options.Connector(this.options);\n        }\n        else if (this.options.sentinels) {\n            const sentinelConnector = new SentinelConnector_1.default(this.options);\n            sentinelConnector.emitter = this;\n            this.connector = sentinelConnector;\n        }\n        else {\n            this.connector = new connectors_1.StandaloneConnector(this.options);\n        }\n        if (this.options.scripts) {\n            Object.entries(this.options.scripts).forEach(([name, definition]) => {\n                this.defineCommand(name, definition);\n            });\n        }\n        // end(or wait) -> connecting -> connect -> ready -> end\n        if (this.options.lazyConnect) {\n            this.setStatus(\"wait\");\n        }\n        else {\n            this.connect().catch(lodash_1.noop);\n        }\n    }\n    /**\n     * Create a Redis instance.\n     * This is the same as `new Redis()` but is included for compatibility with node-redis.\n     */\n    static createClient(...args) {\n        return new Redis(...args);\n    }\n    get autoPipelineQueueSize() {\n        let queued = 0;\n        for (const pipeline of this._autoPipelines.values()) {\n            queued += pipeline.length;\n        }\n        return queued;\n    }\n    /**\n     * Create a connection to Redis.\n     * This method will be invoked automatically when creating a new Redis instance\n     * unless `lazyConnect: true` is passed.\n     *\n     * When calling this method manually, a Promise is returned, which will\n     * be resolved when the connection status is ready.\n     */\n    connect(callback) {\n        const promise = new Promise((resolve, reject) => {\n            if (this.status === \"connecting\" ||\n                this.status === \"connect\" ||\n                this.status === \"ready\") {\n                reject(new Error(\"Redis is already connecting/connected\"));\n                return;\n            }\n            this.connectionEpoch += 1;\n            this.setStatus(\"connecting\");\n            const { options } = this;\n            this.condition = {\n                select: options.db,\n                auth: options.username\n                    ? [options.username, options.password]\n                    : options.password,\n                subscriber: false,\n            };\n            const _this = this;\n            (0, standard_as_callback_1.default)(this.connector.connect(function (type, err) {\n                _this.silentEmit(type, err);\n            }), function (err, stream) {\n                if (err) {\n                    _this.flushQueue(err);\n                    _this.silentEmit(\"error\", err);\n                    reject(err);\n                    _this.setStatus(\"end\");\n                    return;\n                }\n                let CONNECT_EVENT = options.tls ? \"secureConnect\" : \"connect\";\n                if (\"sentinels\" in options &&\n                    options.sentinels &&\n                    !options.enableTLSForSentinelMode) {\n                    CONNECT_EVENT = \"connect\";\n                }\n                _this.stream = stream;\n                if (options.noDelay) {\n                    stream.setNoDelay(true);\n                }\n                // Node ignores setKeepAlive before connect, therefore we wait for the event:\n                // https://github.com/nodejs/node/issues/31663\n                if (typeof options.keepAlive === \"number\") {\n                    if (stream.connecting) {\n                        stream.once(CONNECT_EVENT, () => {\n                            stream.setKeepAlive(true, options.keepAlive);\n                        });\n                    }\n                    else {\n                        stream.setKeepAlive(true, options.keepAlive);\n                    }\n                }\n                if (stream.connecting) {\n                    stream.once(CONNECT_EVENT, eventHandler.connectHandler(_this));\n                    if (options.connectTimeout) {\n                        /*\n                         * Typically, Socket#setTimeout(0) will clear the timer\n                         * set before. However, in some platforms (Electron 3.x~4.x),\n                         * the timer will not be cleared. So we introduce a variable here.\n                         *\n                         * See https://github.com/electron/electron/issues/14915\n                         */\n                        let connectTimeoutCleared = false;\n                        stream.setTimeout(options.connectTimeout, function () {\n                            if (connectTimeoutCleared) {\n                                return;\n                            }\n                            stream.setTimeout(0);\n                            stream.destroy();\n                            const err = new Error(\"connect ETIMEDOUT\");\n                            // @ts-expect-error\n                            err.errorno = \"ETIMEDOUT\";\n                            // @ts-expect-error\n                            err.code = \"ETIMEDOUT\";\n                            // @ts-expect-error\n                            err.syscall = \"connect\";\n                            eventHandler.errorHandler(_this)(err);\n                        });\n                        stream.once(CONNECT_EVENT, function () {\n                            connectTimeoutCleared = true;\n                            stream.setTimeout(0);\n                        });\n                    }\n                }\n                else if (stream.destroyed) {\n                    const firstError = _this.connector.firstError;\n                    if (firstError) {\n                        process.nextTick(() => {\n                            eventHandler.errorHandler(_this)(firstError);\n                        });\n                    }\n                    process.nextTick(eventHandler.closeHandler(_this));\n                }\n                else {\n                    process.nextTick(eventHandler.connectHandler(_this));\n                }\n                if (!stream.destroyed) {\n                    stream.once(\"error\", eventHandler.errorHandler(_this));\n                    stream.once(\"close\", eventHandler.closeHandler(_this));\n                }\n                const connectionReadyHandler = function () {\n                    _this.removeListener(\"close\", connectionCloseHandler);\n                    resolve();\n                };\n                var connectionCloseHandler = function () {\n                    _this.removeListener(\"ready\", connectionReadyHandler);\n                    reject(new Error(utils_1.CONNECTION_CLOSED_ERROR_MSG));\n                };\n                _this.once(\"ready\", connectionReadyHandler);\n                _this.once(\"close\", connectionCloseHandler);\n            });\n        });\n        return (0, standard_as_callback_1.default)(promise, callback);\n    }\n    /**\n     * Disconnect from Redis.\n     *\n     * This method closes the connection immediately,\n     * and may lose some pending replies that haven't written to client.\n     * If you want to wait for the pending replies, use Redis#quit instead.\n     */\n    disconnect(reconnect = false) {\n        if (!reconnect) {\n            this.manuallyClosing = true;\n        }\n        if (this.reconnectTimeout && !reconnect) {\n            clearTimeout(this.reconnectTimeout);\n            this.reconnectTimeout = null;\n        }\n        if (this.status === \"wait\") {\n            eventHandler.closeHandler(this)();\n        }\n        else {\n            this.connector.disconnect();\n        }\n    }\n    /**\n     * Disconnect from Redis.\n     *\n     * @deprecated\n     */\n    end() {\n        this.disconnect();\n    }\n    /**\n     * Create a new instance with the same options as the current one.\n     *\n     * @example\n     * ```js\n     * var redis = new Redis(6380);\n     * var anotherRedis = redis.duplicate();\n     * ```\n     */\n    duplicate(override) {\n        return new Redis({ ...this.options, ...override });\n    }\n    /**\n     * Mode of the connection.\n     *\n     * One of `\"normal\"`, `\"subscriber\"`, or `\"monitor\"`. When the connection is\n     * not in `\"normal\"` mode, certain commands are not allowed.\n     */\n    get mode() {\n        var _a;\n        return this.options.monitor\n            ? \"monitor\"\n            : ((_a = this.condition) === null || _a === void 0 ? void 0 : _a.subscriber)\n                ? \"subscriber\"\n                : \"normal\";\n    }\n    /**\n     * Listen for all requests received by the server in real time.\n     *\n     * This command will create a new connection to Redis and send a\n     * MONITOR command via the new connection in order to avoid disturbing\n     * the current connection.\n     *\n     * @param callback The callback function. If omit, a promise will be returned.\n     * @example\n     * ```js\n     * var redis = new Redis();\n     * redis.monitor(function (err, monitor) {\n     *   // Entering monitoring mode.\n     *   monitor.on('monitor', function (time, args, source, database) {\n     *     console.log(time + \": \" + util.inspect(args));\n     *   });\n     * });\n     *\n     * // supports promise as well as other commands\n     * redis.monitor().then(function (monitor) {\n     *   monitor.on('monitor', function (time, args, source, database) {\n     *     console.log(time + \": \" + util.inspect(args));\n     *   });\n     * });\n     * ```\n     */\n    monitor(callback) {\n        const monitorInstance = this.duplicate({\n            monitor: true,\n            lazyConnect: false,\n        });\n        return (0, standard_as_callback_1.default)(new Promise(function (resolve, reject) {\n            monitorInstance.once(\"error\", reject);\n            monitorInstance.once(\"monitoring\", function () {\n                resolve(monitorInstance);\n            });\n        }), callback);\n    }\n    /**\n     * Send a command to Redis\n     *\n     * This method is used internally and in most cases you should not\n     * use it directly. If you need to send a command that is not supported\n     * by the library, you can use the `call` method:\n     *\n     * ```js\n     * const redis = new Redis();\n     *\n     * redis.call('set', 'foo', 'bar');\n     * // or\n     * redis.call(['set', 'foo', 'bar']);\n     * ```\n     *\n     * @ignore\n     */\n    sendCommand(command, stream) {\n        var _a, _b;\n        if (this.status === \"wait\") {\n            this.connect().catch(lodash_1.noop);\n        }\n        if (this.status === \"end\") {\n            command.reject(new Error(utils_1.CONNECTION_CLOSED_ERROR_MSG));\n            return command.promise;\n        }\n        if (((_a = this.condition) === null || _a === void 0 ? void 0 : _a.subscriber) &&\n            !Command_1.default.checkFlag(\"VALID_IN_SUBSCRIBER_MODE\", command.name)) {\n            command.reject(new Error(\"Connection in subscriber mode, only subscriber commands may be used\"));\n            return command.promise;\n        }\n        if (typeof this.options.commandTimeout === \"number\") {\n            command.setTimeout(this.options.commandTimeout);\n        }\n        let writable = this.status === \"ready\" ||\n            (!stream &&\n                this.status === \"connect\" &&\n                (0, commands_1.exists)(command.name) &&\n                (0, commands_1.hasFlag)(command.name, \"loading\"));\n        if (!this.stream) {\n            writable = false;\n        }\n        else if (!this.stream.writable) {\n            writable = false;\n            // @ts-expect-error\n        }\n        else if (this.stream._writableState && this.stream._writableState.ended) {\n            // TODO: We should be able to remove this as the PR has already been merged.\n            // https://github.com/iojs/io.js/pull/1217\n            writable = false;\n        }\n        if (!writable) {\n            if (!this.options.enableOfflineQueue) {\n                command.reject(new Error(\"Stream isn't writeable and enableOfflineQueue options is false\"));\n                return command.promise;\n            }\n            if (command.name === \"quit\" && this.offlineQueue.length === 0) {\n                this.disconnect();\n                command.resolve(Buffer.from(\"OK\"));\n                return command.promise;\n            }\n            // @ts-expect-error\n            if (debug.enabled) {\n                debug(\"queue command[%s]: %d -> %s(%o)\", this._getDescription(), this.condition.select, command.name, command.args);\n            }\n            this.offlineQueue.push({\n                command: command,\n                stream: stream,\n                select: this.condition.select,\n            });\n        }\n        else {\n            // @ts-expect-error\n            if (debug.enabled) {\n                debug(\"write command[%s]: %d -> %s(%o)\", this._getDescription(), (_b = this.condition) === null || _b === void 0 ? void 0 : _b.select, command.name, command.args);\n            }\n            if (stream) {\n                if (\"isPipeline\" in stream && stream.isPipeline) {\n                    stream.write(command.toWritable(stream.destination.redis.stream));\n                }\n                else {\n                    stream.write(command.toWritable(stream));\n                }\n            }\n            else {\n                this.stream.write(command.toWritable(this.stream));\n            }\n            this.commandQueue.push({\n                command: command,\n                stream: stream,\n                select: this.condition.select,\n            });\n            if (Command_1.default.checkFlag(\"WILL_DISCONNECT\", command.name)) {\n                this.manuallyClosing = true;\n            }\n            if (this.options.socketTimeout !== undefined && this.socketTimeoutTimer === undefined) {\n                this.setSocketTimeout();\n            }\n        }\n        if (command.name === \"select\" && (0, utils_1.isInt)(command.args[0])) {\n            const db = parseInt(command.args[0], 10);\n            if (this.condition.select !== db) {\n                this.condition.select = db;\n                this.emit(\"select\", db);\n                debug(\"switch to db [%d]\", this.condition.select);\n            }\n        }\n        return command.promise;\n    }\n    setSocketTimeout() {\n        this.socketTimeoutTimer = setTimeout(() => {\n            this.stream.destroy(new Error(`Socket timeout. Expecting data, but didn't receive any in ${this.options.socketTimeout}ms.`));\n            this.socketTimeoutTimer = undefined;\n        }, this.options.socketTimeout);\n        // this handler must run after the \"data\" handler in \"DataHandler\"\n        // so that `this.commandQueue.length` will be updated\n        this.stream.once(\"data\", () => {\n            clearTimeout(this.socketTimeoutTimer);\n            this.socketTimeoutTimer = undefined;\n            if (this.commandQueue.length === 0)\n                return;\n            this.setSocketTimeout();\n        });\n    }\n    scanStream(options) {\n        return this.createScanStream(\"scan\", { options });\n    }\n    scanBufferStream(options) {\n        return this.createScanStream(\"scanBuffer\", { options });\n    }\n    sscanStream(key, options) {\n        return this.createScanStream(\"sscan\", { key, options });\n    }\n    sscanBufferStream(key, options) {\n        return this.createScanStream(\"sscanBuffer\", { key, options });\n    }\n    hscanStream(key, options) {\n        return this.createScanStream(\"hscan\", { key, options });\n    }\n    hscanBufferStream(key, options) {\n        return this.createScanStream(\"hscanBuffer\", { key, options });\n    }\n    zscanStream(key, options) {\n        return this.createScanStream(\"zscan\", { key, options });\n    }\n    zscanBufferStream(key, options) {\n        return this.createScanStream(\"zscanBuffer\", { key, options });\n    }\n    /**\n     * Emit only when there's at least one listener.\n     *\n     * @ignore\n     */\n    silentEmit(eventName, arg) {\n        let error;\n        if (eventName === \"error\") {\n            error = arg;\n            if (this.status === \"end\") {\n                return;\n            }\n            if (this.manuallyClosing) {\n                // ignore connection related errors when manually disconnecting\n                if (error instanceof Error &&\n                    (error.message === utils_1.CONNECTION_CLOSED_ERROR_MSG ||\n                        // @ts-expect-error\n                        error.syscall === \"connect\" ||\n                        // @ts-expect-error\n                        error.syscall === \"read\")) {\n                    return;\n                }\n            }\n        }\n        if (this.listeners(eventName).length > 0) {\n            return this.emit.apply(this, arguments);\n        }\n        if (error && error instanceof Error) {\n            console.error(\"[ioredis] Unhandled error event:\", error.stack);\n        }\n        return false;\n    }\n    /**\n     * @ignore\n     */\n    recoverFromFatalError(_commandError, err, options) {\n        this.flushQueue(err, options);\n        this.silentEmit(\"error\", err);\n        this.disconnect(true);\n    }\n    /**\n     * @ignore\n     */\n    handleReconnection(err, item) {\n        var _a;\n        let needReconnect = false;\n        if (this.options.reconnectOnError) {\n            needReconnect = this.options.reconnectOnError(err);\n        }\n        switch (needReconnect) {\n            case 1:\n            case true:\n                if (this.status !== \"reconnecting\") {\n                    this.disconnect(true);\n                }\n                item.command.reject(err);\n                break;\n            case 2:\n                if (this.status !== \"reconnecting\") {\n                    this.disconnect(true);\n                }\n                if (((_a = this.condition) === null || _a === void 0 ? void 0 : _a.select) !== item.select &&\n                    item.command.name !== \"select\") {\n                    this.select(item.select);\n                }\n                // TODO\n                // @ts-expect-error\n                this.sendCommand(item.command);\n                break;\n            default:\n                item.command.reject(err);\n        }\n    }\n    /**\n     * Get description of the connection. Used for debugging.\n     */\n    _getDescription() {\n        let description;\n        if (\"path\" in this.options && this.options.path) {\n            description = this.options.path;\n        }\n        else if (this.stream &&\n            this.stream.remoteAddress &&\n            this.stream.remotePort) {\n            description = this.stream.remoteAddress + \":\" + this.stream.remotePort;\n        }\n        else if (\"host\" in this.options && this.options.host) {\n            description = this.options.host + \":\" + this.options.port;\n        }\n        else {\n            // Unexpected\n            description = \"\";\n        }\n        if (this.options.connectionName) {\n            description += ` (${this.options.connectionName})`;\n        }\n        return description;\n    }\n    resetCommandQueue() {\n        this.commandQueue = new Deque();\n    }\n    resetOfflineQueue() {\n        this.offlineQueue = new Deque();\n    }\n    parseOptions(...args) {\n        const options = {};\n        let isTls = false;\n        for (let i = 0; i < args.length; ++i) {\n            const arg = args[i];\n            if (arg === null || typeof arg === \"undefined\") {\n                continue;\n            }\n            if (typeof arg === \"object\") {\n                (0, lodash_1.defaults)(options, arg);\n            }\n            else if (typeof arg === \"string\") {\n                (0, lodash_1.defaults)(options, (0, utils_1.parseURL)(arg));\n                if (arg.startsWith(\"rediss://\")) {\n                    isTls = true;\n                }\n            }\n            else if (typeof arg === \"number\") {\n                options.port = arg;\n            }\n            else {\n                throw new Error(\"Invalid argument \" + arg);\n            }\n        }\n        if (isTls) {\n            (0, lodash_1.defaults)(options, { tls: true });\n        }\n        (0, lodash_1.defaults)(options, Redis.defaultOptions);\n        if (typeof options.port === \"string\") {\n            options.port = parseInt(options.port, 10);\n        }\n        if (typeof options.db === \"string\") {\n            options.db = parseInt(options.db, 10);\n        }\n        // @ts-expect-error\n        this.options = (0, utils_1.resolveTLSProfile)(options);\n    }\n    /**\n     * Change instance's status\n     */\n    setStatus(status, arg) {\n        // @ts-expect-error\n        if (debug.enabled) {\n            debug(\"status[%s]: %s -> %s\", this._getDescription(), this.status || \"[empty]\", status);\n        }\n        this.status = status;\n        process.nextTick(this.emit.bind(this, status, arg));\n    }\n    createScanStream(command, { key, options = {} }) {\n        return new ScanStream_1.default({\n            objectMode: true,\n            key: key,\n            redis: this,\n            command: command,\n            ...options,\n        });\n    }\n    /**\n     * Flush offline queue and command queue with error.\n     *\n     * @param error The error object to send to the commands\n     * @param options options\n     */\n    flushQueue(error, options) {\n        options = (0, lodash_1.defaults)({}, options, {\n            offlineQueue: true,\n            commandQueue: true,\n        });\n        let item;\n        if (options.offlineQueue) {\n            while ((item = this.offlineQueue.shift())) {\n                item.command.reject(error);\n            }\n        }\n        if (options.commandQueue) {\n            if (this.commandQueue.length > 0) {\n                if (this.stream) {\n                    this.stream.removeAllListeners(\"data\");\n                }\n                while ((item = this.commandQueue.shift())) {\n                    item.command.reject(error);\n                }\n            }\n        }\n    }\n    /**\n     * Check whether Redis has finished loading the persistent data and is able to\n     * process commands.\n     */\n    _readyCheck(callback) {\n        const _this = this;\n        this.info(function (err, res) {\n            if (err) {\n                if (err.message && err.message.includes(\"NOPERM\")) {\n                    console.warn(`Skipping the ready check because INFO command fails: \"${err.message}\". You can disable ready check with \"enableReadyCheck\". More: https://github.com/luin/ioredis/wiki/Disable-ready-check.`);\n                    return callback(null, {});\n                }\n                return callback(err);\n            }\n            if (typeof res !== \"string\") {\n                return callback(null, res);\n            }\n            const info = {};\n            const lines = res.split(\"\\r\\n\");\n            for (let i = 0; i < lines.length; ++i) {\n                const [fieldName, ...fieldValueParts] = lines[i].split(\":\");\n                const fieldValue = fieldValueParts.join(\":\");\n                if (fieldValue) {\n                    info[fieldName] = fieldValue;\n                }\n            }\n            if (!info.loading || info.loading === \"0\") {\n                callback(null, info);\n            }\n            else {\n                const loadingEtaMs = (info.loading_eta_seconds || 1) * 1000;\n                const retryTime = _this.options.maxLoadingRetryTime &&\n                    _this.options.maxLoadingRetryTime < loadingEtaMs\n                    ? _this.options.maxLoadingRetryTime\n                    : loadingEtaMs;\n                debug(\"Redis server still loading, trying again in \" + retryTime + \"ms\");\n                setTimeout(function () {\n                    _this._readyCheck(callback);\n                }, retryTime);\n            }\n        }).catch(lodash_1.noop);\n    }\n}\nRedis.Cluster = cluster_1.default;\nRedis.Command = Command_1.default;\n/**\n * Default options\n */\nRedis.defaultOptions = RedisOptions_1.DEFAULT_REDIS_OPTIONS;\n(0, applyMixin_1.default)(Redis, events_1.EventEmitter);\n(0, transaction_1.addTransactionSupport)(Redis.prototype);\nexports.default = Redis;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,QAAQ,CAAC,GAAG,QAAQ,KAAK,EAAE;AACjC;;;;;;;;;;;;;;;;CAgBC,GACD,MAAM,cAAc,YAAY,OAAO;IACnC,YAAY,IAAI,EAAE,IAAI,EAAE,IAAI,CAAE;QAC1B,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;QACd;;SAEC,GACD,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,eAAe,GAAG;QACvB,mCAAmC;QACnC,IAAI,CAAC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAAC,qBAAqB,GAAG,IAAI;QACjC,IAAI,CAAC,YAAY,CAAC,MAAM,MAAM;QAC9B,SAAS,YAAY,CAAC,IAAI,CAAC,IAAI;QAC/B,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,iBAAiB;QACtB,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACxB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO;QAC5D,OACK,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YAC7B,MAAM,oBAAoB,IAAI,oBAAoB,OAAO,CAAC,IAAI,CAAC,OAAO;YACtE,kBAAkB,OAAO,GAAG,IAAI;YAChC,IAAI,CAAC,SAAS,GAAG;QACrB,OACK;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,aAAa,mBAAmB,CAAC,IAAI,CAAC,OAAO;QACtE;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACtB,OAAO,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,WAAW;gBAC5D,IAAI,CAAC,aAAa,CAAC,MAAM;YAC7B;QACJ;QACA,wDAAwD;QACxD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC1B,IAAI,CAAC,SAAS,CAAC;QACnB,OACK;YACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,IAAI;QACtC;IACJ;IACA;;;KAGC,GACD,OAAO,aAAa,GAAG,IAAI,EAAE;QACzB,OAAO,IAAI,SAAS;IACxB;IACA,IAAI,wBAAwB;QACxB,IAAI,SAAS;QACb,KAAK,MAAM,YAAY,IAAI,CAAC,cAAc,CAAC,MAAM,GAAI;YACjD,UAAU,SAAS,MAAM;QAC7B;QACA,OAAO;IACX;IACA;;;;;;;KAOC,GACD,QAAQ,QAAQ,EAAE;QACd,MAAM,UAAU,IAAI,QAAQ,CAAC,SAAS;YAClC,IAAI,IAAI,CAAC,MAAM,KAAK,gBAChB,IAAI,CAAC,MAAM,KAAK,aAChB,IAAI,CAAC,MAAM,KAAK,SAAS;gBACzB,OAAO,IAAI,MAAM;gBACjB;YACJ;YACA,IAAI,CAAC,eAAe,IAAI;YACxB,IAAI,CAAC,SAAS,CAAC;YACf,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI;YACxB,IAAI,CAAC,SAAS,GAAG;gBACb,QAAQ,QAAQ,EAAE;gBAClB,MAAM,QAAQ,QAAQ,GAChB;oBAAC,QAAQ,QAAQ;oBAAE,QAAQ,QAAQ;iBAAC,GACpC,QAAQ,QAAQ;gBACtB,YAAY;YAChB;YACA,MAAM,QAAQ,IAAI;YAClB,CAAC,GAAG,uBAAuB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAU,IAAI,EAAE,GAAG;gBAC1E,MAAM,UAAU,CAAC,MAAM;YAC3B,IAAI,SAAU,GAAG,EAAE,MAAM;gBACrB,IAAI,KAAK;oBACL,MAAM,UAAU,CAAC;oBACjB,MAAM,UAAU,CAAC,SAAS;oBAC1B,OAAO;oBACP,MAAM,SAAS,CAAC;oBAChB;gBACJ;gBACA,IAAI,gBAAgB,QAAQ,GAAG,GAAG,kBAAkB;gBACpD,IAAI,eAAe,WACf,QAAQ,SAAS,IACjB,CAAC,QAAQ,wBAAwB,EAAE;oBACnC,gBAAgB;gBACpB;gBACA,MAAM,MAAM,GAAG;gBACf,IAAI,QAAQ,OAAO,EAAE;oBACjB,OAAO,UAAU,CAAC;gBACtB;gBACA,6EAA6E;gBAC7E,8CAA8C;gBAC9C,IAAI,OAAO,QAAQ,SAAS,KAAK,UAAU;oBACvC,IAAI,OAAO,UAAU,EAAE;wBACnB,OAAO,IAAI,CAAC,eAAe;4BACvB,OAAO,YAAY,CAAC,MAAM,QAAQ,SAAS;wBAC/C;oBACJ,OACK;wBACD,OAAO,YAAY,CAAC,MAAM,QAAQ,SAAS;oBAC/C;gBACJ;gBACA,IAAI,OAAO,UAAU,EAAE;oBACnB,OAAO,IAAI,CAAC,eAAe,aAAa,cAAc,CAAC;oBACvD,IAAI,QAAQ,cAAc,EAAE;wBACxB;;;;;;yBAMC,GACD,IAAI,wBAAwB;wBAC5B,OAAO,UAAU,CAAC,QAAQ,cAAc,EAAE;4BACtC,IAAI,uBAAuB;gCACvB;4BACJ;4BACA,OAAO,UAAU,CAAC;4BAClB,OAAO,OAAO;4BACd,MAAM,MAAM,IAAI,MAAM;4BACtB,mBAAmB;4BACnB,IAAI,OAAO,GAAG;4BACd,mBAAmB;4BACnB,IAAI,IAAI,GAAG;4BACX,mBAAmB;4BACnB,IAAI,OAAO,GAAG;4BACd,aAAa,YAAY,CAAC,OAAO;wBACrC;wBACA,OAAO,IAAI,CAAC,eAAe;4BACvB,wBAAwB;4BACxB,OAAO,UAAU,CAAC;wBACtB;oBACJ;gBACJ,OACK,IAAI,OAAO,SAAS,EAAE;oBACvB,MAAM,aAAa,MAAM,SAAS,CAAC,UAAU;oBAC7C,IAAI,YAAY;wBACZ,QAAQ,QAAQ,CAAC;4BACb,aAAa,YAAY,CAAC,OAAO;wBACrC;oBACJ;oBACA,QAAQ,QAAQ,CAAC,aAAa,YAAY,CAAC;gBAC/C,OACK;oBACD,QAAQ,QAAQ,CAAC,aAAa,cAAc,CAAC;gBACjD;gBACA,IAAI,CAAC,OAAO,SAAS,EAAE;oBACnB,OAAO,IAAI,CAAC,SAAS,aAAa,YAAY,CAAC;oBAC/C,OAAO,IAAI,CAAC,SAAS,aAAa,YAAY,CAAC;gBACnD;gBACA,MAAM,yBAAyB;oBAC3B,MAAM,cAAc,CAAC,SAAS;oBAC9B;gBACJ;gBACA,IAAI,yBAAyB;oBACzB,MAAM,cAAc,CAAC,SAAS;oBAC9B,OAAO,IAAI,MAAM,QAAQ,2BAA2B;gBACxD;gBACA,MAAM,IAAI,CAAC,SAAS;gBACpB,MAAM,IAAI,CAAC,SAAS;YACxB;QACJ;QACA,OAAO,CAAC,GAAG,uBAAuB,OAAO,EAAE,SAAS;IACxD;IACA;;;;;;KAMC,GACD,WAAW,YAAY,KAAK,EAAE;QAC1B,IAAI,CAAC,WAAW;YACZ,IAAI,CAAC,eAAe,GAAG;QAC3B;QACA,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,WAAW;YACrC,aAAa,IAAI,CAAC,gBAAgB;YAClC,IAAI,CAAC,gBAAgB,GAAG;QAC5B;QACA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YACxB,aAAa,YAAY,CAAC,IAAI;QAClC,OACK;YACD,IAAI,CAAC,SAAS,CAAC,UAAU;QAC7B;IACJ;IACA;;;;KAIC,GACD,MAAM;QACF,IAAI,CAAC,UAAU;IACnB;IACA;;;;;;;;KAQC,GACD,UAAU,QAAQ,EAAE;QAChB,OAAO,IAAI,MAAM;YAAE,GAAG,IAAI,CAAC,OAAO;YAAE,GAAG,QAAQ;QAAC;IACpD;IACA;;;;;KAKC,GACD,IAAI,OAAO;QACP,IAAI;QACJ,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,GACrB,YACA,CAAC,CAAC,KAAK,IAAI,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,IACrE,eACA;IACd;IACA;;;;;;;;;;;;;;;;;;;;;;;;;KAyBC,GACD,QAAQ,QAAQ,EAAE;QACd,MAAM,kBAAkB,IAAI,CAAC,SAAS,CAAC;YACnC,SAAS;YACT,aAAa;QACjB;QACA,OAAO,CAAC,GAAG,uBAAuB,OAAO,EAAE,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YAC5E,gBAAgB,IAAI,CAAC,SAAS;YAC9B,gBAAgB,IAAI,CAAC,cAAc;gBAC/B,QAAQ;YACZ;QACJ,IAAI;IACR;IACA;;;;;;;;;;;;;;;;KAgBC,GACD,YAAY,OAAO,EAAE,MAAM,EAAE;QACzB,IAAI,IAAI;QACR,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;YACxB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,IAAI;QACtC;QACA,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO;YACvB,QAAQ,MAAM,CAAC,IAAI,MAAM,QAAQ,2BAA2B;YAC5D,OAAO,QAAQ,OAAO;QAC1B;QACA,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,KACzE,CAAC,UAAU,OAAO,CAAC,SAAS,CAAC,4BAA4B,QAAQ,IAAI,GAAG;YACxE,QAAQ,MAAM,CAAC,IAAI,MAAM;YACzB,OAAO,QAAQ,OAAO;QAC1B;QACA,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,UAAU;YACjD,QAAQ,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc;QAClD;QACA,IAAI,WAAW,IAAI,CAAC,MAAM,KAAK,WAC1B,CAAC,UACE,IAAI,CAAC,MAAM,KAAK,aAChB,CAAC,GAAG,WAAW,MAAM,EAAE,QAAQ,IAAI,KACnC,CAAC,GAAG,WAAW,OAAO,EAAE,QAAQ,IAAI,EAAE;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACd,WAAW;QACf,OACK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YAC5B,WAAW;QACX,mBAAmB;QACvB,OACK,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE;YACrE,4EAA4E;YAC5E,0CAA0C;YAC1C,WAAW;QACf;QACA,IAAI,CAAC,UAAU;YACX,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;gBAClC,QAAQ,MAAM,CAAC,IAAI,MAAM;gBACzB,OAAO,QAAQ,OAAO;YAC1B;YACA,IAAI,QAAQ,IAAI,KAAK,UAAU,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,GAAG;gBAC3D,IAAI,CAAC,UAAU;gBACf,QAAQ,OAAO,CAAC,OAAO,IAAI,CAAC;gBAC5B,OAAO,QAAQ,OAAO;YAC1B;YACA,mBAAmB;YACnB,IAAI,MAAM,OAAO,EAAE;gBACf,MAAM,mCAAmC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE,QAAQ,IAAI;YACtH;YACA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACnB,SAAS;gBACT,QAAQ;gBACR,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM;YACjC;QACJ,OACK;YACD,mBAAmB;YACnB,IAAI,MAAM,OAAO,EAAE;gBACf,MAAM,mCAAmC,IAAI,CAAC,eAAe,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,EAAE,QAAQ,IAAI,EAAE,QAAQ,IAAI;YACrK;YACA,IAAI,QAAQ;gBACR,IAAI,gBAAgB,UAAU,OAAO,UAAU,EAAE;oBAC7C,OAAO,KAAK,CAAC,QAAQ,UAAU,CAAC,OAAO,WAAW,CAAC,KAAK,CAAC,MAAM;gBACnE,OACK;oBACD,OAAO,KAAK,CAAC,QAAQ,UAAU,CAAC;gBACpC;YACJ,OACK;gBACD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,UAAU,CAAC,IAAI,CAAC,MAAM;YACpD;YACA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACnB,SAAS;gBACT,QAAQ;gBACR,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM;YACjC;YACA,IAAI,UAAU,OAAO,CAAC,SAAS,CAAC,mBAAmB,QAAQ,IAAI,GAAG;gBAC9D,IAAI,CAAC,eAAe,GAAG;YAC3B;YACA,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,aAAa,IAAI,CAAC,kBAAkB,KAAK,WAAW;gBACnF,IAAI,CAAC,gBAAgB;YACzB;QACJ;QACA,IAAI,QAAQ,IAAI,KAAK,YAAY,CAAC,GAAG,QAAQ,KAAK,EAAE,QAAQ,IAAI,CAAC,EAAE,GAAG;YAClE,MAAM,KAAK,SAAS,QAAQ,IAAI,CAAC,EAAE,EAAE;YACrC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,IAAI;gBAC9B,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG;gBACxB,IAAI,CAAC,IAAI,CAAC,UAAU;gBACpB,MAAM,qBAAqB,IAAI,CAAC,SAAS,CAAC,MAAM;YACpD;QACJ;QACA,OAAO,QAAQ,OAAO;IAC1B;IACA,mBAAmB;QACf,IAAI,CAAC,kBAAkB,GAAG,WAAW;YACjC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,0DAA0D,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC;YAC1H,IAAI,CAAC,kBAAkB,GAAG;QAC9B,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa;QAC7B,kEAAkE;QAClE,qDAAqD;QACrD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;YACrB,aAAa,IAAI,CAAC,kBAAkB;YACpC,IAAI,CAAC,kBAAkB,GAAG;YAC1B,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,GAC7B;YACJ,IAAI,CAAC,gBAAgB;QACzB;IACJ;IACA,WAAW,OAAO,EAAE;QAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ;YAAE;QAAQ;IACnD;IACA,iBAAiB,OAAO,EAAE;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc;YAAE;QAAQ;IACzD;IACA,YAAY,GAAG,EAAE,OAAO,EAAE;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS;YAAE;YAAK;QAAQ;IACzD;IACA,kBAAkB,GAAG,EAAE,OAAO,EAAE;QAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe;YAAE;YAAK;QAAQ;IAC/D;IACA,YAAY,GAAG,EAAE,OAAO,EAAE;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS;YAAE;YAAK;QAAQ;IACzD;IACA,kBAAkB,GAAG,EAAE,OAAO,EAAE;QAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe;YAAE;YAAK;QAAQ;IAC/D;IACA,YAAY,GAAG,EAAE,OAAO,EAAE;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS;YAAE;YAAK;QAAQ;IACzD;IACA,kBAAkB,GAAG,EAAE,OAAO,EAAE;QAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe;YAAE;YAAK;QAAQ;IAC/D;IACA;;;;KAIC,GACD,WAAW,SAAS,EAAE,GAAG,EAAE;QACvB,IAAI;QACJ,IAAI,cAAc,SAAS;YACvB,QAAQ;YACR,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO;gBACvB;YACJ;YACA,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,+DAA+D;gBAC/D,IAAI,iBAAiB,SACjB,CAAC,MAAM,OAAO,KAAK,QAAQ,2BAA2B,IAClD,mBAAmB;gBACnB,MAAM,OAAO,KAAK,aAClB,mBAAmB;gBACnB,MAAM,OAAO,KAAK,MAAM,GAAG;oBAC/B;gBACJ;YACJ;QACJ;QACA,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,MAAM,GAAG,GAAG;YACtC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACjC;QACA,IAAI,SAAS,iBAAiB,OAAO;YACjC,QAAQ,KAAK,CAAC,oCAAoC,MAAM,KAAK;QACjE;QACA,OAAO;IACX;IACA;;KAEC,GACD,sBAAsB,aAAa,EAAE,GAAG,EAAE,OAAO,EAAE;QAC/C,IAAI,CAAC,UAAU,CAAC,KAAK;QACrB,IAAI,CAAC,UAAU,CAAC,SAAS;QACzB,IAAI,CAAC,UAAU,CAAC;IACpB;IACA;;KAEC,GACD,mBAAmB,GAAG,EAAE,IAAI,EAAE;QAC1B,IAAI;QACJ,IAAI,gBAAgB;QACpB,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YAC/B,gBAAgB,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;QAClD;QACA,OAAQ;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,IAAI,CAAC,MAAM,KAAK,gBAAgB;oBAChC,IAAI,CAAC,UAAU,CAAC;gBACpB;gBACA,KAAK,OAAO,CAAC,MAAM,CAAC;gBACpB;YACJ,KAAK;gBACD,IAAI,IAAI,CAAC,MAAM,KAAK,gBAAgB;oBAChC,IAAI,CAAC,UAAU,CAAC;gBACpB;gBACA,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM,KAAK,MAAM,IACtF,KAAK,OAAO,CAAC,IAAI,KAAK,UAAU;oBAChC,IAAI,CAAC,MAAM,CAAC,KAAK,MAAM;gBAC3B;gBACA,OAAO;gBACP,mBAAmB;gBACnB,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;gBAC7B;YACJ;gBACI,KAAK,OAAO,CAAC,MAAM,CAAC;QAC5B;IACJ;IACA;;KAEC,GACD,kBAAkB;QACd,IAAI;QACJ,IAAI,UAAU,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YAC7C,cAAc,IAAI,CAAC,OAAO,CAAC,IAAI;QACnC,OACK,IAAI,IAAI,CAAC,MAAM,IAChB,IAAI,CAAC,MAAM,CAAC,aAAa,IACzB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACxB,cAAc,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU;QAC1E,OACK,IAAI,UAAU,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YAClD,cAAc,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI;QAC7D,OACK;YACD,aAAa;YACb,cAAc;QAClB;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC7B,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;QACtD;QACA,OAAO;IACX;IACA,oBAAoB;QAChB,IAAI,CAAC,YAAY,GAAG,IAAI;IAC5B;IACA,oBAAoB;QAChB,IAAI,CAAC,YAAY,GAAG,IAAI;IAC5B;IACA,aAAa,GAAG,IAAI,EAAE;QAClB,MAAM,UAAU,CAAC;QACjB,IAAI,QAAQ;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;YAClC,MAAM,MAAM,IAAI,CAAC,EAAE;YACnB,IAAI,QAAQ,QAAQ,OAAO,QAAQ,aAAa;gBAC5C;YACJ;YACA,IAAI,OAAO,QAAQ,UAAU;gBACzB,CAAC,GAAG,SAAS,QAAQ,EAAE,SAAS;YACpC,OACK,IAAI,OAAO,QAAQ,UAAU;gBAC9B,CAAC,GAAG,SAAS,QAAQ,EAAE,SAAS,CAAC,GAAG,QAAQ,QAAQ,EAAE;gBACtD,IAAI,IAAI,UAAU,CAAC,cAAc;oBAC7B,QAAQ;gBACZ;YACJ,OACK,IAAI,OAAO,QAAQ,UAAU;gBAC9B,QAAQ,IAAI,GAAG;YACnB,OACK;gBACD,MAAM,IAAI,MAAM,sBAAsB;YAC1C;QACJ;QACA,IAAI,OAAO;YACP,CAAC,GAAG,SAAS,QAAQ,EAAE,SAAS;gBAAE,KAAK;YAAK;QAChD;QACA,CAAC,GAAG,SAAS,QAAQ,EAAE,SAAS,MAAM,cAAc;QACpD,IAAI,OAAO,QAAQ,IAAI,KAAK,UAAU;YAClC,QAAQ,IAAI,GAAG,SAAS,QAAQ,IAAI,EAAE;QAC1C;QACA,IAAI,OAAO,QAAQ,EAAE,KAAK,UAAU;YAChC,QAAQ,EAAE,GAAG,SAAS,QAAQ,EAAE,EAAE;QACtC;QACA,mBAAmB;QACnB,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,QAAQ,iBAAiB,EAAE;IAClD;IACA;;KAEC,GACD,UAAU,MAAM,EAAE,GAAG,EAAE;QACnB,mBAAmB;QACnB,IAAI,MAAM,OAAO,EAAE;YACf,MAAM,wBAAwB,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,IAAI,WAAW;QACpF;QACA,IAAI,CAAC,MAAM,GAAG;QACd,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ;IAClD;IACA,iBAAiB,OAAO,EAAE,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE,EAAE;QAC7C,OAAO,IAAI,aAAa,OAAO,CAAC;YAC5B,YAAY;YACZ,KAAK;YACL,OAAO,IAAI;YACX,SAAS;YACT,GAAG,OAAO;QACd;IACJ;IACA;;;;;KAKC,GACD,WAAW,KAAK,EAAE,OAAO,EAAE;QACvB,UAAU,CAAC,GAAG,SAAS,QAAQ,EAAE,CAAC,GAAG,SAAS;YAC1C,cAAc;YACd,cAAc;QAClB;QACA,IAAI;QACJ,IAAI,QAAQ,YAAY,EAAE;YACtB,MAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,GAAK;gBACvC,KAAK,OAAO,CAAC,MAAM,CAAC;YACxB;QACJ;QACA,IAAI,QAAQ,YAAY,EAAE;YACtB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;gBAC9B,IAAI,IAAI,CAAC,MAAM,EAAE;oBACb,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;gBACnC;gBACA,MAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,GAAK;oBACvC,KAAK,OAAO,CAAC,MAAM,CAAC;gBACxB;YACJ;QACJ;IACJ;IACA;;;KAGC,GACD,YAAY,QAAQ,EAAE;QAClB,MAAM,QAAQ,IAAI;QAClB,IAAI,CAAC,IAAI,CAAC,SAAU,GAAG,EAAE,GAAG;YACxB,IAAI,KAAK;gBACL,IAAI,IAAI,OAAO,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW;oBAC/C,QAAQ,IAAI,CAAC,CAAC,sDAAsD,EAAE,IAAI,OAAO,CAAC,uHAAuH,CAAC;oBAC1M,OAAO,SAAS,MAAM,CAAC;gBAC3B;gBACA,OAAO,SAAS;YACpB;YACA,IAAI,OAAO,QAAQ,UAAU;gBACzB,OAAO,SAAS,MAAM;YAC1B;YACA,MAAM,OAAO,CAAC;YACd,MAAM,QAAQ,IAAI,KAAK,CAAC;YACxB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;gBACnC,MAAM,CAAC,WAAW,GAAG,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;gBACvD,MAAM,aAAa,gBAAgB,IAAI,CAAC;gBACxC,IAAI,YAAY;oBACZ,IAAI,CAAC,UAAU,GAAG;gBACtB;YACJ;YACA,IAAI,CAAC,KAAK,OAAO,IAAI,KAAK,OAAO,KAAK,KAAK;gBACvC,SAAS,MAAM;YACnB,OACK;gBACD,MAAM,eAAe,CAAC,KAAK,mBAAmB,IAAI,CAAC,IAAI;gBACvD,MAAM,YAAY,MAAM,OAAO,CAAC,mBAAmB,IAC/C,MAAM,OAAO,CAAC,mBAAmB,GAAG,eAClC,MAAM,OAAO,CAAC,mBAAmB,GACjC;gBACN,MAAM,iDAAiD,YAAY;gBACnE,WAAW;oBACP,MAAM,WAAW,CAAC;gBACtB,GAAG;YACP;QACJ,GAAG,KAAK,CAAC,SAAS,IAAI;IAC1B;AACJ;AACA,MAAM,OAAO,GAAG,UAAU,OAAO;AACjC,MAAM,OAAO,GAAG,UAAU,OAAO;AACjC;;CAEC,GACD,MAAM,cAAc,GAAG,eAAe,qBAAqB;AAC3D,CAAC,GAAG,aAAa,OAAO,EAAE,OAAO,SAAS,YAAY;AACtD,CAAC,GAAG,cAAc,qBAAqB,EAAE,MAAM,SAAS;AACxD,QAAQ,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5218, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/ioredis%405.6.1/node_modules/ioredis/built/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.print = exports.ReplyError = exports.SentinelIterator = exports.SentinelConnector = exports.AbstractConnector = exports.Pipeline = exports.ScanStream = exports.Command = exports.Cluster = exports.Redis = exports.default = void 0;\nexports = module.exports = require(\"./Redis\").default;\nvar Redis_1 = require(\"./Redis\");\nObject.defineProperty(exports, \"default\", { enumerable: true, get: function () { return Redis_1.default; } });\nvar Redis_2 = require(\"./Redis\");\nObject.defineProperty(exports, \"Redis\", { enumerable: true, get: function () { return Redis_2.default; } });\nvar cluster_1 = require(\"./cluster\");\nObject.defineProperty(exports, \"Cluster\", { enumerable: true, get: function () { return cluster_1.default; } });\n/**\n * @ignore\n */\nvar Command_1 = require(\"./Command\");\nObject.defineProperty(exports, \"Command\", { enumerable: true, get: function () { return Command_1.default; } });\n/**\n * @ignore\n */\nvar ScanStream_1 = require(\"./ScanStream\");\nObject.defineProperty(exports, \"ScanStream\", { enumerable: true, get: function () { return ScanStream_1.default; } });\n/**\n * @ignore\n */\nvar Pipeline_1 = require(\"./Pipeline\");\nObject.defineProperty(exports, \"Pipeline\", { enumerable: true, get: function () { return Pipeline_1.default; } });\n/**\n * @ignore\n */\nvar AbstractConnector_1 = require(\"./connectors/AbstractConnector\");\nObject.defineProperty(exports, \"AbstractConnector\", { enumerable: true, get: function () { return AbstractConnector_1.default; } });\n/**\n * @ignore\n */\nvar SentinelConnector_1 = require(\"./connectors/SentinelConnector\");\nObject.defineProperty(exports, \"SentinelConnector\", { enumerable: true, get: function () { return SentinelConnector_1.default; } });\nObject.defineProperty(exports, \"SentinelIterator\", { enumerable: true, get: function () { return SentinelConnector_1.SentinelIterator; } });\n// No TS typings\nexports.ReplyError = require(\"redis-errors\").ReplyError;\n/**\n * @ignore\n */\nObject.defineProperty(exports, \"Promise\", {\n    get() {\n        console.warn(\"ioredis v5 does not support plugging third-party Promise library anymore. Native Promise will be used.\");\n        return Promise;\n    },\n    set(_lib) {\n        console.warn(\"ioredis v5 does not support plugging third-party Promise library anymore. Native Promise will be used.\");\n    },\n});\n/**\n * @ignore\n */\nfunction print(err, reply) {\n    if (err) {\n        console.log(\"Error: \" + err);\n    }\n    else {\n        console.log(\"Reply: \" + reply);\n    }\n}\nexports.print = print;\n"], "names": [], "mappings": "AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,KAAK,GAAG,QAAQ,UAAU,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,QAAQ,GAAG,QAAQ,UAAU,GAAG,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAG,QAAQ,KAAK,GAAG,QAAQ,OAAO,GAAG,KAAK;AAC3O,UAAU,OAAO,OAAO,GAAG,iIAAmB,OAAO;AACrD,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,WAAW;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,QAAQ,OAAO;IAAE;AAAE;AAC3G,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,SAAS;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,QAAQ,OAAO;IAAE;AAAE;AACzG,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,WAAW;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,UAAU,OAAO;IAAE;AAAE;AAC7G;;CAEC,GACD,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,WAAW;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,UAAU,OAAO;IAAE;AAAE;AAC7G;;CAEC,GACD,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,aAAa,OAAO;IAAE;AAAE;AACnH;;CAEC,GACD,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,YAAY;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,WAAW,OAAO;IAAE;AAAE;AAC/G;;CAEC,GACD,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,qBAAqB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,oBAAoB,OAAO;IAAE;AAAE;AACjI;;CAEC,GACD,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,qBAAqB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,oBAAoB,OAAO;IAAE;AAAE;AACjI,OAAO,cAAc,CAAC,SAAS,oBAAoB;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,oBAAoB,gBAAgB;IAAE;AAAE;AACzI,gBAAgB;AAChB,QAAQ,UAAU,GAAG,qIAAwB,UAAU;AACvD;;CAEC,GACD,OAAO,cAAc,CAAC,SAAS,WAAW;IACtC;QACI,QAAQ,IAAI,CAAC;QACb,OAAO;IACX;IACA,KAAI,IAAI;QACJ,QAAQ,IAAI,CAAC;IACjB;AACJ;AACA;;CAEC,GACD,SAAS,MAAM,GAAG,EAAE,KAAK;IACrB,IAAI,KAAK;QACL,QAAQ,GAAG,CAAC,YAAY;IAC5B,OACK;QACD,QAAQ,GAAG,CAAC,YAAY;IAC5B;AACJ;AACA,QAAQ,KAAK,GAAG", "ignoreList": [0], "debugId": null}}]}