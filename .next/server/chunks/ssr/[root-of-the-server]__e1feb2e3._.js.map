{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/env.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nconst envSchema = z.object({\n  // AWS S3 Configuration\n  AWS_ACCESS_KEY_ID: z.string().min(1, \"AWS_ACCESS_KEY_ID is required\"),\n  AWS_SECRET_ACCESS_KEY: z.string().min(1, \"AWS_SECRET_ACCESS_KEY is required\"),\n  AWS_REGION: z.string().min(1, \"AWS_REGION is required\"),\n  AWS_S3_BUCKET_NAME: z.string().min(1, \"AWS_S3_BUCKET_NAME is required\"),\n\n  // Anthropic Claude Configuration\n  ANTHROPIC_API_KEY: z.string().min(1, \"ANTHROPIC_API_KEY is required\"),\n\n  // Upstash Redis Configuration\n  UPSTASH_REDIS_REST_URL: z\n    .string()\n    .min(1, \"UPSTASH_REDIS_REST_URL is required\"),\n  UPSTASH_REDIS_REST_TOKEN: z\n    .string()\n    .min(1, \"UPSTASH_REDIS_REST_TOKEN is required\"),\n\n  // Next.js Environment\n  NODE_ENV: z\n    .enum([\"development\", \"production\", \"test\"])\n    .default(\"development\"),\n});\n\n// Validate environment variables\nconst parseEnv = () => {\n  // Skip validation during build time if we're not in a runtime environment\n  if (typeof window === \"undefined\" && !process.env.ANTHROPIC_API_KEY) {\n    console.warn(\"Environment variables not available during build time\");\n    return {} as z.infer<typeof envSchema>;\n  }\n\n  try {\n    return envSchema.parse(process.env);\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      const missingVars = error.errors\n        .map((err) => err.path.join(\".\"))\n        .join(\", \");\n      throw new Error(\n        `Missing or invalid environment variables: ${missingVars}`\n      );\n    }\n    throw error;\n  }\n};\n\nexport const env = parseEnv();\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY,oNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzB,uBAAuB;IACvB,mBAAmB,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACrC,uBAAuB,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzC,YAAY,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC9B,oBAAoB,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAEtC,iCAAiC;IACjC,mBAAmB,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAErC,8BAA8B;IAC9B,wBAAwB,oNAAA,CAAA,IAAC,CACtB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,0BAA0B,oNAAA,CAAA,IAAC,CACxB,MAAM,GACN,GAAG,CAAC,GAAG;IAEV,sBAAsB;IACtB,UAAU,oNAAA,CAAA,IAAC,CACR,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAC1C,OAAO,CAAC;AACb;AAEA,iCAAiC;AACjC,MAAM,WAAW;IACf,0EAA0E;IAC1E,IAAI,gBAAkB,eAAe,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;QACnE,QAAQ,IAAI,CAAC;QACb,OAAO,CAAC;IACV;IAEA,IAAI;QACF,OAAO,UAAU,KAAK,CAAC,QAAQ,GAAG;IACpC,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,oNAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,MAAM,cAAc,MAAM,MAAM,CAC7B,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,MAC3B,IAAI,CAAC;YACR,MAAM,IAAI,MACR,CAAC,0CAA0C,EAAE,aAAa;QAE9D;QACA,MAAM;IACR;AACF;AAEO,MAAM,MAAM", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/ai.ts"], "sourcesContent": ["import { createAnthropic } from \"@ai-sdk/anthropic\";\nimport { generateText } from \"ai\";\nimport { env } from \"./env\";\n\n// Initialize Anthropic with API key\nconst anthropic = createAnthropic({\n  apiKey: env.ANTHROPIC_API_KEY,\n});\n\nconst MAX_TOKENS = 4000;\n\n/**\n * Enhanced system prompt for generating complete webpages\n */\nconst SYSTEM_PROMPT = `You are an expert web developer and designer. Your task is to generate a complete, modern, and visually appealing HTML webpage based on the given purpose/description.\n\nIMPORTANT SAFETY REQUIREMENT:\n- If the user request is for NSFW content, illegal activities, harmful content, or triggers your safety system in any way, respond with ONLY the text \"INAPPROPRIATE_PROMPT_DETECTED\" and nothing else.\n- Do not generate any HTML, explanations, or other content for inappropriate requests.\n\nREQUIREMENTS:\n1. Generate a complete HTML5 document with proper DOCTYPE, head, and body sections\n2. Include responsive design using modern CSS (flexbox/grid)\n3. Use semantic HTML elements (header, main, section, article, footer, etc.)\n4. Include proper meta tags for SEO and viewport\n5. Add attractive styling with CSS (embedded in <style> tags in the head)\n6. Use modern color schemes and typography\n7. Make it desktop and mobile-responsive\n8. Include interactive elements where appropriate (buttons, forms, etc.)\n9. Add proper accessibility attributes (alt text, ARIA labels, etc.)\n10. Use modern CSS features like CSS Grid, Flexbox, custom properties (CSS variables)\n11. You must include two following adverts:\n  - Advert to https://www.dothistask.ai (logo is https://www.dothistask.ai/icon.png)\n    - The copy for this advert must be specific to the use-case for this page + the purpose of dothistask.ai (help knowledge workers find AI solutions for their jobs)\n    - The advert (logo + text) must link to https://dothistask.ai\n  - Advert to https://everywebsite.ai (no logo)\n    - The copy for this advert should indiciate that this page was generated by https://everywebsite.ai and they can generate new pages by just changing the url\n    - The advert (text) must link to https://everywebsite.ai\n11. You must include a subtle advert to https://dothistask.ai (logo is https://www.dothistask.ai/icon.png)\n  - The copy for this advert must be specific to the use-case for this page + the purpose of dothistask.ai (help knowledge workers find AI solutions for their jobs)\n  - The advert (logo + text) must link to https://dothistask.ai\n12. You have no backend and cannot implement any features that require a backend (database, APIs, AI, etc.) You need to generate the webpage with this in mind\n13. Do not include a footer, copyright, etc.. Just a functional webpage\n14. You don't have access to images besides the Dothistask logo, you should rely on emojis or svgs that you can define. \n\nSTYLE GUIDELINES:\n- Use a modern, clean design aesthetic\n- Implement a cohesive color palette\n- Use proper typography hierarchy\n- Add subtle animations/transitions for better UX\n- Ensure good contrast ratios for accessibility\n- Use modern CSS techniques (no inline styles except for the main <style> tag)\n\nOUTPUT FORMAT:\n- Return ONLY the complete HTML document\n- No markdown code blocks or explanations\n- Start with <!DOCTYPE html> and end with </html>\n- Ensure the HTML is valid and well-formatted\n- Limit the output to ${MAX_TOKENS} tokens\n\nThe webpage should be production-ready and look professional.`;\n\n/**\n * Generate HTML content using Claude Sonnet 4\n */\nexport async function generateWebpageHtml(slug: string): Promise<string> {\n  try {\n    const { text } = await generateText({\n      model: anthropic(\"claude-3-5-sonnet-20241022\"),\n      system: SYSTEM_PROMPT,\n      prompt: `Generate a complete HTML webpage that solves this purpose: \"${slug}\"\n      \n      Make sure the webpage is:\n      - Fully functional and complete\n      - Visually appealing and modern\n      - Responsive across all devices\n      - Accessible and SEO-friendly\n      - Professional and production-ready\n      \n      The purpose \"${slug}\" should guide the content, design, and functionality of the webpage.`,\n      maxTokens: MAX_TOKENS,\n      temperature: 0.7,\n    });\n\n    return text;\n  } catch (error) {\n    console.error(\"Error generating webpage with Claude:\", error);\n    throw new Error(\"Failed to generate webpage content\");\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,oCAAoC;AACpC,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,QAAQ,iHAAA,CAAA,MAAG,CAAC,iBAAiB;AAC/B;AAEA,MAAM,aAAa;AAEnB;;CAEC,GACD,MAAM,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBA4CD,EAAE,WAAW;;6DAE0B,CAAC;AAKvD,eAAe,oBAAoB,IAAY;IACpD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,+OAAA,CAAA,eAAY,AAAD,EAAE;YAClC,OAAO,UAAU;YACjB,QAAQ;YACR,QAAQ,CAAC,4DAA4D,EAAE,KAAK;;;;;;;;;mBAS/D,EAAE,KAAK,qEAAqE,CAAC;YAC1F,WAAW;YACX,aAAa;QACf;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/s3.ts"], "sourcesContent": ["import {\n  S3Client,\n  PutObjectCommand,\n  GetObjectCommand,\n} from \"@aws-sdk/client-s3\";\nimport { env } from \"./env\";\n\n// Initialize S3 client\nconst s3Client = new S3Client({\n  region: env.AWS_REGION,\n  credentials: {\n    accessKeyId: env.AWS_ACCESS_KEY_ID,\n    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,\n  },\n});\n\n/**\n * Upload HTML content to S3\n */\nexport async function uploadHtmlToS3(\n  slug: string,\n  htmlContent: string\n): Promise<string> {\n  const key = `websites/${slug}.html`;\n\n  // Determine content type based on content\n  const isInappropriate =\n    htmlContent.trim() === \"INAPPROPRIATE_PROMPT_DETECTED\";\n  const contentType = isInappropriate ? \"text/plain\" : \"text/html\";\n\n  const command = new PutObjectCommand({\n    Bucket: env.AWS_S3_BUCKET_NAME,\n    Key: key,\n    Body: htmlContent,\n    ContentType: contentType,\n    CacheControl: \"max-age=3600\", // Cache for 1 hour\n    Metadata: {\n      inappropriate: isInappropriate ? \"true\" : \"false\",\n    },\n  });\n\n  try {\n    await s3Client.send(command);\n    return key;\n  } catch (error) {\n    console.error(\"Error uploading to S3:\", error);\n    throw new Error(\"Failed to upload HTML to S3\");\n  }\n}\n\n/**\n * Fetch HTML content from S3\n */\nexport async function fetchHtmlFromS3(slug: string): Promise<string | null> {\n  const key = `websites/${slug}.html`;\n\n  const command = new GetObjectCommand({\n    Bucket: env.AWS_S3_BUCKET_NAME,\n    Key: key,\n  });\n\n  try {\n    const response = await s3Client.send(command);\n    if (response.Body) {\n      return await response.Body.transformToString();\n    }\n    return null;\n  } catch (error) {\n    // If file doesn't exist, return null instead of throwing\n    if ((error as { name?: string })?.name === \"NoSuchKey\") {\n      return null;\n    }\n    console.error(\"Error fetching from S3:\", error);\n    throw new Error(\"Failed to fetch HTML from S3\");\n  }\n}\n\n/**\n * Check if HTML exists in S3\n */\nexport async function htmlExistsInS3(slug: string): Promise<boolean> {\n  try {\n    const html = await fetchHtmlFromS3(slug);\n    return html !== null;\n  } catch {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAKA;;;AAEA,uBAAuB;AACvB,MAAM,WAAW,IAAI,iJAAA,CAAA,WAAQ,CAAC;IAC5B,QAAQ,iHAAA,CAAA,MAAG,CAAC,UAAU;IACtB,aAAa;QACX,aAAa,iHAAA,CAAA,MAAG,CAAC,iBAAiB;QAClC,iBAAiB,iHAAA,CAAA,MAAG,CAAC,qBAAqB;IAC5C;AACF;AAKO,eAAe,eACpB,IAAY,EACZ,WAAmB;IAEnB,MAAM,MAAM,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC;IAEnC,0CAA0C;IAC1C,MAAM,kBACJ,YAAY,IAAI,OAAO;IACzB,MAAM,cAAc,kBAAkB,eAAe;IAErD,MAAM,UAAU,IAAI,iJAAA,CAAA,mBAAgB,CAAC;QACnC,QAAQ,iHAAA,CAAA,MAAG,CAAC,kBAAkB;QAC9B,KAAK;QACL,MAAM;QACN,aAAa;QACb,cAAc;QACd,UAAU;YACR,eAAe,kBAAkB,SAAS;QAC5C;IACF;IAEA,IAAI;QACF,MAAM,SAAS,IAAI,CAAC;QACpB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,gBAAgB,IAAY;IAChD,MAAM,MAAM,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC;IAEnC,MAAM,UAAU,IAAI,iJAAA,CAAA,mBAAgB,CAAC;QACnC,QAAQ,iHAAA,CAAA,MAAG,CAAC,kBAAkB;QAC9B,KAAK;IACP;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,SAAS,IAAI,CAAC;QACrC,IAAI,SAAS,IAAI,EAAE;YACjB,OAAO,MAAM,SAAS,IAAI,CAAC,iBAAiB;QAC9C;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,yDAAyD;QACzD,IAAI,AAAC,OAA6B,SAAS,aAAa;YACtD,OAAO;QACT;QACA,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,eAAe,IAAY;IAC/C,IAAI;QACF,MAAM,OAAO,MAAM,gBAAgB;QACnC,OAAO,SAAS;IAClB,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/redis.ts"], "sourcesContent": ["import Redis from \"ioredis\";\nimport { env } from \"./env\";\n\n// Create Redis client\nlet redis: Redis | null = null;\n\nexport function getRedisClient(): Redis {\n  if (!redis) {\n    redis = new Redis(env.REDIS_URL, {\n      retryDelayOnFailover: 100,\n      maxRetriesPerRequest: 3,\n      lazyConnect: true,\n    });\n\n    redis.on(\"error\", (error) => {\n      console.error(\"Redis connection error:\", error);\n    });\n\n    redis.on(\"connect\", () => {\n      console.log(\"Connected to Red<PERSON>\");\n    });\n  }\n\n  return redis;\n}\n\n// Rate limiting constants\nconst USER_RATE_LIMIT = 3; // 3 pages per hour per user\nconst GLOBAL_RATE_LIMIT = 100; // 100 pages per day globally\nconst USER_WINDOW = 60 * 60; // 1 hour in seconds\nconst GLOBAL_WINDOW = 24 * 60 * 60; // 24 hours in seconds\n\nexport interface RateLimitResult {\n  allowed: boolean;\n  reason?: \"user_limit\" | \"global_limit\";\n  userCount?: number;\n  globalCount?: number;\n  userResetTime?: number;\n  globalResetTime?: number;\n}\n\n/**\n * Check if a user can generate a new page based on rate limits\n * @param userIp - User's IP address (used as identifier)\n * @returns Promise<RateLimitResult>\n */\nexport async function checkRateLimit(userIp: string): Promise<RateLimitResult> {\n  const client = getRedisClient();\n  \n  try {\n    // Ensure Redis is connected\n    await client.ping();\n\n    const now = Math.floor(Date.now() / 1000);\n    const userKey = `user_rate_limit:${userIp}`;\n    const globalKey = \"global_rate_limit\";\n\n    // Check user rate limit\n    const userCount = await client.incr(userKey);\n    \n    if (userCount === 1) {\n      // First request from this user, set expiration\n      await client.expire(userKey, USER_WINDOW);\n    }\n\n    const userTtl = await client.ttl(userKey);\n    const userResetTime = now + userTtl;\n\n    if (userCount > USER_RATE_LIMIT) {\n      return {\n        allowed: false,\n        reason: \"user_limit\",\n        userCount,\n        userResetTime,\n      };\n    }\n\n    // Check global rate limit\n    const globalCount = await client.incr(globalKey);\n    \n    if (globalCount === 1) {\n      // First request of the day, set expiration\n      await client.expire(globalKey, GLOBAL_WINDOW);\n    }\n\n    const globalTtl = await client.ttl(globalKey);\n    const globalResetTime = now + globalTtl;\n\n    if (globalCount > GLOBAL_RATE_LIMIT) {\n      // Decrement user count since we're not allowing this request\n      await client.decr(userKey);\n      \n      return {\n        allowed: false,\n        reason: \"global_limit\",\n        globalCount,\n        globalResetTime,\n      };\n    }\n\n    return {\n      allowed: true,\n      userCount,\n      globalCount,\n      userResetTime,\n      globalResetTime,\n    };\n\n  } catch (error) {\n    console.error(\"Redis rate limit check failed:\", error);\n    // If Redis is down, allow the request but log the error\n    // In production, you might want to implement a fallback strategy\n    return {\n      allowed: true,\n    };\n  }\n}\n\n/**\n * Get current rate limit status without incrementing counters\n * @param userIp - User's IP address\n * @returns Promise<RateLimitResult>\n */\nexport async function getRateLimitStatus(userIp: string): Promise<RateLimitResult> {\n  const client = getRedisClient();\n  \n  try {\n    await client.ping();\n\n    const now = Math.floor(Date.now() / 1000);\n    const userKey = `user_rate_limit:${userIp}`;\n    const globalKey = \"global_rate_limit\";\n\n    const [userCount, userTtl, globalCount, globalTtl] = await Promise.all([\n      client.get(userKey),\n      client.ttl(userKey),\n      client.get(globalKey),\n      client.ttl(globalKey),\n    ]);\n\n    const userCountNum = parseInt(userCount || \"0\");\n    const globalCountNum = parseInt(globalCount || \"0\");\n\n    const userResetTime = userTtl > 0 ? now + userTtl : 0;\n    const globalResetTime = globalTtl > 0 ? now + globalTtl : 0;\n\n    if (userCountNum >= USER_RATE_LIMIT) {\n      return {\n        allowed: false,\n        reason: \"user_limit\",\n        userCount: userCountNum,\n        userResetTime,\n      };\n    }\n\n    if (globalCountNum >= GLOBAL_RATE_LIMIT) {\n      return {\n        allowed: false,\n        reason: \"global_limit\",\n        globalCount: globalCountNum,\n        globalResetTime,\n      };\n    }\n\n    return {\n      allowed: true,\n      userCount: userCountNum,\n      globalCount: globalCountNum,\n      userResetTime,\n      globalResetTime,\n    };\n\n  } catch (error) {\n    console.error(\"Redis rate limit status check failed:\", error);\n    return {\n      allowed: true,\n    };\n  }\n}\n\n/**\n * Reset rate limits (for testing or admin purposes)\n */\nexport async function resetRateLimits(userIp?: string): Promise<void> {\n  const client = getRedisClient();\n  \n  try {\n    if (userIp) {\n      await client.del(`user_rate_limit:${userIp}`);\n    } else {\n      // Reset global limit\n      await client.del(\"global_rate_limit\");\n    }\n  } catch (error) {\n    console.error(\"Failed to reset rate limits:\", error);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AACA;;;AAEA,sBAAsB;AACtB,IAAI,QAAsB;AAEnB,SAAS;IACd,IAAI,CAAC,OAAO;QACV,QAAQ,IAAI,MAAM,iHAAA,CAAA,MAAG,CAAC,SAAS,EAAE;YAC/B,sBAAsB;YACtB,sBAAsB;YACtB,aAAa;QACf;QAEA,MAAM,EAAE,CAAC,SAAS,CAAC;YACjB,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;QAEA,MAAM,EAAE,CAAC,WAAW;YAClB,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,OAAO;AACT;AAEA,0BAA0B;AAC1B,MAAM,kBAAkB,GAAG,4BAA4B;AACvD,MAAM,oBAAoB,KAAK,6BAA6B;AAC5D,MAAM,cAAc,KAAK,IAAI,oBAAoB;AACjD,MAAM,gBAAgB,KAAK,KAAK,IAAI,sBAAsB;AAgBnD,eAAe,eAAe,MAAc;IACjD,MAAM,SAAS;IAEf,IAAI;QACF,4BAA4B;QAC5B,MAAM,OAAO,IAAI;QAEjB,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QACpC,MAAM,UAAU,CAAC,gBAAgB,EAAE,QAAQ;QAC3C,MAAM,YAAY;QAElB,wBAAwB;QACxB,MAAM,YAAY,MAAM,OAAO,IAAI,CAAC;QAEpC,IAAI,cAAc,GAAG;YACnB,+CAA+C;YAC/C,MAAM,OAAO,MAAM,CAAC,SAAS;QAC/B;QAEA,MAAM,UAAU,MAAM,OAAO,GAAG,CAAC;QACjC,MAAM,gBAAgB,MAAM;QAE5B,IAAI,YAAY,iBAAiB;YAC/B,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR;gBACA;YACF;QACF;QAEA,0BAA0B;QAC1B,MAAM,cAAc,MAAM,OAAO,IAAI,CAAC;QAEtC,IAAI,gBAAgB,GAAG;YACrB,2CAA2C;YAC3C,MAAM,OAAO,MAAM,CAAC,WAAW;QACjC;QAEA,MAAM,YAAY,MAAM,OAAO,GAAG,CAAC;QACnC,MAAM,kBAAkB,MAAM;QAE9B,IAAI,cAAc,mBAAmB;YACnC,6DAA6D;YAC7D,MAAM,OAAO,IAAI,CAAC;YAElB,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR;gBACA;YACF;QACF;QAEA,OAAO;YACL,SAAS;YACT;YACA;YACA;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,wDAAwD;QACxD,iEAAiE;QACjE,OAAO;YACL,SAAS;QACX;IACF;AACF;AAOO,eAAe,mBAAmB,MAAc;IACrD,MAAM,SAAS;IAEf,IAAI;QACF,MAAM,OAAO,IAAI;QAEjB,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QACpC,MAAM,UAAU,CAAC,gBAAgB,EAAE,QAAQ;QAC3C,MAAM,YAAY;QAElB,MAAM,CAAC,WAAW,SAAS,aAAa,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;YACrE,OAAO,GAAG,CAAC;YACX,OAAO,GAAG,CAAC;YACX,OAAO,GAAG,CAAC;YACX,OAAO,GAAG,CAAC;SACZ;QAED,MAAM,eAAe,SAAS,aAAa;QAC3C,MAAM,iBAAiB,SAAS,eAAe;QAE/C,MAAM,gBAAgB,UAAU,IAAI,MAAM,UAAU;QACpD,MAAM,kBAAkB,YAAY,IAAI,MAAM,YAAY;QAE1D,IAAI,gBAAgB,iBAAiB;YACnC,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,WAAW;gBACX;YACF;QACF;QAEA,IAAI,kBAAkB,mBAAmB;YACvC,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb;YACF;QACF;QAEA,OAAO;YACL,SAAS;YACT,WAAW;YACX,aAAa;YACb;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO;YACL,SAAS;QACX;IACF;AACF;AAKO,eAAe,gBAAgB,MAAe;IACnD,MAAM,SAAS;IAEf,IAAI;QACF,IAAI,QAAQ;YACV,MAAM,OAAO,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ;QAC9C,OAAO;YACL,qBAAqB;YACrB,MAAM,OAAO,GAAG,CAAC;QACnB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;IAChD;AACF", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/ip.ts"], "sourcesContent": ["import { headers } from \"next/headers\";\n\n/**\n * Get the user's IP address from request headers\n * Handles various proxy headers and fallbacks\n */\nexport async function getUserIP(): Promise<string> {\n  const headersList = await headers();\n  \n  // Check various headers that might contain the real IP\n  const forwardedFor = headersList.get(\"x-forwarded-for\");\n  const realIP = headersList.get(\"x-real-ip\");\n  const cfConnectingIP = headersList.get(\"cf-connecting-ip\");\n  const xClientIP = headersList.get(\"x-client-ip\");\n  \n  // x-forwarded-for can contain multiple IPs, take the first one\n  if (forwardedFor) {\n    const ips = forwardedFor.split(\",\").map(ip => ip.trim());\n    return ips[0];\n  }\n  \n  // Try other headers\n  if (cfConnectingIP) return cfConnectingIP;\n  if (realIP) return realIP;\n  if (xClientIP) return xClientIP;\n  \n  // Fallback to a default IP for development/testing\n  return \"127.0.0.1\";\n}\n"], "names": [], "mappings": ";;;AAAA;;AAMO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,sSAAA,CAAA,UAAO,AAAD;IAEhC,uDAAuD;IACvD,MAAM,eAAe,YAAY,GAAG,CAAC;IACrC,MAAM,SAAS,YAAY,GAAG,CAAC;IAC/B,MAAM,iBAAiB,YAAY,GAAG,CAAC;IACvC,MAAM,YAAY,YAAY,GAAG,CAAC;IAElC,+DAA+D;IAC/D,IAAI,cAAc;QAChB,MAAM,MAAM,aAAa,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,KAAM,GAAG,IAAI;QACrD,OAAO,GAAG,CAAC,EAAE;IACf;IAEA,oBAAoB;IACpB,IAAI,gBAAgB,OAAO;IAC3B,IAAI,QAAQ,OAAO;IACnB,IAAI,WAAW,OAAO;IAEtB,mDAAmD;IACnD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/inappropriate-content.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/inappropriate-content.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/inappropriate-content.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oZAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/inappropriate-content.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/inappropriate-content.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/inappropriate-content.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oZAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,qZAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/rate-limit-exceeded.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\nimport { Clock, Globe, Twitter, ExternalLink } from \"lucide-react\";\n\ninterface RateLimitExceededProps {\n  type: \"user\" | \"global\";\n  resetTime?: number;\n}\n\nexport default function RateLimitExceeded({ type, resetTime }: RateLimitExceededProps) {\n  const formatResetTime = (timestamp?: number) => {\n    if (!timestamp) return \"soon\";\n    \n    const now = Math.floor(Date.now() / 1000);\n    const diff = timestamp - now;\n    \n    if (diff <= 0) return \"now\";\n    \n    const hours = Math.floor(diff / 3600);\n    const minutes = Math.floor((diff % 3600) / 60);\n    \n    if (hours > 0) {\n      return `${hours}h ${minutes}m`;\n    }\n    return `${minutes}m`;\n  };\n\n  const isUserLimit = type === \"user\";\n  \n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center p-4\">\n      <div className=\"max-w-2xl w-full\">\n        <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-red-100\">\n          {/* Icon and Title */}\n          <div className=\"text-center mb-8\">\n            <div className=\"mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4\">\n              {isUserLimit ? (\n                <Clock className=\"w-8 h-8 text-red-600\" />\n              ) : (\n                <Globe className=\"w-8 h-8 text-red-600\" />\n              )}\n            </div>\n            \n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n              {isUserLimit ? \"Slow down there!\" : \"Server busy!\"}\n            </h1>\n            \n            <p className=\"text-lg text-gray-600\">\n              {isUserLimit \n                ? \"You're generating pages too quickly. Take a breather!\"\n                : \"Too many people are generating pages right now.\"\n              }\n            </p>\n          </div>\n\n          {/* Rate Limit Details */}\n          <div className=\"bg-red-50 rounded-lg p-6 mb-8 border border-red-200\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h3 className=\"font-semibold text-red-800 mb-1\">\n                  {isUserLimit ? \"Personal Rate Limit\" : \"Global Rate Limit\"}\n                </h3>\n                <p className=\"text-red-700 text-sm\">\n                  {isUserLimit \n                    ? \"You can generate 3 pages per hour\"\n                    : \"We allow 100 new pages per day across all users\"\n                  }\n                </p>\n              </div>\n              <div className=\"text-right\">\n                <p className=\"text-red-800 font-semibold\">Reset in:</p>\n                <p className=\"text-red-600 text-lg font-mono\">\n                  {formatResetTime(resetTime)}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* What you can do */}\n          <div className=\"mb-8\">\n            <h3 className=\"font-semibold text-gray-800 mb-4\">What you can do:</h3>\n            <ul className=\"space-y-2 text-gray-600\">\n              <li className=\"flex items-start\">\n                <span className=\"w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0\"></span>\n                Browse existing pages - no limits on viewing!\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0\"></span>\n                {isUserLimit \n                  ? \"Wait for your rate limit to reset\"\n                  : \"Try again later when traffic is lower\"\n                }\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0\"></span>\n                Check out our other projects while you wait\n              </li>\n            </ul>\n          </div>\n\n          {/* Call to Action Buttons */}\n          <div className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n              <Button\n                asChild\n                className=\"w-full bg-blue-600 hover:bg-blue-700 text-white\"\n              >\n                <a\n                  href=\"https://dothistaskai.com\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"flex items-center justify-center gap-2\"\n                >\n                  <ExternalLink className=\"w-4 h-4\" />\n                  Check out DoThisTaskAI\n                </a>\n              </Button>\n              \n              <Button\n                asChild\n                variant=\"outline\"\n                className=\"w-full border-blue-200 text-blue-700 hover:bg-blue-50\"\n              >\n                <a\n                  href=\"https://twitter.com/willness\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"flex items-center justify-center gap-2\"\n                >\n                  <Twitter className=\"w-4 h-4\" />\n                  Follow on Twitter\n                </a>\n              </Button>\n            </div>\n            \n            <Button\n              asChild\n              variant=\"ghost\"\n              className=\"w-full text-gray-600 hover:text-gray-800\"\n            >\n              <a href=\"/\" className=\"flex items-center justify-center gap-2\">\n                ← Back to Home\n              </a>\n            </Button>\n          </div>\n\n          {/* Footer message */}\n          <div className=\"mt-8 pt-6 border-t border-gray-200 text-center\">\n            <p className=\"text-sm text-gray-500\">\n              Rate limits help us keep the service free and fast for everyone. \n              Thanks for understanding! 🚀\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAOe,SAAS,kBAAkB,EAAE,IAAI,EAAE,SAAS,EAA0B;IACnF,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,WAAW,OAAO;QAEvB,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QACpC,MAAM,OAAO,YAAY;QAEzB,IAAI,QAAQ,GAAG,OAAO;QAEtB,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO;QAChC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,OAAQ;QAE3C,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QAChC;QACA,OAAO,GAAG,QAAQ,CAAC,CAAC;IACtB;IAEA,MAAM,cAAc,SAAS;IAE7B,qBACE,qZAAC;QAAI,WAAU;kBACb,cAAA,qZAAC;YAAI,WAAU;sBACb,cAAA,qZAAC;gBAAI,WAAU;;kCAEb,qZAAC;wBAAI,WAAU;;0CACb,qZAAC;gCAAI,WAAU;0CACZ,4BACC,qZAAC,wRAAA,CAAA,QAAK;oCAAC,WAAU;;;;;yDAEjB,qZAAC,wRAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAIrB,qZAAC;gCAAG,WAAU;0CACX,cAAc,qBAAqB;;;;;;0CAGtC,qZAAC;gCAAE,WAAU;0CACV,cACG,0DACA;;;;;;;;;;;;kCAMR,qZAAC;wBAAI,WAAU;kCACb,cAAA,qZAAC;4BAAI,WAAU;;8CACb,qZAAC;;sDACC,qZAAC;4CAAG,WAAU;sDACX,cAAc,wBAAwB;;;;;;sDAEzC,qZAAC;4CAAE,WAAU;sDACV,cACG,sCACA;;;;;;;;;;;;8CAIR,qZAAC;oCAAI,WAAU;;sDACb,qZAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,qZAAC;4CAAE,WAAU;sDACV,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;kCAOzB,qZAAC;wBAAI,WAAU;;0CACb,qZAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,qZAAC;gCAAG,WAAU;;kDACZ,qZAAC;wCAAG,WAAU;;0DACZ,qZAAC;gDAAK,WAAU;;;;;;4CAAkE;;;;;;;kDAGpF,qZAAC;wCAAG,WAAU;;0DACZ,qZAAC;gDAAK,WAAU;;;;;;4CACf,cACG,sCACA;;;;;;;kDAGN,qZAAC;wCAAG,WAAU;;0DACZ,qZAAC;gDAAK,WAAU;;;;;;4CAAkE;;;;;;;;;;;;;;;;;;;kCAOxF,qZAAC;wBAAI,WAAU;;0CACb,qZAAC;gCAAI,WAAU;;kDACb,qZAAC,kIAAA,CAAA,SAAM;wCACL,OAAO;wCACP,WAAU;kDAEV,cAAA,qZAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;;8DAEV,qZAAC,0SAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAKxC,qZAAC,kIAAA,CAAA,SAAM;wCACL,OAAO;wCACP,SAAQ;wCACR,WAAU;kDAEV,cAAA,qZAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;;8DAEV,qZAAC,4RAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;0CAMrC,qZAAC,kIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,SAAQ;gCACR,WAAU;0CAEV,cAAA,qZAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAyC;;;;;;;;;;;;;;;;;kCAOnE,qZAAC;wBAAI,WAAU;kCACb,cAAA,qZAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/%5Bslug%5D/page.tsx"], "sourcesContent": ["import { generateWebpageHtml } from \"@/lib/ai\";\nimport { fetchHtmlFromS3, uploadHtmlToS3 } from \"@/lib/s3\";\nimport { checkRateLimit } from \"@/lib/redis\";\nimport { getUserIP } from \"@/lib/ip\";\nimport { notFound } from \"next/navigation\";\nimport InappropriateContent from \"@/components/inappropriate-content\";\nimport RateLimitExceeded from \"@/components/rate-limit-exceeded\";\n\ninterface PageProps {\n  params: Promise<{ slug: string }>;\n}\n\nexport default async function SlugPage({ params }: PageProps) {\n  const { slug } = await params;\n\n  // Validate slug (basic sanitization)\n  if (!slug || slug.length > 100 || !/^[a-zA-Z0-9\\-_\\s]+$/.test(slug)) {\n    notFound();\n  }\n\n  let htmlContent: string;\n\n  try {\n    // First, try to fetch existing HTML from S3\n    const existingHtml = await fetchHtmlFromS3(slug);\n\n    if (existingHtml) {\n      htmlContent = existingHtml;\n    } else {\n      // Check rate limits before generating new content\n      const userIP = await getUserIP();\n      const rateLimitResult = await checkRateLimit(userIP);\n\n      if (!rateLimitResult.allowed) {\n        // Rate limit exceeded, show appropriate message\n        return (\n          <RateLimitExceeded\n            type={rateLimitResult.reason === \"user_limit\" ? \"user\" : \"global\"}\n            resetTime={\n              rateLimitResult.reason === \"user_limit\"\n                ? rateLimitResult.userResetTime\n                : rateLimitResult.globalResetTime\n            }\n          />\n        );\n      }\n\n      // Generate new HTML using AI\n      console.log(`Generating new webpage for slug: ${slug} (User: ${userIP})`);\n      htmlContent = await generateWebpageHtml(slug);\n\n      // Upload to S3 for caching (including inappropriate content responses)\n      try {\n        await uploadHtmlToS3(slug, htmlContent);\n        console.log(`Successfully cached webpage for slug: ${slug}`);\n      } catch (uploadError) {\n        console.error(\"Failed to cache webpage to S3:\", uploadError);\n        // Continue anyway, we have the HTML content\n      }\n    }\n  } catch (error) {\n    console.error(\"Error processing webpage:\", error);\n    throw new Error(\"Failed to generate webpage. Please try again later.\");\n  }\n\n  // Check if the content is inappropriate\n  if (htmlContent.trim() === \"INAPPROPRIATE_PROMPT_DETECTED\") {\n    return <InappropriateContent />;\n  }\n\n  // Return the HTML content directly\n  return (\n    <div\n      dangerouslySetInnerHTML={{ __html: htmlContent }}\n      style={{ width: \"100%\", height: \"100vh\" }}\n    />\n  );\n}\n\n// Generate metadata for the page\nexport async function generateMetadata({ params }: PageProps) {\n  const { slug } = await params;\n\n  return {\n    title: `${slug\n      .replace(/[-_]/g, \" \")\n      .replace(/\\b\\w/g, (l) => l.toUpperCase())} | Every Website AI`,\n    description: `AI-generated webpage for: ${slug}`,\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;;AAMe,eAAe,SAAS,EAAE,MAAM,EAAa;IAC1D,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAEvB,qCAAqC;IACrC,IAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,OAAO,CAAC,sBAAsB,IAAI,CAAC,OAAO;QACnE,CAAA,GAAA,4VAAA,CAAA,WAAQ,AAAD;IACT;IAEA,IAAI;IAEJ,IAAI;QACF,4CAA4C;QAC5C,MAAM,eAAe,MAAM,CAAA,GAAA,gHAAA,CAAA,kBAAe,AAAD,EAAE;QAE3C,IAAI,cAAc;YAChB,cAAc;QAChB,OAAO;YACL,kDAAkD;YAClD,MAAM,SAAS,MAAM,CAAA,GAAA,gHAAA,CAAA,YAAS,AAAD;YAC7B,MAAM,kBAAkB,MAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;YAE7C,IAAI,CAAC,gBAAgB,OAAO,EAAE;gBAC5B,gDAAgD;gBAChD,qBACE,qZAAC,+IAAA,CAAA,UAAiB;oBAChB,MAAM,gBAAgB,MAAM,KAAK,eAAe,SAAS;oBACzD,WACE,gBAAgB,MAAM,KAAK,eACvB,gBAAgB,aAAa,GAC7B,gBAAgB,eAAe;;;;;;YAI3C;YAEA,6BAA6B;YAC7B,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,KAAK,QAAQ,EAAE,OAAO,CAAC,CAAC;YACxE,cAAc,MAAM,CAAA,GAAA,gHAAA,CAAA,sBAAmB,AAAD,EAAE;YAExC,uEAAuE;YACvE,IAAI;gBACF,MAAM,CAAA,GAAA,gHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM;gBAC3B,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,MAAM;YAC7D,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,4CAA4C;YAC9C;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM,IAAI,MAAM;IAClB;IAEA,wCAAwC;IACxC,IAAI,YAAY,IAAI,OAAO,iCAAiC;QAC1D,qBAAO,qZAAC,8IAAA,CAAA,UAAoB;;;;;IAC9B;IAEA,mCAAmC;IACnC,qBACE,qZAAC;QACC,yBAAyB;YAAE,QAAQ;QAAY;QAC/C,OAAO;YAAE,OAAO;YAAQ,QAAQ;QAAQ;;;;;;AAG9C;AAGO,eAAe,iBAAiB,EAAE,MAAM,EAAa;IAC1D,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAEvB,OAAO;QACL,OAAO,GAAG,KACP,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,SAAS,CAAC,IAAM,EAAE,WAAW,IAAI,mBAAmB,CAAC;QAChE,aAAa,CAAC,0BAA0B,EAAE,MAAM;IAClD;AACF", "debugId": null}}]}