{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/env.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nconst envSchema = z.object({\n  // AWS S3 Configuration\n  AWS_ACCESS_KEY_ID: z.string().min(1, \"AWS_ACCESS_KEY_ID is required\"),\n  AWS_SECRET_ACCESS_KEY: z.string().min(1, \"AWS_SECRET_ACCESS_KEY is required\"),\n  AWS_REGION: z.string().min(1, \"AWS_REGION is required\"),\n  AWS_S3_BUCKET_NAME: z.string().min(1, \"AWS_S3_BUCKET_NAME is required\"),\n\n  // Anthropic Claude Configuration\n  ANTHROPIC_API_KEY: z.string().min(1, \"ANTHROPIC_API_KEY is required\"),\n\n  // Upstash Redis Configuration\n  UPSTASH_REDIS_REST_URL: z\n    .string()\n    .min(1, \"UPSTASH_REDIS_REST_URL is required\"),\n  UPSTASH_REDIS_REST_TOKEN: z\n    .string()\n    .min(1, \"UPSTASH_REDIS_REST_TOKEN is required\"),\n\n  // Next.js Environment\n  NODE_ENV: z\n    .enum([\"development\", \"production\", \"test\"])\n    .default(\"development\"),\n});\n\n// Validate environment variables\nconst parseEnv = () => {\n  // Skip validation during build time if we're not in a runtime environment\n  if (typeof window === \"undefined\" && !process.env.ANTHROPIC_API_KEY) {\n    console.warn(\"Environment variables not available during build time\");\n    return {} as z.infer<typeof envSchema>;\n  }\n\n  try {\n    return envSchema.parse(process.env);\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      const missingVars = error.errors\n        .map((err) => err.path.join(\".\"))\n        .join(\", \");\n      throw new Error(\n        `Missing or invalid environment variables: ${missingVars}`\n      );\n    }\n    throw error;\n  }\n};\n\nexport const env = parseEnv();\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,YAAY,oNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzB,uBAAuB;IACvB,mBAAmB,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACrC,uBAAuB,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzC,YAAY,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC9B,oBAAoB,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAEtC,iCAAiC;IACjC,mBAAmB,oNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAErC,8BAA8B;IAC9B,wBAAwB,oNAAA,CAAA,IAAC,CACtB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,0BAA0B,oNAAA,CAAA,IAAC,CACxB,MAAM,GACN,GAAG,CAAC,GAAG;IAEV,sBAAsB;IACtB,UAAU,oNAAA,CAAA,IAAC,CACR,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAC1C,OAAO,CAAC;AACb;AAEA,iCAAiC;AACjC,MAAM,WAAW;IACf,0EAA0E;IAC1E,IAAI,gBAAkB,eAAe,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;QACnE,QAAQ,IAAI,CAAC;QACb,OAAO,CAAC;IACV;IAEA,IAAI;QACF,OAAO,UAAU,KAAK,CAAC,QAAQ,GAAG;IACpC,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,oNAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,MAAM,cAAc,MAAM,MAAM,CAC7B,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,MAC3B,IAAI,CAAC;YACR,MAAM,IAAI,MACR,CAAC,0CAA0C,EAAE,aAAa;QAE9D;QACA,MAAM;IACR;AACF;AAEO,MAAM,MAAM", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/s3.ts"], "sourcesContent": ["import {\n  S3Client,\n  PutObjectCommand,\n  GetObjectCommand,\n  ListObjectsV2Command,\n} from \"@aws-sdk/client-s3\";\nimport { env } from \"./env\";\n\n// Initialize S3 client\nconst s3Client = new S3Client({\n  region: env.AWS_REGION,\n  credentials: {\n    accessKeyId: env.AWS_ACCESS_KEY_ID,\n    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,\n  },\n});\n\n/**\n * Upload HTML content to S3\n */\nexport async function uploadHtmlToS3(\n  slug: string,\n  htmlContent: string\n): Promise<string> {\n  const key = `websites/${slug}.html`;\n\n  // Determine content type based on content\n  const isInappropriate =\n    htmlContent.trim() === \"INAPPROPRIATE_PROMPT_DETECTED\";\n  const contentType = isInappropriate ? \"text/plain\" : \"text/html\";\n\n  const command = new PutObjectCommand({\n    Bucket: env.AWS_S3_BUCKET_NAME,\n    Key: key,\n    Body: htmlContent,\n    ContentType: contentType,\n    CacheControl: \"max-age=3600\", // Cache for 1 hour\n    Metadata: {\n      inappropriate: isInappropriate ? \"true\" : \"false\",\n    },\n  });\n\n  try {\n    await s3Client.send(command);\n    return key;\n  } catch (error) {\n    console.error(\"Error uploading to S3:\", error);\n    throw new Error(\"Failed to upload HTML to S3\");\n  }\n}\n\n/**\n * Fetch HTML content from S3\n */\nexport async function fetchHtmlFromS3(slug: string): Promise<string | null> {\n  const key = `websites/${slug}.html`;\n\n  const command = new GetObjectCommand({\n    Bucket: env.AWS_S3_BUCKET_NAME,\n    Key: key,\n  });\n\n  try {\n    const response = await s3Client.send(command);\n    if (response.Body) {\n      return await response.Body.transformToString();\n    }\n    return null;\n  } catch (error) {\n    // If file doesn't exist, return null instead of throwing\n    if ((error as { name?: string })?.name === \"NoSuchKey\") {\n      return null;\n    }\n    console.error(\"Error fetching from S3:\", error);\n    throw new Error(\"Failed to fetch HTML from S3\");\n  }\n}\n\n/**\n * Check if HTML exists in S3\n */\nexport async function htmlExistsInS3(slug: string): Promise<boolean> {\n  try {\n    const html = await fetchHtmlFromS3(slug);\n    return html !== null;\n  } catch {\n    return false;\n  }\n}\n\nexport interface RecentPage {\n  slug: string;\n  lastModified: Date;\n  title: string;\n}\n\n/**\n * Get the most recently generated pages from S3\n */\nexport async function getRecentPages(limit: number = 5): Promise<RecentPage[]> {\n  const command = new ListObjectsV2Command({\n    Bucket: env.AWS_S3_BUCKET_NAME,\n    Prefix: \"websites/\",\n    MaxKeys: 50, // Get more than we need to filter out inappropriate content\n  });\n\n  try {\n    const response = await s3Client.send(command);\n\n    if (!response.Contents) {\n      return [];\n    }\n\n    // Filter and sort pages\n    const pages = response.Contents.filter((object) => {\n      // Filter out inappropriate content and ensure we have valid objects\n      return (\n        object.Key &&\n        object.Key.endsWith(\".html\") &&\n        object.LastModified &&\n        object.Size &&\n        object.Size > 100 // Filter out very small files (likely inappropriate content)\n      );\n    })\n      .sort((a, b) => {\n        // Sort by last modified date, newest first\n        const dateA = a.LastModified?.getTime() || 0;\n        const dateB = b.LastModified?.getTime() || 0;\n        return dateB - dateA;\n      })\n      .slice(0, limit)\n      .map((object) => {\n        const slug = object.Key!.replace(\"websites/\", \"\").replace(\".html\", \"\");\n        const title = slug\n          .replace(/[-_]/g, \" \")\n          .replace(/\\b\\w/g, (l) => l.toUpperCase());\n\n        return {\n          slug,\n          lastModified: object.LastModified!,\n          title,\n        };\n      });\n\n    return pages;\n  } catch (error) {\n    console.error(\"Error fetching recent pages from S3:\", error);\n    return [];\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAMA;;;AAEA,uBAAuB;AACvB,MAAM,WAAW,IAAI,iJAAA,CAAA,WAAQ,CAAC;IAC5B,QAAQ,iHAAA,CAAA,MAAG,CAAC,UAAU;IACtB,aAAa;QACX,aAAa,iHAAA,CAAA,MAAG,CAAC,iBAAiB;QAClC,iBAAiB,iHAAA,CAAA,MAAG,CAAC,qBAAqB;IAC5C;AACF;AAKO,eAAe,eACpB,IAAY,EACZ,WAAmB;IAEnB,MAAM,MAAM,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC;IAEnC,0CAA0C;IAC1C,MAAM,kBACJ,YAAY,IAAI,OAAO;IACzB,MAAM,cAAc,kBAAkB,eAAe;IAErD,MAAM,UAAU,IAAI,iJAAA,CAAA,mBAAgB,CAAC;QACnC,QAAQ,iHAAA,CAAA,MAAG,CAAC,kBAAkB;QAC9B,KAAK;QACL,MAAM;QACN,aAAa;QACb,cAAc;QACd,UAAU;YACR,eAAe,kBAAkB,SAAS;QAC5C;IACF;IAEA,IAAI;QACF,MAAM,SAAS,IAAI,CAAC;QACpB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,gBAAgB,IAAY;IAChD,MAAM,MAAM,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC;IAEnC,MAAM,UAAU,IAAI,iJAAA,CAAA,mBAAgB,CAAC;QACnC,QAAQ,iHAAA,CAAA,MAAG,CAAC,kBAAkB;QAC9B,KAAK;IACP;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,SAAS,IAAI,CAAC;QACrC,IAAI,SAAS,IAAI,EAAE;YACjB,OAAO,MAAM,SAAS,IAAI,CAAC,iBAAiB;QAC9C;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,yDAAyD;QACzD,IAAI,AAAC,OAA6B,SAAS,aAAa;YACtD,OAAO;QACT;QACA,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,eAAe,IAAY;IAC/C,IAAI;QACF,MAAM,OAAO,MAAM,gBAAgB;QACnC,OAAO,SAAS;IAClB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAWO,eAAe,eAAe,QAAgB,CAAC;IACpD,MAAM,UAAU,IAAI,iJAAA,CAAA,uBAAoB,CAAC;QACvC,QAAQ,iHAAA,CAAA,MAAG,CAAC,kBAAkB;QAC9B,QAAQ;QACR,SAAS;IACX;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,SAAS,IAAI,CAAC;QAErC,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,OAAO,EAAE;QACX;QAEA,wBAAwB;QACxB,MAAM,QAAQ,SAAS,QAAQ,CAAC,MAAM,CAAC,CAAC;YACtC,oEAAoE;YACpE,OACE,OAAO,GAAG,IACV,OAAO,GAAG,CAAC,QAAQ,CAAC,YACpB,OAAO,YAAY,IACnB,OAAO,IAAI,IACX,OAAO,IAAI,GAAG,IAAI,6DAA6D;;QAEnF,GACG,IAAI,CAAC,CAAC,GAAG;YACR,2CAA2C;YAC3C,MAAM,QAAQ,EAAE,YAAY,EAAE,aAAa;YAC3C,MAAM,QAAQ,EAAE,YAAY,EAAE,aAAa;YAC3C,OAAO,QAAQ;QACjB,GACC,KAAK,CAAC,GAAG,OACT,GAAG,CAAC,CAAC;YACJ,MAAM,OAAO,OAAO,GAAG,CAAE,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,SAAS;YACnE,MAAM,QAAQ,KACX,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,SAAS,CAAC,IAAM,EAAE,WAAW;YAExC,OAAO;gBACL;gBACA,cAAc,OAAO,YAAY;gBACjC;YACF;QACF;QAEF,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO,EAAE;IACX;AACF", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/homepage-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/homepage-form.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/homepage-form.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oZAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/homepage-form.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/homepage-form.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/homepage-form.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oZAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/recent-pages.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { RecentPage } from \"@/lib/s3\";\nimport { Clock } from \"lucide-react\";\n\ninterface RecentPagesProps {\n  pages: RecentPage[];\n}\n\nexport default function RecentPages({ pages }: RecentPagesProps) {\n  if (pages.length === 0) {\n    return null;\n  }\n\n  const formatTimeAgo = (date: Date) => {\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return \"Just now\";\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `${diffInDays}d ago`;\n    \n    return date.toLocaleDateString();\n  };\n\n  return (\n    <section className=\"py-12 sm:py-16 bg-white/30\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6\">\n        <h2 className=\"text-2xl sm:text-3xl font-bold text-center text-gray-900 mb-8 sm:mb-12\">\n          Recently Generated Pages\n        </h2>\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6\">\n          {pages.map((page) => (\n            <Link\n              key={page.slug}\n              href={`/${page.slug}`}\n              className=\"group block bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-200 border border-gray-100 hover:border-blue-200 overflow-hidden\"\n            >\n              <div className=\"p-4 sm:p-6\">\n                <div className=\"flex items-start justify-between mb-3\">\n                  <h3 className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2 text-sm sm:text-base\">\n                    {page.title}\n                  </h3>\n                </div>\n                \n                <div className=\"flex items-center text-gray-500 text-xs sm:text-sm\">\n                  <Clock className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0\" />\n                  <span>{formatTimeAgo(page.lastModified)}</span>\n                </div>\n                \n                <div className=\"mt-3 pt-3 border-t border-gray-100\">\n                  <p className=\"text-xs text-gray-600 font-mono break-all\">\n                    everywebsite.ai/{page.slug}\n                  </p>\n                </div>\n              </div>\n              \n              <div className=\"h-1 bg-gradient-to-r from-blue-500 to-indigo-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-200 origin-left\"></div>\n            </Link>\n          ))}\n        </div>\n        \n        <div className=\"text-center mt-8\">\n          <p className=\"text-gray-600 text-sm\">\n            These pages were generated by other users. Click to view them!\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAMe,SAAS,YAAY,EAAE,KAAK,EAAoB;IAC7D,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,GAAG,cAAc,KAAK,CAAC;QAEtD,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,IAAI,cAAc,IAAI,OAAO,GAAG,YAAY,KAAK,CAAC;QAElD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,KAAK,CAAC;QAE/C,OAAO,KAAK,kBAAkB;IAChC;IAEA,qBACE,qZAAC;QAAQ,WAAU;kBACjB,cAAA,qZAAC;YAAI,WAAU;;8BACb,qZAAC;oBAAG,WAAU;8BAAyE;;;;;;8BAGvF,qZAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,qZAAC,mUAAA,CAAA,UAAI;4BAEH,MAAM,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE;4BACrB,WAAU;;8CAEV,qZAAC;oCAAI,WAAU;;sDACb,qZAAC;4CAAI,WAAU;sDACb,cAAA,qZAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;;;;;;sDAIf,qZAAC;4CAAI,WAAU;;8DACb,qZAAC,wRAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,qZAAC;8DAAM,cAAc,KAAK,YAAY;;;;;;;;;;;;sDAGxC,qZAAC;4CAAI,WAAU;sDACb,cAAA,qZAAC;gDAAE,WAAU;;oDAA4C;oDACtC,KAAK,IAAI;;;;;;;;;;;;;;;;;;8CAKhC,qZAAC;oCAAI,WAAU;;;;;;;2BAvBV,KAAK,IAAI;;;;;;;;;;8BA4BpB,qZAAC;oBAAI,WAAU;8BACb,cAAA,qZAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/page.tsx"], "sourcesContent": ["import { getRecentPages } from \"@/lib/s3\";\nimport HomepageForm from \"@/components/homepage-form\";\nimport RecentPages from \"@/components/recent-pages\";\n\nexport default async function Home() {\n  // Fetch recent pages from S3\n  const recentPages = await getRecentPages(6);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\n      {/* Header */}\n      <header className=\"pt-6 sm:pt-8 pb-4 text-center\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6\">\n          <h1 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4\">\n            Every Website AI\n          </h1>\n          <p className=\"text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-2\">\n            Generate any webpage instantly with AI. Just change the URL to\n            describe what you want.\n          </p>\n          <div className=\"mt-6 p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-200 max-w-xl mx-auto\">\n            <p className=\"text-blue-800 font-mono text-sm sm:text-base lg:text-lg break-all\">\n              everywebsite.ai/\n              <span className=\"bg-blue-200 px-1 sm:px-2 py-1 rounded\">\n                your-prompt-here\n              </span>\n            </p>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"flex-1 flex items-center justify-center px-4 sm:px-6 py-8 sm:py-12\">\n        <HomepageForm />\n      </main>\n\n      {/* Recent Pages */}\n      <RecentPages pages={recentPages} />\n\n      {/* How it works */}\n      <section className=\"py-12 sm:py-16 bg-white/50\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6\">\n          <h2 className=\"text-2xl sm:text-3xl font-bold text-center text-gray-900 mb-8 sm:mb-12\">\n            How it works\n          </h2>\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-blue-600 font-bold text-lg\">1</span>\n              </div>\n              <h3 className=\"font-semibold text-gray-800 mb-2 text-base sm:text-lg\">\n                Change the URL\n              </h3>\n              <p className=\"text-gray-600 text-sm sm:text-base px-2\">\n                Add your prompt to the URL: everywebsite.ai/your-idea\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-blue-600 font-bold text-lg\">2</span>\n              </div>\n              <h3 className=\"font-semibold text-gray-800 mb-2 text-base sm:text-lg\">\n                AI Generates\n              </h3>\n              <p className=\"text-gray-600 text-sm sm:text-base px-2\">\n                Our AI instantly creates a complete, responsive webpage\n              </p>\n            </div>\n            <div className=\"text-center sm:col-span-2 lg:col-span-1\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-blue-600 font-bold text-lg\">3</span>\n              </div>\n              <h3 className=\"font-semibold text-gray-800 mb-2 text-base sm:text-lg\">\n                That&apos;s it!\n              </h3>\n              <p className=\"text-gray-600 text-sm sm:text-base px-2\">\n                Your custom webpage is ready to use and share\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"py-6 sm:py-8 text-center text-gray-500 text-xs sm:text-sm px-4\">\n        <p className=\"flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-0\">\n          <span>\n            Powered by{\" \"}\n            <a\n              href=\"https://dothistask.ai\"\n              className=\"underline hover:text-gray-700\"\n            >\n              dothistask.ai\n            </a>\n          </span>\n          <span className=\"hidden sm:inline\">{\" • \"}</span>\n          <a\n            href=\"https://twitter.com/n3sonline\"\n            className=\"underline hover:text-gray-700\"\n          >\n            @n3sonline\n          </a>\n        </p>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,eAAe;IAC5B,6BAA6B;IAC7B,MAAM,cAAc,MAAM,CAAA,GAAA,gHAAA,CAAA,iBAAc,AAAD,EAAE;IAEzC,qBACE,qZAAC;QAAI,WAAU;;0BAEb,qZAAC;gBAAO,WAAU;0BAChB,cAAA,qZAAC;oBAAI,WAAU;;sCACb,qZAAC;4BAAG,WAAU;sCAAgE;;;;;;sCAG9E,qZAAC;4BAAE,WAAU;sCAA0D;;;;;;sCAIvE,qZAAC;4BAAI,WAAU;sCACb,cAAA,qZAAC;gCAAE,WAAU;;oCAAoE;kDAE/E,qZAAC;wCAAK,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShE,qZAAC;gBAAK,WAAU;0BACd,cAAA,qZAAC,sIAAA,CAAA,UAAY;;;;;;;;;;0BAIf,qZAAC,qIAAA,CAAA,UAAW;gBAAC,OAAO;;;;;;0BAGpB,qZAAC;gBAAQ,WAAU;0BACjB,cAAA,qZAAC;oBAAI,WAAU;;sCACb,qZAAC;4BAAG,WAAU;sCAAyE;;;;;;sCAGvF,qZAAC;4BAAI,WAAU;;8CACb,qZAAC;oCAAI,WAAU;;sDACb,qZAAC;4CAAI,WAAU;sDACb,cAAA,qZAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,qZAAC;4CAAG,WAAU;sDAAwD;;;;;;sDAGtE,qZAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAIzD,qZAAC;oCAAI,WAAU;;sDACb,qZAAC;4CAAI,WAAU;sDACb,cAAA,qZAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,qZAAC;4CAAG,WAAU;sDAAwD;;;;;;sDAGtE,qZAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAIzD,qZAAC;oCAAI,WAAU;;sDACb,qZAAC;4CAAI,WAAU;sDACb,cAAA,qZAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,qZAAC;4CAAG,WAAU;sDAAwD;;;;;;sDAGtE,qZAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/D,qZAAC;gBAAO,WAAU;0BAChB,cAAA,qZAAC;oBAAE,WAAU;;sCACX,qZAAC;;gCAAK;gCACO;8CACX,qZAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAIH,qZAAC;4BAAK,WAAU;sCAAoB;;;;;;sCACpC,qZAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}]}