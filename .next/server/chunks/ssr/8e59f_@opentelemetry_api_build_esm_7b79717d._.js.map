{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/context/context.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/context/context.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from './types';\n\n/** Get a key to uniquely identify a context value */\nexport function createContextKey(description: string) {\n  // The specification states that for the same input, multiple calls should\n  // return different keys. Due to the nature of the JS dependency management\n  // system, this creates problems where multiple versions of some package\n  // could hold different keys for the same property.\n  //\n  // Therefore, we use Symbol.for which returns the same key for the same input.\n  return Symbol.for(description);\n}\n\nclass BaseContext implements Context {\n  private _currentContext!: Map<symbol, unknown>;\n\n  /**\n   * Construct a new context which inherits values from an optional parent context.\n   *\n   * @param parentContext a context from which to inherit values\n   */\n  constructor(parentContext?: Map<symbol, unknown>) {\n    // for minification\n    const self = this;\n\n    self._currentContext = parentContext ? new Map(parentContext) : new Map();\n\n    self.getValue = (key: symbol) => self._currentContext.get(key);\n\n    self.setValue = (key: symbol, value: unknown): Context => {\n      const context = new BaseContext(self._currentContext);\n      context._currentContext.set(key, value);\n      return context;\n    };\n\n    self.deleteValue = (key: symbol): Context => {\n      const context = new BaseContext(self._currentContext);\n      context._currentContext.delete(key);\n      return context;\n    };\n  }\n\n  /**\n   * Get a value from the context.\n   *\n   * @param key key which identifies a context value\n   */\n  public getValue!: (key: symbol) => unknown;\n\n  /**\n   * Create a new context which inherits from this context and has\n   * the given key set to the given value.\n   *\n   * @param key context key for which to set the value\n   * @param value value to set for the given key\n   */\n  public setValue!: (key: symbol, value: unknown) => Context;\n\n  /**\n   * Return a new context which inherits from this context but does\n   * not contain a value for the given key.\n   *\n   * @param key context key for which to clear a value\n   */\n  public deleteValue!: (key: symbol) => Context;\n}\n\n/** The root context is used as the default parent context when there is no active context */\nexport const ROOT_CONTEXT: Context = new BaseContext();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAIH,mDAAA,EAAqD;;;;AAC/C,SAAU,gBAAgB,CAAC,WAAmB;IAClD,0EAA0E;IAC1E,2EAA2E;IAC3E,wEAAwE;IACxE,mDAAmD;IACnD,EAAE;IACF,8EAA8E;IAC9E,OAAO,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACjC,CAAC;AAED,IAAA,cAAA;IAGE;;;;OAIG,CACH,SAAA,YAAY,aAAoC;QAC9C,mBAAmB;QACnB,IAAM,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;QAE1E,IAAI,CAAC,QAAQ,GAAG,SAAC,GAAW;YAAK,OAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC;QAA7B,CAA6B,CAAC;QAE/D,IAAI,CAAC,QAAQ,GAAG,SAAC,GAAW,EAAE,KAAc;YAC1C,IAAM,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtD,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACxC,OAAO,OAAO,CAAC;QACjB,CAAC,CAAC;QAEF,IAAI,CAAC,WAAW,GAAG,SAAC,GAAW;YAC7B,IAAM,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtD,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACpC,OAAO,OAAO,CAAC;QACjB,CAAC,CAAC;IACJ,CAAC;IAyBH,OAAA,WAAC;AAAD,CAAC,AApDD,IAoDC;AAGM,IAAM,YAAY,GAAY,IAAI,WAAW,EAAE,CAAC", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/context/NoopContextManager.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/context/NoopContextManager.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ROOT_CONTEXT } from './context';\nimport * as types from './types';\n\nexport class NoopContextManager implements types.ContextManager {\n  active(): types.Context {\n    return ROOT_CONTEXT;\n  }\n\n  with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n    _context: types.Context,\n    fn: F,\n    thisArg?: ThisParameterType<F>,\n    ...args: A\n  ): ReturnType<F> {\n    return fn.call(thisArg, ...args);\n  }\n\n  bind<T>(_context: types.Context, target: T): T {\n    return target;\n  }\n\n  enable(): this {\n    return this;\n  }\n\n  disable(): this {\n    return this;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,WAAW,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGzC,IAAA,qBAAA;IAAA,SAAA,sBAyBA,CAAC;IAxBC,mBAAA,SAAA,CAAA,MAAM,GAAN;QACE,sPAAO,gBAAY,CAAC;IACtB,CAAC;IAED,mBAAA,SAAA,CAAA,IAAI,GAAJ,SACE,QAAuB,EACvB,EAAK,EACL,OAA8B;QAC9B,IAAA,OAAA,EAAA,CAAU;YAAV,IAAA,KAAA,CAAU,EAAV,KAAA,UAAA,MAAU,EAAV,IAAU,CAAA;YAAV,IAAA,CAAA,KAAA,EAAA,GAAA,SAAA,CAAA,GAAA,CAAU;;QAEV,OAAO,EAAE,CAAC,IAAI,CAAA,KAAA,CAAP,EAAE,EAAA,cAAA;YAAM,OAAO;SAAA,EAAA,OAAK,IAAI,GAAA,QAAE;IACnC,CAAC;IAED,mBAAA,SAAA,CAAA,IAAI,GAAJ,SAAQ,QAAuB,EAAE,MAAS;QACxC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,mBAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAA,SAAA,CAAA,OAAO,GAAP;QACE,OAAO,IAAI,CAAC;IACd,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AAzBD,IAyBC", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/platform/node/globalThis.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/platform/node/globalThis.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** only globals that common to node and browsers are allowed */\n// eslint-disable-next-line node/no-unsupported-features/es-builtins\nexport const _globalThis = typeof globalThis === 'object' ? globalThis : global;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,8DAAA,EAAgE,CAChE,oEAAoE;;;;AAC7D,IAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/version.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/version.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// this is autogenerated file, see scripts/version-update.js\nexport const VERSION = '1.9.0';\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,4DAA4D;;;;AACrD,IAAM,OAAO,GAAG,OAAO,CAAC", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/internal/semver.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/internal/semver.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { VERSION } from '../version';\n\nconst re = /^(\\d+)\\.(\\d+)\\.(\\d+)(-(.+))?$/;\n\n/**\n * Create a function to test an API version to see if it is compatible with the provided ownVersion.\n *\n * The returned function has the following semantics:\n * - Exact match is always compatible\n * - Major versions must match exactly\n *    - 1.x package cannot use global 2.x package\n *    - 2.x package cannot use global 1.x package\n * - The minor version of the API module requesting access to the global API must be less than or equal to the minor version of this API\n *    - 1.3 package may use 1.4 global because the later global contains all functions 1.3 expects\n *    - 1.4 package may NOT use 1.3 global because it may try to call functions which don't exist on 1.3\n * - If the major version is 0, the minor version is treated as the major and the patch is treated as the minor\n * - Patch and build tag differences are not considered at this time\n *\n * @param ownVersion version which should be checked against\n */\nexport function _makeCompatibilityCheck(\n  ownVersion: string\n): (globalVersion: string) => boolean {\n  const acceptedVersions = new Set<string>([ownVersion]);\n  const rejectedVersions = new Set<string>();\n\n  const myVersionMatch = ownVersion.match(re);\n  if (!myVersionMatch) {\n    // we cannot guarantee compatibility so we always return noop\n    return () => false;\n  }\n\n  const ownVersionParsed = {\n    major: +myVersionMatch[1],\n    minor: +myVersionMatch[2],\n    patch: +myVersionMatch[3],\n    prerelease: myVersionMatch[4],\n  };\n\n  // if ownVersion has a prerelease tag, versions must match exactly\n  if (ownVersionParsed.prerelease != null) {\n    return function isExactmatch(globalVersion: string): boolean {\n      return globalVersion === ownVersion;\n    };\n  }\n\n  function _reject(v: string) {\n    rejectedVersions.add(v);\n    return false;\n  }\n\n  function _accept(v: string) {\n    acceptedVersions.add(v);\n    return true;\n  }\n\n  return function isCompatible(globalVersion: string): boolean {\n    if (acceptedVersions.has(globalVersion)) {\n      return true;\n    }\n\n    if (rejectedVersions.has(globalVersion)) {\n      return false;\n    }\n\n    const globalVersionMatch = globalVersion.match(re);\n    if (!globalVersionMatch) {\n      // cannot parse other version\n      // we cannot guarantee compatibility so we always noop\n      return _reject(globalVersion);\n    }\n\n    const globalVersionParsed = {\n      major: +globalVersionMatch[1],\n      minor: +globalVersionMatch[2],\n      patch: +globalVersionMatch[3],\n      prerelease: globalVersionMatch[4],\n    };\n\n    // if globalVersion has a prerelease tag, versions must match exactly\n    if (globalVersionParsed.prerelease != null) {\n      return _reject(globalVersion);\n    }\n\n    // major versions must match\n    if (ownVersionParsed.major !== globalVersionParsed.major) {\n      return _reject(globalVersion);\n    }\n\n    if (ownVersionParsed.major === 0) {\n      if (\n        ownVersionParsed.minor === globalVersionParsed.minor &&\n        ownVersionParsed.patch <= globalVersionParsed.patch\n      ) {\n        return _accept(globalVersion);\n      }\n\n      return _reject(globalVersion);\n    }\n\n    if (ownVersionParsed.minor <= globalVersionParsed.minor) {\n      return _accept(globalVersion);\n    }\n\n    return _reject(globalVersion);\n  };\n}\n\n/**\n * Test an API version to see if it is compatible with this API.\n *\n * - Exact match is always compatible\n * - Major versions must match exactly\n *    - 1.x package cannot use global 2.x package\n *    - 2.x package cannot use global 1.x package\n * - The minor version of the API module requesting access to the global API must be less than or equal to the minor version of this API\n *    - 1.3 package may use 1.4 global because the later global contains all functions 1.3 expects\n *    - 1.4 package may NOT use 1.3 global because it may try to call functions which don't exist on 1.3\n * - If the major version is 0, the minor version is treated as the major and the patch is treated as the minor\n * - Patch and build tag differences are not considered at this time\n *\n * @param version version of the API requesting an instance of the global API\n */\nexport const isCompatible = _makeCompatibilityCheck(VERSION);\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;AAErC,IAAM,EAAE,GAAG,+BAA+B,CAAC;AAkBrC,SAAU,uBAAuB,CACrC,UAAkB;IAElB,IAAM,gBAAgB,GAAG,IAAI,GAAG,CAAS;QAAC,UAAU;KAAC,CAAC,CAAC;IACvD,IAAM,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;IAE3C,IAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC5C,IAAI,CAAC,cAAc,EAAE;QACnB,6DAA6D;QAC7D,OAAO;YAAM,OAAA,KAAK;QAAL,CAAK,CAAC;KACpB;IAED,IAAM,gBAAgB,GAAG;QACvB,KAAK,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;QACzB,KAAK,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;QACzB,KAAK,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;QACzB,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC;KAC9B,CAAC;IAEF,kEAAkE;IAClE,IAAI,gBAAgB,CAAC,UAAU,IAAI,IAAI,EAAE;QACvC,OAAO,SAAS,YAAY,CAAC,aAAqB;YAChD,OAAO,aAAa,KAAK,UAAU,CAAC;QACtC,CAAC,CAAC;KACH;IAED,SAAS,OAAO,CAAC,CAAS;QACxB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,SAAS,OAAO,CAAC,CAAS;QACxB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,SAAS,YAAY,CAAC,aAAqB;QAChD,IAAI,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;YACvC,OAAO,IAAI,CAAC;SACb;QAED,IAAI,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;YACvC,OAAO,KAAK,CAAC;SACd;QAED,IAAM,kBAAkB,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACnD,IAAI,CAAC,kBAAkB,EAAE;YACvB,6BAA6B;YAC7B,sDAAsD;YACtD,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;SAC/B;QAED,IAAM,mBAAmB,GAAG;YAC1B,KAAK,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAC7B,KAAK,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAC7B,KAAK,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAC7B,UAAU,EAAE,kBAAkB,CAAC,CAAC,CAAC;SAClC,CAAC;QAEF,qEAAqE;QACrE,IAAI,mBAAmB,CAAC,UAAU,IAAI,IAAI,EAAE;YAC1C,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;SAC/B;QAED,4BAA4B;QAC5B,IAAI,gBAAgB,CAAC,KAAK,KAAK,mBAAmB,CAAC,KAAK,EAAE;YACxD,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;SAC/B;QAED,IAAI,gBAAgB,CAAC,KAAK,KAAK,CAAC,EAAE;YAChC,IACE,gBAAgB,CAAC,KAAK,KAAK,mBAAmB,CAAC,KAAK,IACpD,gBAAgB,CAAC,KAAK,IAAI,mBAAmB,CAAC,KAAK,EACnD;gBACA,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;aAC/B;YAED,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;SAC/B;QAED,IAAI,gBAAgB,CAAC,KAAK,IAAI,mBAAmB,CAAC,KAAK,EAAE;YACvD,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;SAC/B;QAED,OAAO,OAAO,CAAC,aAAa,CAAC,CAAC;IAChC,CAAC,CAAC;AACJ,CAAC;AAiBM,IAAM,YAAY,GAAG,uBAAuB,sOAAC,UAAO,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/internal/global-utils.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/internal/global-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MeterProvider } from '../metrics/MeterProvider';\nimport { ContextManager } from '../context/types';\nimport { DiagLogger } from '../diag/types';\nimport { _globalThis } from '../platform';\nimport { TextMapPropagator } from '../propagation/TextMapPropagator';\nimport type { TracerProvider } from '../trace/tracer_provider';\nimport { VERSION } from '../version';\nimport { isCompatible } from './semver';\n\nconst major = VERSION.split('.')[0];\nconst GLOBAL_OPENTELEMETRY_API_KEY = Symbol.for(\n  `opentelemetry.js.api.${major}`\n);\n\nconst _global = _globalThis as OTelGlobal;\n\nexport function registerGlobal<Type extends keyof OTelGlobalAPI>(\n  type: Type,\n  instance: OTelGlobalAPI[Type],\n  diag: DiagLogger,\n  allowOverride = false\n): boolean {\n  const api = (_global[GLOBAL_OPENTELEMETRY_API_KEY] = _global[\n    GLOBAL_OPENTELEMETRY_API_KEY\n  ] ?? {\n    version: VERSION,\n  });\n\n  if (!allowOverride && api[type]) {\n    // already registered an API of this type\n    const err = new Error(\n      `@opentelemetry/api: Attempted duplicate registration of API: ${type}`\n    );\n    diag.error(err.stack || err.message);\n    return false;\n  }\n\n  if (api.version !== VERSION) {\n    // All registered APIs must be of the same version exactly\n    const err = new Error(\n      `@opentelemetry/api: Registration of version v${api.version} for ${type} does not match previously registered API v${VERSION}`\n    );\n    diag.error(err.stack || err.message);\n    return false;\n  }\n\n  api[type] = instance;\n  diag.debug(\n    `@opentelemetry/api: Registered a global for ${type} v${VERSION}.`\n  );\n\n  return true;\n}\n\nexport function getGlobal<Type extends keyof OTelGlobalAPI>(\n  type: Type\n): OTelGlobalAPI[Type] | undefined {\n  const globalVersion = _global[GLOBAL_OPENTELEMETRY_API_KEY]?.version;\n  if (!globalVersion || !isCompatible(globalVersion)) {\n    return;\n  }\n  return _global[GLOBAL_OPENTELEMETRY_API_KEY]?.[type];\n}\n\nexport function unregisterGlobal(type: keyof OTelGlobalAPI, diag: DiagLogger) {\n  diag.debug(\n    `@opentelemetry/api: Unregistering a global for ${type} v${VERSION}.`\n  );\n  const api = _global[GLOBAL_OPENTELEMETRY_API_KEY];\n\n  if (api) {\n    delete api[type];\n  }\n}\n\ntype OTelGlobal = {\n  [GLOBAL_OPENTELEMETRY_API_KEY]?: OTelGlobalAPI;\n};\n\ntype OTelGlobalAPI = {\n  version: string;\n\n  diag?: DiagLogger;\n  trace?: TracerProvider;\n  context?: ContextManager;\n  metrics?: MeterProvider;\n  propagation?: TextMapPropagator;\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;AAKH,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAG1C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;;;;AAExC,IAAM,KAAK,wOAAG,UAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,IAAM,4BAA4B,GAAG,MAAM,CAAC,GAAG,CAC7C,0BAAwB,KAAO,CAChC,CAAC;AAEF,IAAM,OAAO,GAAG,0QAAyB,CAAC;AAEpC,SAAU,cAAc,CAC5B,IAAU,EACV,QAA6B,EAC7B,IAAgB,EAChB,aAAqB;;IAArB,IAAA,kBAAA,KAAA,GAAA;QAAA,gBAAA,KAAqB;IAAA;IAErB,IAAM,GAAG,GAAG,AAAC,OAAO,CAAC,4BAA4B,CAAC,GAAG,CAAA,KAAA,OAAO,CAC1D,4BAA4B,CAC7B,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI;QACH,OAAO,uOAAE,UAAO;KACjB,CAAC,CAAC;IAEH,IAAI,CAAC,aAAa,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE;QAC/B,yCAAyC;QACzC,IAAM,GAAG,GAAG,IAAI,KAAK,CACnB,kEAAgE,IAAM,CACvE,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;QACrC,OAAO,KAAK,CAAC;KACd;IAED,IAAI,GAAG,CAAC,OAAO,KAAK,+OAAO,EAAE;QAC3B,0DAA0D;QAC1D,IAAM,GAAG,GAAG,IAAI,KAAK,CACnB,kDAAgD,GAAG,CAAC,OAAO,GAAA,UAAQ,IAAI,GAAA,qRAA8C,UAAS,CAC/H,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;QACrC,OAAO,KAAK,CAAC;KACd;IAED,GAAG,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;IACrB,IAAI,CAAC,KAAK,CACR,iDAA+C,IAAI,GAAA,4OAAK,UAAO,GAAA,GAAG,CACnE,CAAC;IAEF,OAAO,IAAI,CAAC;AACd,CAAC;AAEK,SAAU,SAAS,CACvB,IAAU;;IAEV,IAAM,aAAa,GAAG,CAAA,KAAA,OAAO,CAAC,4BAA4B,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,OAAO,CAAC;IACrE,IAAI,CAAC,aAAa,IAAI,qPAAC,eAAA,AAAY,EAAC,aAAa,CAAC,EAAE;QAClD,OAAO;KACR;IACD,OAAO,CAAA,KAAA,OAAO,CAAC,4BAA4B,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,EAAA,CAAG,IAAI,CAAC,CAAC;AACvD,CAAC;AAEK,SAAU,gBAAgB,CAAC,IAAyB,EAAE,IAAgB;IAC1E,IAAI,CAAC,KAAK,CACR,oDAAkD,IAAI,GAAA,4OAAK,UAAO,GAAA,GAAG,CACtE,CAAC;IACF,IAAM,GAAG,GAAG,OAAO,CAAC,4BAA4B,CAAC,CAAC;IAElD,IAAI,GAAG,EAAE;QACP,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;KAClB;AACH,CAAC", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/diag/ComponentLogger.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/diag/ComponentLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getGlobal } from '../internal/global-utils';\nimport { ComponentLoggerOptions, DiagLogger, DiagLogFunction } from './types';\n\n/**\n * Component Logger which is meant to be used as part of any component which\n * will add automatically additional namespace in front of the log message.\n * It will then forward all message to global diag logger\n * @example\n * const cLogger = diag.createComponentLogger({ namespace: '@opentelemetry/instrumentation-http' });\n * cLogger.debug('test');\n * // @opentelemetry/instrumentation-http test\n */\nexport class DiagComponentLogger implements DiagLogger {\n  private _namespace: string;\n\n  constructor(props: ComponentLoggerOptions) {\n    this._namespace = props.namespace || 'DiagComponentLogger';\n  }\n\n  public debug(...args: any[]): void {\n    return logProxy('debug', this._namespace, args);\n  }\n\n  public error(...args: any[]): void {\n    return logProxy('error', this._namespace, args);\n  }\n\n  public info(...args: any[]): void {\n    return logProxy('info', this._namespace, args);\n  }\n\n  public warn(...args: any[]): void {\n    return logProxy('warn', this._namespace, args);\n  }\n\n  public verbose(...args: any[]): void {\n    return logProxy('verbose', this._namespace, args);\n  }\n}\n\nfunction logProxy(\n  funcName: keyof DiagLogger,\n  namespace: string,\n  args: any\n): void {\n  const logger = getGlobal('diag');\n  // shortcut if logger not set\n  if (!logger) {\n    return;\n  }\n\n  args.unshift(namespace);\n  return logger[funcName](...(args as Parameters<DiagLogFunction>));\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGrD;;;;;;;;GAQG,CACH,IAAA,sBAAA;IAGE,SAAA,oBAAY,KAA6B;QACvC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,IAAI,qBAAqB,CAAC;IAC7D,CAAC;IAEM,oBAAA,SAAA,CAAA,KAAK,GAAZ;QAAa,IAAA,OAAA,EAAA,CAAc;YAAd,IAAA,KAAA,CAAc,EAAd,KAAA,UAAA,MAAc,EAAd,IAAc,CAAA;YAAd,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAc;;QACzB,OAAO,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;IAEM,oBAAA,SAAA,CAAA,KAAK,GAAZ;QAAa,IAAA,OAAA,EAAA,CAAc;YAAd,IAAA,KAAA,CAAc,EAAd,KAAA,UAAA,MAAc,EAAd,IAAc,CAAA;YAAd,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAc;;QACzB,OAAO,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;IAEM,oBAAA,SAAA,CAAA,IAAI,GAAX;QAAY,IAAA,OAAA,EAAA,CAAc;YAAd,IAAA,KAAA,CAAc,EAAd,KAAA,UAAA,MAAc,EAAd,IAAc,CAAA;YAAd,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAc;;QACxB,OAAO,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAEM,oBAAA,SAAA,CAAA,IAAI,GAAX;QAAY,IAAA,OAAA,EAAA,CAAc;YAAd,IAAA,KAAA,CAAc,EAAd,KAAA,UAAA,MAAc,EAAd,IAAc,CAAA;YAAd,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAc;;QACxB,OAAO,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAEM,oBAAA,SAAA,CAAA,OAAO,GAAd;QAAe,IAAA,OAAA,EAAA,CAAc;YAAd,IAAA,KAAA,CAAc,EAAd,KAAA,UAAA,MAAc,EAAd,IAAc,CAAA;YAAd,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAc;;QAC3B,OAAO,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AA1BD,IA0BC;;AAED,SAAS,QAAQ,CACf,QAA0B,EAC1B,SAAiB,EACjB,IAAS;IAET,IAAM,MAAM,gQAAG,YAAA,AAAS,EAAC,MAAM,CAAC,CAAC;IACjC,6BAA6B;IAC7B,IAAI,CAAC,MAAM,EAAE;QACX,OAAO;KACR;IAED,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACxB,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAA,KAAA,CAAhB,MAAM,EAAA,cAAA,EAAA,EAAA,OAAe,IAAoC,GAAA,QAAE;AACpE,CAAC", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/diag/types.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/diag/types.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type DiagLogFunction = (message: string, ...args: unknown[]) => void;\n\n/**\n * Defines an internal diagnostic logger interface which is used to log internal diagnostic\n * messages, you can set the default diagnostic logger via the {@link DiagAPI} setLogger function.\n * API provided implementations include :-\n * - a No-Op {@link createNoopDiagLogger}\n * - a {@link DiagLogLevel} filtering wrapper {@link createLogLevelDiagLogger}\n * - a general Console {@link DiagConsoleLogger} version.\n */\nexport interface DiagLogger {\n  /** Log an error scenario that was not expected and caused the requested operation to fail. */\n  error: DiagLogFunction;\n\n  /**\n   * Log a warning scenario to inform the developer of an issues that should be investigated.\n   * The requested operation may or may not have succeeded or completed.\n   */\n  warn: DiagLogFunction;\n\n  /**\n   * Log a general informational message, this should not affect functionality.\n   * This is also the default logging level so this should NOT be used for logging\n   * debugging level information.\n   */\n  info: DiagLogFunction;\n\n  /**\n   * Log a general debug message that can be useful for identifying a failure.\n   * Information logged at this level may include diagnostic details that would\n   * help identify a failure scenario.\n   * For example: Logging the order of execution of async operations.\n   */\n  debug: DiagLogFunction;\n\n  /**\n   * Log a detailed (verbose) trace level logging that can be used to identify failures\n   * where debug level logging would be insufficient, this level of tracing can include\n   * input and output parameters and as such may include PII information passing through\n   * the API. As such it is recommended that this level of tracing should not be enabled\n   * in a production environment.\n   */\n  verbose: DiagLogFunction;\n}\n\n/**\n * Defines the available internal logging levels for the diagnostic logger, the numeric values\n * of the levels are defined to match the original values from the initial LogLevel to avoid\n * compatibility/migration issues for any implementation that assume the numeric ordering.\n */\nexport enum DiagLogLevel {\n  /** Diagnostic Logging level setting to disable all logging (except and forced logs) */\n  NONE = 0,\n\n  /** Identifies an error scenario */\n  ERROR = 30,\n\n  /** Identifies a warning scenario */\n  WARN = 50,\n\n  /** General informational log message */\n  INFO = 60,\n\n  /** General debug log message */\n  DEBUG = 70,\n\n  /**\n   * Detailed trace level logging should only be used for development, should only be set\n   * in a development environment.\n   */\n  VERBOSE = 80,\n\n  /** Used to set the logging level to include all logging */\n  ALL = 9999,\n}\n\n/**\n * Defines options for ComponentLogger\n */\nexport interface ComponentLoggerOptions {\n  namespace: string;\n}\n\nexport interface DiagLoggerOptions {\n  /**\n   * The {@link DiagLogLevel} used to filter logs sent to the logger.\n   *\n   * @defaultValue DiagLogLevel.INFO\n   */\n  logLevel?: DiagLogLevel;\n\n  /**\n   * Setting this value to `true` will suppress the warning message normally emitted when registering a logger when another logger is already registered.\n   */\n  suppressOverrideMessage?: boolean;\n}\n\nexport interface DiagLoggerApi {\n  /**\n   * Set the global DiagLogger and DiagLogLevel.\n   * If a global diag logger is already set, this will override it.\n   *\n   * @param logger - The {@link DiagLogger} instance to set as the default logger.\n   * @param options - A {@link DiagLoggerOptions} object. If not provided, default values will be set.\n   * @returns `true` if the logger was successfully registered, else `false`\n   */\n  setLogger(logger: DiagLogger, options?: DiagLoggerOptions): boolean;\n\n  /**\n   *\n   * @param logger - The {@link DiagLogger} instance to set as the default logger.\n   * @param logLevel - The {@link DiagLogLevel} used to filter logs sent to the logger. If not provided it will default to {@link DiagLogLevel.INFO}.\n   * @returns `true` if the logger was successfully registered, else `false`\n   */\n  setLogger(logger: DiagLogger, logLevel?: DiagLogLevel): boolean;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CA+CH;;;;GAIG;;;AACH,IAAY,YAwBX;AAxBD,CAAA,SAAY,YAAY;IACtB,qFAAA,EAAuF,CACvF,YAAA,CAAA,YAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAQ,CAAA;IAER,iCAAA,EAAmC,CACnC,YAAA,CAAA,YAAA,CAAA,QAAA,GAAA,GAAA,GAAA,OAAU,CAAA;IAEV,kCAAA,EAAoC,CACpC,YAAA,CAAA,YAAA,CAAA,OAAA,GAAA,GAAA,GAAA,MAAS,CAAA;IAET,sCAAA,EAAwC,CACxC,YAAA,CAAA,YAAA,CAAA,OAAA,GAAA,GAAA,GAAA,MAAS,CAAA;IAET,8BAAA,EAAgC,CAChC,YAAA,CAAA,YAAA,CAAA,QAAA,GAAA,GAAA,GAAA,OAAU,CAAA;IAEV;;;OAGG,CACH,YAAA,CAAA,YAAA,CAAA,UAAA,GAAA,GAAA,GAAA,SAAY,CAAA;IAEZ,yDAAA,EAA2D,CAC3D,YAAA,CAAA,YAAA,CAAA,MAAA,GAAA,KAAA,GAAA,KAAU,CAAA;AACZ,CAAC,EAxBW,YAAY,IAAA,CAAZ,YAAY,GAAA,CAAA,CAAA,GAwBvB", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/diag/internal/logLevelLogger.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/diag/internal/logLevelLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DiagLogFunction, DiagLogger, DiagLogLevel } from '../types';\n\nexport function createLogLevelDiagLogger(\n  maxLevel: DiagLogLevel,\n  logger: DiagLogger\n): DiagLogger {\n  if (maxLevel < DiagLogLevel.NONE) {\n    maxLevel = DiagLogLevel.NONE;\n  } else if (maxLevel > DiagLogLevel.ALL) {\n    maxLevel = DiagLogLevel.ALL;\n  }\n\n  // In case the logger is null or undefined\n  logger = logger || {};\n\n  function _filterFunc(\n    funcName: keyof DiagLogger,\n    theLevel: DiagLogLevel\n  ): DiagLogFunction {\n    const theFunc = logger[funcName];\n\n    if (typeof theFunc === 'function' && maxLevel >= theLevel) {\n      return theFunc.bind(logger);\n    }\n    return function () {};\n  }\n\n  return {\n    error: _filterFunc('error', DiagLogLevel.ERROR),\n    warn: _filterFunc('warn', DiagLogLevel.WARN),\n    info: _filterFunc('info', DiagLogLevel.INFO),\n    debug: _filterFunc('debug', DiagLogLevel.DEBUG),\n    verbose: _filterFunc('verbose', DiagLogLevel.VERBOSE),\n  };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAA+B,YAAY,EAAE,MAAM,UAAU,CAAC;;AAE/D,SAAU,wBAAwB,CACtC,QAAsB,EACtB,MAAkB;IAElB,IAAI,QAAQ,8OAAG,eAAY,CAAC,IAAI,EAAE;QAChC,QAAQ,8OAAG,eAAY,CAAC,IAAI,CAAC;KAC9B,MAAM,IAAI,QAAQ,8OAAG,eAAY,CAAC,GAAG,EAAE;QACtC,QAAQ,8OAAG,eAAY,CAAC,GAAG,CAAC;KAC7B;IAED,0CAA0C;IAC1C,MAAM,GAAG,MAAM,IAAI,CAAA,CAAE,CAAC;IAEtB,SAAS,WAAW,CAClB,QAA0B,EAC1B,QAAsB;QAEtB,IAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEjC,IAAI,OAAO,OAAO,KAAK,UAAU,IAAI,QAAQ,IAAI,QAAQ,EAAE;YACzD,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC7B;QACD,OAAO,YAAa,CAAC,CAAC;IACxB,CAAC;IAED,OAAO;QACL,KAAK,EAAE,WAAW,CAAC,OAAO,6OAAE,eAAY,CAAC,KAAK,CAAC;QAC/C,IAAI,EAAE,WAAW,CAAC,MAAM,6OAAE,eAAY,CAAC,IAAI,CAAC;QAC5C,IAAI,EAAE,WAAW,CAAC,MAAM,6OAAE,eAAY,CAAC,IAAI,CAAC;QAC5C,KAAK,EAAE,WAAW,CAAC,OAAO,6OAAE,eAAY,CAAC,KAAK,CAAC;QAC/C,OAAO,EAAE,WAAW,CAAC,SAAS,6OAAE,eAAY,CAAC,OAAO,CAAC;KACtD,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/api/diag.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/api/diag.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DiagComponentLogger } from '../diag/ComponentLogger';\nimport { createLogLevelDiagLogger } from '../diag/internal/logLevelLogger';\nimport {\n  ComponentLoggerOptions,\n  DiagLogFunction,\n  DiagLogger,\n  DiagLoggerApi,\n  DiagLogLevel,\n} from '../diag/types';\nimport {\n  getGlobal,\n  registerGlobal,\n  unregisterGlobal,\n} from '../internal/global-utils';\n\nconst API_NAME = 'diag';\n\n/**\n * Singleton object which represents the entry point to the OpenTelemetry internal\n * diagnostic API\n */\nexport class DiagAPI implements DiagLogger, DiagLoggerApi {\n  private static _instance?: DiagAPI;\n\n  /** Get the singleton instance of the DiagAPI API */\n  public static instance(): DiagAPI {\n    if (!this._instance) {\n      this._instance = new DiagAPI();\n    }\n\n    return this._instance;\n  }\n\n  /**\n   * Private internal constructor\n   * @private\n   */\n  private constructor() {\n    function _logProxy(funcName: keyof DiagLogger): DiagLogFunction {\n      return function (...args) {\n        const logger = getGlobal('diag');\n        // shortcut if logger not set\n        if (!logger) return;\n        return logger[funcName](...args);\n      };\n    }\n\n    // Using self local variable for minification purposes as 'this' cannot be minified\n    const self = this;\n\n    // DiagAPI specific functions\n\n    const setLogger: DiagLoggerApi['setLogger'] = (\n      logger,\n      optionsOrLogLevel = { logLevel: DiagLogLevel.INFO }\n    ) => {\n      if (logger === self) {\n        // There isn't much we can do here.\n        // Logging to the console might break the user application.\n        // Try to log to self. If a logger was previously registered it will receive the log.\n        const err = new Error(\n          'Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation'\n        );\n        self.error(err.stack ?? err.message);\n        return false;\n      }\n\n      if (typeof optionsOrLogLevel === 'number') {\n        optionsOrLogLevel = {\n          logLevel: optionsOrLogLevel,\n        };\n      }\n\n      const oldLogger = getGlobal('diag');\n      const newLogger = createLogLevelDiagLogger(\n        optionsOrLogLevel.logLevel ?? DiagLogLevel.INFO,\n        logger\n      );\n      // There already is an logger registered. We'll let it know before overwriting it.\n      if (oldLogger && !optionsOrLogLevel.suppressOverrideMessage) {\n        const stack = new Error().stack ?? '<failed to generate stacktrace>';\n        oldLogger.warn(`Current logger will be overwritten from ${stack}`);\n        newLogger.warn(\n          `Current logger will overwrite one already registered from ${stack}`\n        );\n      }\n\n      return registerGlobal('diag', newLogger, self, true);\n    };\n\n    self.setLogger = setLogger;\n\n    self.disable = () => {\n      unregisterGlobal(API_NAME, self);\n    };\n\n    self.createComponentLogger = (options: ComponentLoggerOptions) => {\n      return new DiagComponentLogger(options);\n    };\n\n    self.verbose = _logProxy('verbose');\n    self.debug = _logProxy('debug');\n    self.info = _logProxy('info');\n    self.warn = _logProxy('warn');\n    self.error = _logProxy('error');\n  }\n\n  public setLogger!: DiagLoggerApi['setLogger'];\n  /**\n   *\n   */\n  public createComponentLogger!: (\n    options: ComponentLoggerOptions\n  ) => DiagLogger;\n\n  // DiagLogger implementation\n  public verbose!: DiagLogFunction;\n  public debug!: DiagLogFunction;\n  public info!: DiagLogFunction;\n  public warn!: DiagLogFunction;\n  public error!: DiagLogFunction;\n\n  /**\n   * Unregister the global logger and return to Noop\n   */\n  public disable!: () => void;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,mBAAmB,EAAE,MAAM,yBAAyB,CAAC;AAC9D,OAAO,EAAE,wBAAwB,EAAE,MAAM,iCAAiC,CAAC;AAC3E,OAAO,EAKL,YAAY,GACb,MAAM,eAAe,CAAC;AACvB,OAAO,EACL,SAAS,EACT,cAAc,EACd,gBAAgB,GACjB,MAAM,0BAA0B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElC,IAAM,QAAQ,GAAG,MAAM,CAAC;AAExB;;;GAGG,CACH,IAAA,UAAA;IAYE;;;OAGG,CACH,SAAA;QACE,SAAS,SAAS,CAAC,QAA0B;YAC3C,OAAO;gBAAU,IAAA,OAAA,EAAA,CAAO;oBAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;oBAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;gBACtB,IAAM,MAAM,GAAG,yQAAA,AAAS,EAAC,MAAM,CAAC,CAAC;gBACjC,6BAA6B;gBAC7B,IAAI,CAAC,MAAM,EAAE,OAAO;gBACpB,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAA,KAAA,CAAhB,MAAM,EAAA,cAAA,EAAA,EAAA,OAAc,IAAI,GAAA,QAAE;YACnC,CAAC,CAAC;QACJ,CAAC;QAED,mFAAmF;QACnF,IAAM,IAAI,GAAG,IAAI,CAAC;QAElB,6BAA6B;QAE7B,IAAM,SAAS,GAA+B,SAC5C,MAAM,EACN,iBAAmD;;YAAnD,IAAA,sBAAA,KAAA,GAAA;gBAAA,oBAAA;oBAAsB,QAAQ,6OAAE,eAAY,CAAC,IAAI;gBAAA,CAAE;YAAA;YAEnD,IAAI,MAAM,KAAK,IAAI,EAAE;gBACnB,mCAAmC;gBACnC,2DAA2D;gBAC3D,qFAAqF;gBACrF,IAAM,GAAG,GAAG,IAAI,KAAK,CACnB,oIAAoI,CACrI,CAAC;gBACF,IAAI,CAAC,KAAK,CAAC,CAAA,KAAA,GAAG,CAAC,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,GAAG,CAAC,OAAO,CAAC,CAAC;gBACrC,OAAO,KAAK,CAAC;aACd;YAED,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE;gBACzC,iBAAiB,GAAG;oBAClB,QAAQ,EAAE,iBAAiB;iBAC5B,CAAC;aACH;YAED,IAAM,SAAS,gQAAG,YAAA,AAAS,EAAC,MAAM,CAAC,CAAC;YACpC,IAAM,SAAS,IAAG,8RAAA,AAAwB,EACxC,CAAA,KAAA,iBAAiB,CAAC,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,gPAAI,eAAY,CAAC,IAAI,EAC/C,MAAM,CACP,CAAC;YACF,kFAAkF;YAClF,IAAI,SAAS,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,EAAE;gBAC3D,IAAM,KAAK,GAAG,CAAA,KAAA,IAAI,KAAK,EAAE,CAAC,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,iCAAiC,CAAC;gBACrE,SAAS,CAAC,IAAI,CAAC,6CAA2C,KAAO,CAAC,CAAC;gBACnE,SAAS,CAAC,IAAI,CACZ,+DAA6D,KAAO,CACrE,CAAC;aACH;YAED,QAAO,6QAAA,AAAc,EAAC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,CAAC,OAAO,GAAG;yQACb,mBAAA,AAAgB,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACnC,CAAC,CAAC;QAEF,IAAI,CAAC,qBAAqB,GAAG,SAAC,OAA+B;YAC3D,OAAO,yPAAI,sBAAmB,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;IAClC,CAAC;IAjFD,kDAAA,EAAoD,CACtC,QAAA,QAAQ,GAAtB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,EAAE,CAAC;SAChC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IA+FH,OAAA,OAAC;AAAD,CAAC,AAzGD,IAyGC", "debugId": null}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/api/context.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/api/context.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NoopContextManager } from '../context/NoopContextManager';\nimport { Context, ContextManager } from '../context/types';\nimport {\n  getGlobal,\n  registerGlobal,\n  unregisterGlobal,\n} from '../internal/global-utils';\nimport { DiagAPI } from './diag';\n\nconst API_NAME = 'context';\nconst NOOP_CONTEXT_MANAGER = new NoopContextManager();\n\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Context API\n */\nexport class ContextAPI {\n  private static _instance?: ContextAPI;\n\n  /** Empty private constructor prevents end users from constructing a new instance of the API */\n  private constructor() {}\n\n  /** Get the singleton instance of the Context API */\n  public static getInstance(): ContextAPI {\n    if (!this._instance) {\n      this._instance = new ContextAPI();\n    }\n\n    return this._instance;\n  }\n\n  /**\n   * Set the current context manager.\n   *\n   * @returns true if the context manager was successfully registered, else false\n   */\n  public setGlobalContextManager(contextManager: ContextManager): boolean {\n    return registerGlobal(API_NAME, contextManager, DiagAPI.instance());\n  }\n\n  /**\n   * Get the currently active context\n   */\n  public active(): Context {\n    return this._getContextManager().active();\n  }\n\n  /**\n   * Execute a function with an active context\n   *\n   * @param context context to be active during function execution\n   * @param fn function to execute in a context\n   * @param thisArg optional receiver to be used for calling fn\n   * @param args optional arguments forwarded to fn\n   */\n  public with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n    context: Context,\n    fn: F,\n    thisArg?: ThisParameterType<F>,\n    ...args: A\n  ): ReturnType<F> {\n    return this._getContextManager().with(context, fn, thisArg, ...args);\n  }\n\n  /**\n   * Bind a context to a target function or event emitter\n   *\n   * @param context context to bind to the event emitter or function. Defaults to the currently active context\n   * @param target function or event emitter to bind\n   */\n  public bind<T>(context: Context, target: T): T {\n    return this._getContextManager().bind(context, target);\n  }\n\n  private _getContextManager(): ContextManager {\n    return getGlobal(API_NAME) || NOOP_CONTEXT_MANAGER;\n  }\n\n  /** Disable and remove the global context manager */\n  public disable() {\n    this._getContextManager().disable();\n    unregisterGlobal(API_NAME, DiagAPI.instance());\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,kBAAkB,EAAE,MAAM,+BAA+B,CAAC;AAEnE,OAAO,EACL,SAAS,EACT,cAAc,EACd,gBAAgB,GACjB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjC,IAAM,QAAQ,GAAG,SAAS,CAAC;AAC3B,IAAM,oBAAoB,GAAG,+PAAI,qBAAkB,EAAE,CAAC;AAEtD;;GAEG,CACH,IAAA,aAAA;IAGE,6FAAA,EAA+F,CAC/F,SAAA,cAAuB,CAAC;IAExB,kDAAA,EAAoD,CACtC,WAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,EAAE,CAAC;SACnC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;;OAIG,CACI,WAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,cAA8B;QAC3D,oQAAO,iBAAA,AAAc,EAAC,QAAQ,EAAE,cAAc,2OAAE,UAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG,CACI,WAAA,SAAA,CAAA,MAAM,GAAb;QACE,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,CAAC;IAC5C,CAAC;IAED;;;;;;;OAOG,CACI,WAAA,SAAA,CAAA,IAAI,GAAX,SACE,OAAgB,EAChB,EAAK,EACL,OAA8B;;QAC9B,IAAA,OAAA,EAAA,CAAU;YAAV,IAAA,KAAA,CAAU,EAAV,KAAA,UAAA,MAAU,EAAV,IAAU,CAAA;YAAV,IAAA,CAAA,KAAA,EAAA,GAAA,SAAA,CAAA,GAAA,CAAU;;QAEV,OAAO,CAAA,KAAA,IAAI,CAAC,kBAAkB,EAAE,CAAA,CAAC,IAAI,CAAA,KAAA,CAAA,IAAA,cAAA;YAAC,OAAO;YAAE,EAAE;YAAE,OAAO;SAAA,EAAA,OAAK,IAAI,GAAA,QAAE;IACvE,CAAC;IAED;;;;;OAKG,CACI,WAAA,SAAA,CAAA,IAAI,GAAX,SAAe,OAAgB,EAAE,MAAS;QACxC,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACzD,CAAC;IAEO,WAAA,SAAA,CAAA,kBAAkB,GAA1B;QACE,oQAAO,YAAA,AAAS,EAAC,QAAQ,CAAC,IAAI,oBAAoB,CAAC;IACrD,CAAC;IAED,kDAAA,EAAoD,CAC7C,WAAA,SAAA,CAAA,OAAO,GAAd;QACE,IAAI,CAAC,kBAAkB,EAAE,CAAC,OAAO,EAAE,CAAC;qQACpC,mBAAA,AAAgB,EAAC,QAAQ,2OAAE,UAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjD,CAAC;IACH,OAAA,UAAC;AAAD,CAAC,AAnED,IAmEC", "debugId": null}}, {"offset": {"line": 799, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/context-api.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/context-api.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { ContextAPI } from './api/context';\n/** Entrypoint for context API */\nexport const context = ContextAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,sEAAsE;AACtE,qCAAqC;;;;AACrC,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;;AAEpC,IAAM,OAAO,+OAAG,aAAU,CAAC,WAAW,EAAE,CAAC", "debugId": null}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/diag-api.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/diag-api.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { DiagAPI } from './api/diag';\n/**\n * Entrypoint for Diag API.\n * Defines Diagnostic handler used for internal diagnostic logging operations.\n * The default provides a Noop DiagLogger implementation which may be changed via the\n * diag.setLogger(logger: DiagLogger) function.\n */\nexport const diag = DiagAPI.instance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,sEAAsE;AACtE,qCAAqC;;;;AACrC,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;;AAO9B,IAAM,IAAI,4OAAG,UAAO,CAAC,QAAQ,EAAE,CAAC", "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/metrics/NoopMeter.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/metrics/NoopMeter.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Meter } from './Meter';\nimport {\n  BatchObservableCallback,\n  Counter,\n  Gauge,\n  Histogram,\n  MetricAttributes,\n  MetricOptions,\n  Observable,\n  ObservableCallback,\n  ObservableCounter,\n  ObservableGauge,\n  ObservableUpDownCounter,\n  UpDownCounter,\n} from './Metric';\n\n/**\n * NoopMeter is a noop implementation of the {@link Meter} interface. It reuses\n * constant NoopMetrics for all of its methods.\n */\nexport class NoopMeter implements Meter {\n  constructor() {}\n\n  /**\n   * @see {@link Meter.createGauge}\n   */\n  createGauge(_name: string, _options?: MetricOptions): Gauge {\n    return NOOP_GAUGE_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.createHistogram}\n   */\n  createHistogram(_name: string, _options?: MetricOptions): Histogram {\n    return NOOP_HISTOGRAM_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.createCounter}\n   */\n  createCounter(_name: string, _options?: MetricOptions): Counter {\n    return NOOP_COUNTER_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.createUpDownCounter}\n   */\n  createUpDownCounter(_name: string, _options?: MetricOptions): UpDownCounter {\n    return NOOP_UP_DOWN_COUNTER_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.createObservableGauge}\n   */\n  createObservableGauge(\n    _name: string,\n    _options?: MetricOptions\n  ): ObservableGauge {\n    return NOOP_OBSERVABLE_GAUGE_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.createObservableCounter}\n   */\n  createObservableCounter(\n    _name: string,\n    _options?: MetricOptions\n  ): ObservableCounter {\n    return NOOP_OBSERVABLE_COUNTER_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.createObservableUpDownCounter}\n   */\n  createObservableUpDownCounter(\n    _name: string,\n    _options?: MetricOptions\n  ): ObservableUpDownCounter {\n    return NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC;\n  }\n\n  /**\n   * @see {@link Meter.addBatchObservableCallback}\n   */\n  addBatchObservableCallback(\n    _callback: BatchObservableCallback,\n    _observables: Observable[]\n  ): void {}\n\n  /**\n   * @see {@link Meter.removeBatchObservableCallback}\n   */\n  removeBatchObservableCallback(_callback: BatchObservableCallback): void {}\n}\n\nexport class NoopMetric {}\n\nexport class NoopCounterMetric extends NoopMetric implements Counter {\n  add(_value: number, _attributes: MetricAttributes): void {}\n}\n\nexport class NoopUpDownCounterMetric\n  extends NoopMetric\n  implements UpDownCounter\n{\n  add(_value: number, _attributes: MetricAttributes): void {}\n}\n\nexport class NoopGaugeMetric extends NoopMetric implements Gauge {\n  record(_value: number, _attributes: MetricAttributes): void {}\n}\n\nexport class NoopHistogramMetric extends NoopMetric implements Histogram {\n  record(_value: number, _attributes: MetricAttributes): void {}\n}\n\nexport class NoopObservableMetric {\n  addCallback(_callback: ObservableCallback) {}\n\n  removeCallback(_callback: ObservableCallback) {}\n}\n\nexport class NoopObservableCounterMetric\n  extends NoopObservableMetric\n  implements ObservableCounter {}\n\nexport class NoopObservableGaugeMetric\n  extends NoopObservableMetric\n  implements ObservableGauge {}\n\nexport class NoopObservableUpDownCounterMetric\n  extends NoopObservableMetric\n  implements ObservableUpDownCounter {}\n\nexport const NOOP_METER = new NoopMeter();\n\n// Synchronous instruments\nexport const NOOP_COUNTER_METRIC = new NoopCounterMetric();\nexport const NOOP_GAUGE_METRIC = new NoopGaugeMetric();\nexport const NOOP_HISTOGRAM_METRIC = new NoopHistogramMetric();\nexport const NOOP_UP_DOWN_COUNTER_METRIC = new NoopUpDownCounterMetric();\n\n// Asynchronous instruments\nexport const NOOP_OBSERVABLE_COUNTER_METRIC = new NoopObservableCounterMetric();\nexport const NOOP_OBSERVABLE_GAUGE_METRIC = new NoopObservableGaugeMetric();\nexport const NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC =\n  new NoopObservableUpDownCounterMetric();\n\n/**\n * Create a no-op Meter\n */\nexport function createNoopMeter(): Meter {\n  return NOOP_METER;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBH;;;GAGG,CACH,IAAA,YAAA;IACE,SAAA,aAAe,CAAC;IAEhB;;OAEG,CACH,UAAA,SAAA,CAAA,WAAW,GAAX,SAAY,KAAa,EAAE,QAAwB;QACjD,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,UAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,KAAa,EAAE,QAAwB;QACrD,OAAO,qBAAqB,CAAC;IAC/B,CAAC;IAED;;OAEG,CACH,UAAA,SAAA,CAAA,aAAa,GAAb,SAAc,KAAa,EAAE,QAAwB;QACnD,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,UAAA,SAAA,CAAA,mBAAmB,GAAnB,SAAoB,KAAa,EAAE,QAAwB;QACzD,OAAO,2BAA2B,CAAC;IACrC,CAAC;IAED;;OAEG,CACH,UAAA,SAAA,CAAA,qBAAqB,GAArB,SACE,KAAa,EACb,QAAwB;QAExB,OAAO,4BAA4B,CAAC;IACtC,CAAC;IAED;;OAEG,CACH,UAAA,SAAA,CAAA,uBAAuB,GAAvB,SACE,KAAa,EACb,QAAwB;QAExB,OAAO,8BAA8B,CAAC;IACxC,CAAC;IAED;;OAEG,CACH,UAAA,SAAA,CAAA,6BAA6B,GAA7B,SACE,KAAa,EACb,QAAwB;QAExB,OAAO,sCAAsC,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,UAAA,SAAA,CAAA,0BAA0B,GAA1B,SACE,SAAkC,EAClC,YAA0B,GACnB,CAAC;IAEV;;OAEG,CACH,UAAA,SAAA,CAAA,6BAA6B,GAA7B,SAA8B,SAAkC,GAAS,CAAC;IAC5E,OAAA,SAAC;AAAD,CAAC,AAzED,IAyEC;;AAED,IAAA,aAAA;IAAA,SAAA,cAAyB,CAAC;IAAD,OAAA,UAAC;AAAD,CAAC,AAA1B,IAA0B;;AAE1B,IAAA,oBAAA,SAAA,MAAA;IAAuC,UAAA,mBAAA,QAAU;IAAjD,SAAA;;IAEA,CAAC;IADC,kBAAA,SAAA,CAAA,GAAG,GAAH,SAAI,MAAc,EAAE,WAA6B,GAAS,CAAC;IAC7D,OAAA,iBAAC;AAAD,CAAC,AAFD,CAAuC,UAAU,GAEhD;;AAED,IAAA,0BAAA,SAAA,MAAA;IACU,UAAA,yBAAA,QAAU;IADpB,SAAA;;IAKA,CAAC;IADC,wBAAA,SAAA,CAAA,GAAG,GAAH,SAAI,MAAc,EAAE,WAA6B,GAAS,CAAC;IAC7D,OAAA,uBAAC;AAAD,CAAC,AALD,CACU,UAAU,GAInB;;AAED,IAAA,kBAAA,SAAA,MAAA;IAAqC,UAAA,iBAAA,QAAU;IAA/C,SAAA;;IAEA,CAAC;IADC,gBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,MAAc,EAAE,WAA6B,GAAS,CAAC;IAChE,OAAA,eAAC;AAAD,CAAC,AAFD,CAAqC,UAAU,GAE9C;;AAED,IAAA,sBAAA,SAAA,MAAA;IAAyC,UAAA,qBAAA,QAAU;IAAnD,SAAA;;IAEA,CAAC;IADC,oBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,MAAc,EAAE,WAA6B,GAAS,CAAC;IAChE,OAAA,mBAAC;AAAD,CAAC,AAFD,CAAyC,UAAU,GAElD;;AAED,IAAA,uBAAA;IAAA,SAAA,wBAIA,CAAC;IAHC,qBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,SAA6B,GAAG,CAAC;IAE7C,qBAAA,SAAA,CAAA,cAAc,GAAd,SAAe,SAA6B,GAAG,CAAC;IAClD,OAAA,oBAAC;AAAD,CAAC,AAJD,IAIC;;AAED,IAAA,8BAAA,SAAA,MAAA;IACU,UAAA,6BAAA,QAAoB;IAD9B,SAAA;;IAEgC,CAAC;IAAD,OAAA,2BAAC;AAAD,CAAC,AAFjC,CACU,oBAAoB,GACG;;AAEjC,IAAA,4BAAA,SAAA,MAAA;IACU,UAAA,2BAAA,QAAoB;IAD9B,SAAA;;IAE8B,CAAC;IAAD,OAAA,yBAAC;AAAD,CAAC,AAF/B,CACU,oBAAoB,GACC;;AAE/B,IAAA,oCAAA,SAAA,MAAA;IACU,UAAA,mCAAA,QAAoB;IAD9B,SAAA;;IAEsC,CAAC;IAAD,OAAA,iCAAC;AAAD,CAAC,AAFvC,CACU,oBAAoB,GACS;;AAEhC,IAAM,UAAU,GAAG,IAAI,SAAS,EAAE,CAAC;AAGnC,IAAM,mBAAmB,GAAG,IAAI,iBAAiB,EAAE,CAAC;AACpD,IAAM,iBAAiB,GAAG,IAAI,eAAe,EAAE,CAAC;AAChD,IAAM,qBAAqB,GAAG,IAAI,mBAAmB,EAAE,CAAC;AACxD,IAAM,2BAA2B,GAAG,IAAI,uBAAuB,EAAE,CAAC;AAGlE,IAAM,8BAA8B,GAAG,IAAI,2BAA2B,EAAE,CAAC;AACzE,IAAM,4BAA4B,GAAG,IAAI,yBAAyB,EAAE,CAAC;AACrE,IAAM,sCAAsC,GACjD,IAAI,iCAAiC,EAAE,CAAC;AAKpC,SAAU,eAAe;IAC7B,OAAO,UAAU,CAAC;AACpB,CAAC", "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/metrics/NoopMeterProvider.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/metrics/NoopMeterProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Meter, MeterOptions } from './Meter';\nimport { MeterProvider } from './MeterProvider';\nimport { NOOP_METER } from './NoopMeter';\n\n/**\n * An implementation of the {@link MeterProvider} which returns an impotent Meter\n * for all calls to `getMeter`\n */\nexport class NoopMeterProvider implements MeterProvider {\n  getMeter(_name: string, _version?: string, _options?: MeterOptions): Meter {\n    return NOOP_METER;\n  }\n}\n\nexport const NOOP_METER_PROVIDER = new NoopMeterProvider();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAIH,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;;AAEzC;;;GAGG,CACH,IAAA,oBAAA;IAAA,SAAA,qBAIA,CAAC;IAHC,kBAAA,SAAA,CAAA,QAAQ,GAAR,SAAS,KAAa,EAAE,QAAiB,EAAE,QAAuB;QAChE,yPAAO,aAAU,CAAC;IACpB,CAAC;IACH,OAAA,iBAAC;AAAD,CAAC,AAJD,IAIC;;AAEM,IAAM,mBAAmB,GAAG,IAAI,iBAAiB,EAAE,CAAC", "debugId": null}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/api/metrics.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/api/metrics.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Meter, MeterOptions } from '../metrics/Meter';\nimport { MeterProvider } from '../metrics/MeterProvider';\nimport { NOOP_METER_PROVIDER } from '../metrics/NoopMeterProvider';\nimport {\n  getGlobal,\n  registerGlobal,\n  unregisterGlobal,\n} from '../internal/global-utils';\nimport { DiagAPI } from './diag';\n\nconst API_NAME = 'metrics';\n\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Metrics API\n */\nexport class MetricsAPI {\n  private static _instance?: MetricsAPI;\n\n  /** Empty private constructor prevents end users from constructing a new instance of the API */\n  private constructor() {}\n\n  /** Get the singleton instance of the Metrics API */\n  public static getInstance(): MetricsAPI {\n    if (!this._instance) {\n      this._instance = new MetricsAPI();\n    }\n\n    return this._instance;\n  }\n\n  /**\n   * Set the current global meter provider.\n   * Returns true if the meter provider was successfully registered, else false.\n   */\n  public setGlobalMeterProvider(provider: MeterProvider): boolean {\n    return registerGlobal(API_NAME, provider, DiagAPI.instance());\n  }\n\n  /**\n   * Returns the global meter provider.\n   */\n  public getMeterProvider(): MeterProvider {\n    return getGlobal(API_NAME) || NOOP_METER_PROVIDER;\n  }\n\n  /**\n   * Returns a meter from the global meter provider.\n   */\n  public getMeter(\n    name: string,\n    version?: string,\n    options?: MeterOptions\n  ): Meter {\n    return this.getMeterProvider().getMeter(name, version, options);\n  }\n\n  /** Remove the global meter provider */\n  public disable(): void {\n    unregisterGlobal(API_NAME, DiagAPI.instance());\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EACL,SAAS,EACT,cAAc,EACd,gBAAgB,GACjB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;;;;AAEjC,IAAM,QAAQ,GAAG,SAAS,CAAC;AAE3B;;GAEG,CACH,IAAA,aAAA;IAGE,6FAAA,EAA+F,CAC/F,SAAA,cAAuB,CAAC;IAExB,kDAAA,EAAoD,CACtC,WAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,EAAE,CAAC;SACnC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;OAGG,CACI,WAAA,SAAA,CAAA,sBAAsB,GAA7B,SAA8B,QAAuB;QACnD,oQAAO,iBAAA,AAAc,EAAC,QAAQ,EAAE,QAAQ,EAAE,mPAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG,CACI,WAAA,SAAA,CAAA,gBAAgB,GAAvB;QACE,QAAO,wQAAA,AAAS,EAAC,QAAQ,CAAC,8PAAI,sBAAmB,CAAC;IACpD,CAAC;IAED;;OAEG,CACI,WAAA,SAAA,CAAA,QAAQ,GAAf,SACE,IAAY,EACZ,OAAgB,EAChB,OAAsB;QAEtB,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAClE,CAAC;IAED,qCAAA,EAAuC,CAChC,WAAA,SAAA,CAAA,OAAO,GAAd;qQACE,mBAAA,AAAgB,EAAC,QAAQ,2OAAE,UAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjD,CAAC;IACH,OAAA,UAAC;AAAD,CAAC,AA7CD,IA6CC", "debugId": null}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/metrics-api.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/metrics-api.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { MetricsAPI } from './api/metrics';\n/** Entrypoint for metrics API */\nexport const metrics = MetricsAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,sEAAsE;AACtE,qCAAqC;;;;AACrC,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;;AAEpC,IAAM,OAAO,+OAAG,aAAU,CAAC,WAAW,EAAE,CAAC", "debugId": null}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/propagation/NoopTextMapPropagator.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/propagation/NoopTextMapPropagator.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '../context/types';\nimport { TextMapPropagator } from './TextMapPropagator';\n\n/**\n * No-op implementations of {@link TextMapPropagator}.\n */\nexport class NoopTextMapPropagator implements TextMapPropagator {\n  /** Noop inject function does nothing */\n  inject(_context: Context, _carrier: unknown): void {}\n  /** Noop extract function does nothing and returns the input context */\n  extract(context: Context, _carrier: unknown): Context {\n    return context;\n  }\n  fields(): string[] {\n    return [];\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAKH;;GAEG;;;AACH,IAAA,wBAAA;IAAA,SAAA,yBAUA,CAAC;IATC,sCAAA,EAAwC,CACxC,sBAAA,SAAA,CAAA,MAAM,GAAN,SAAO,QAAiB,EAAE,QAAiB,GAAS,CAAC;IACrD,qEAAA,EAAuE,CACvE,sBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,OAAgB,EAAE,QAAiB;QACzC,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,sBAAA,SAAA,CAAA,MAAM,GAAN;QACE,OAAO,EAAE,CAAC;IACZ,CAAC;IACH,OAAA,qBAAC;AAAD,CAAC,AAVD,IAUC", "debugId": null}}, {"offset": {"line": 1201, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/propagation/TextMapPropagator.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/propagation/TextMapPropagator.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '../context/types';\n\n/**\n * Injects `Context` into and extracts it from carriers that travel\n * in-band across process boundaries. Encoding is expected to conform to the\n * HTTP Header Field semantics. Values are often encoded as RPC/HTTP request\n * headers.\n *\n * The carrier of propagated data on both the client (injector) and server\n * (extractor) side is usually an object such as http headers. Propagation is\n * usually implemented via library-specific request interceptors, where the\n * client-side injects values and the server-side extracts them.\n */\nexport interface TextMapPropagator<Carrier = any> {\n  /**\n   * Injects values from a given `Context` into a carrier.\n   *\n   * OpenTelemetry defines a common set of format values (TextMapPropagator),\n   * and each has an expected `carrier` type.\n   *\n   * @param context the Context from which to extract values to transmit over\n   *     the wire.\n   * @param carrier the carrier of propagation fields, such as http request\n   *     headers.\n   * @param setter an optional {@link TextMapSetter}. If undefined, values will be\n   *     set by direct object assignment.\n   */\n  inject(\n    context: Context,\n    carrier: Carrier,\n    setter: TextMapSetter<Carrier>\n  ): void;\n\n  /**\n   * Given a `Context` and a carrier, extract context values from a\n   * carrier and return a new context, created from the old context, with the\n   * extracted values.\n   *\n   * @param context the Context from which to extract values to transmit over\n   *     the wire.\n   * @param carrier the carrier of propagation fields, such as http request\n   *     headers.\n   * @param getter an optional {@link TextMapGetter}. If undefined, keys will be all\n   *     own properties, and keys will be accessed by direct object access.\n   */\n  extract(\n    context: Context,\n    carrier: Carrier,\n    getter: TextMapGetter<Carrier>\n  ): Context;\n\n  /**\n   * Return a list of all fields which may be used by the propagator.\n   */\n  fields(): string[];\n}\n\n/**\n * A setter is specified by the caller to define a specific method\n * to set key/value pairs on the carrier within a propagator.\n */\nexport interface TextMapSetter<Carrier = any> {\n  /**\n   * Callback used to set a key/value pair on an object.\n   *\n   * Should be called by the propagator each time a key/value pair\n   * should be set, and should set that key/value pair on the propagator.\n   *\n   * @param carrier object or class which carries key/value pairs\n   * @param key string key to modify\n   * @param value value to be set to the key on the carrier\n   */\n  set(carrier: Carrier, key: string, value: string): void;\n}\n\n/**\n * A getter is specified by the caller to define a specific method\n * to get the value of a key from a carrier.\n */\nexport interface TextMapGetter<Carrier = any> {\n  /**\n   * Get a list of all keys available on the carrier.\n   *\n   * @param carrier\n   */\n  keys(carrier: Carrier): string[];\n\n  /**\n   * Get the value of a specific key from the carrier.\n   *\n   * @param carrier\n   * @param key\n   */\n  get(carrier: Carrier, key: string): undefined | string | string[];\n}\n\nexport const defaultTextMapGetter: TextMapGetter = {\n  get(carrier, key) {\n    if (carrier == null) {\n      return undefined;\n    }\n    return carrier[key];\n  },\n\n  keys(carrier) {\n    if (carrier == null) {\n      return [];\n    }\n    return Object.keys(carrier);\n  },\n};\n\nexport const defaultTextMapSetter: TextMapSetter = {\n  set(carrier, key, value) {\n    if (carrier == null) {\n      return;\n    }\n\n    carrier[key] = value;\n  },\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAkGI,IAAM,oBAAoB,GAAkB;IACjD,GAAG,EAAA,SAAC,OAAO,EAAE,GAAG;QACd,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,OAAO,SAAS,CAAC;SAClB;QACD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC;IAED,IAAI,EAAA,SAAC,OAAO;QACV,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,OAAO,EAAE,CAAC;SACX;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;CACF,CAAC;AAEK,IAAM,oBAAoB,GAAkB;IACjD,GAAG,EAAA,SAAC,OAAO,EAAE,GAAG,EAAE,KAAK;QACrB,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,OAAO;SACR;QAED,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACvB,CAAC;CACF,CAAC", "debugId": null}}, {"offset": {"line": 1245, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/baggage/context-helpers.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/baggage/context-helpers.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ContextAPI } from '../api/context';\nimport { createContextKey } from '../context/context';\nimport { Context } from '../context/types';\nimport { Baggage } from './types';\n\n/**\n * Baggage key\n */\nconst BAGGAGE_KEY = createContextKey('OpenTelemetry Baggage Key');\n\n/**\n * Retrieve the current baggage from the given context\n *\n * @param {Context} Context that manage all context values\n * @returns {Baggage} Extracted baggage from the context\n */\nexport function getBaggage(context: Context): Baggage | undefined {\n  return (context.getValue(BAGGAGE_KEY) as Baggage) || undefined;\n}\n\n/**\n * Retrieve the current baggage from the active/current context\n *\n * @returns {Baggage} Extracted baggage from the context\n */\nexport function getActiveBaggage(): Baggage | undefined {\n  return getBaggage(ContextAPI.getInstance().active());\n}\n\n/**\n * Store a baggage in the given context\n *\n * @param {Context} Context that manage all context values\n * @param {Baggage} baggage that will be set in the actual context\n */\nexport function setBaggage(context: Context, baggage: Baggage): Context {\n  return context.setValue(BAGGAGE_KEY, baggage);\n}\n\n/**\n * Delete the baggage stored in the given context\n *\n * @param {Context} Context that manage all context values\n */\nexport function deleteBaggage(context: Context): Context {\n  return context.deleteValue(BAGGAGE_KEY);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AAEH,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAC5C,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;;;AAItD;;GAEG,CACH,IAAM,WAAW,uPAAG,mBAAA,AAAgB,EAAC,2BAA2B,CAAC,CAAC;AAQ5D,SAAU,UAAU,CAAC,OAAgB;IACzC,OAAQ,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAa,IAAI,SAAS,CAAC;AACjE,CAAC;AAOK,SAAU,gBAAgB;IAC9B,OAAO,UAAU,6OAAC,aAAU,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;AACvD,CAAC;AAQK,SAAU,UAAU,CAAC,OAAgB,EAAE,OAAgB;IAC3D,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAChD,CAAC;AAOK,SAAU,aAAa,CAAC,OAAgB;IAC5C,OAAO,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AAC1C,CAAC", "debugId": null}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/baggage/internal/baggage-impl.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/baggage/internal/baggage-impl.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Baggage, BaggageEntry } from '../types';\n\nexport class BaggageImpl implements Baggage {\n  private _entries: Map<string, BaggageEntry>;\n\n  constructor(entries?: Map<string, BaggageEntry>) {\n    this._entries = entries ? new Map(entries) : new Map();\n  }\n\n  getEntry(key: string): BaggageEntry | undefined {\n    const entry = this._entries.get(key);\n    if (!entry) {\n      return undefined;\n    }\n\n    return Object.assign({}, entry);\n  }\n\n  getAllEntries(): [string, BaggageEntry][] {\n    return Array.from(this._entries.entries()).map(([k, v]) => [k, v]);\n  }\n\n  setEntry(key: string, entry: BaggageEntry): BaggageImpl {\n    const newBaggage = new BaggageImpl(this._entries);\n    newBaggage._entries.set(key, entry);\n    return newBaggage;\n  }\n\n  removeEntry(key: string): BaggageImpl {\n    const newBaggage = new BaggageImpl(this._entries);\n    newBaggage._entries.delete(key);\n    return newBaggage;\n  }\n\n  removeEntries(...keys: string[]): BaggageImpl {\n    const newBaggage = new BaggageImpl(this._entries);\n    for (const key of keys) {\n      newBaggage._entries.delete(key);\n    }\n    return newBaggage;\n  }\n\n  clear(): BaggageImpl {\n    return new BaggageImpl();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,IAAA,cAAA;IAGE,SAAA,YAAY,OAAmC;QAC7C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;IACzD,CAAC;IAED,YAAA,SAAA,CAAA,QAAQ,GAAR,SAAS,GAAW;QAClB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,SAAS,CAAC;SAClB;QAED,OAAO,MAAM,CAAC,MAAM,CAAC,CAAA,CAAE,EAAE,KAAK,CAAC,CAAC;IAClC,CAAC;IAED,YAAA,SAAA,CAAA,aAAa,GAAb;QACE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,SAAC,EAAM;gBAAN,KAAA,OAAA,IAAA,EAAM,EAAL,CAAC,GAAA,EAAA,CAAA,EAAA,EAAE,CAAC,GAAA,EAAA,CAAA,EAAA;YAAM,OAAA;gBAAC,CAAC;gBAAE,CAAC;aAAC;QAAN,CAAM,CAAC,CAAC;IACrE,CAAC;IAED,YAAA,SAAA,CAAA,QAAQ,GAAR,SAAS,GAAW,EAAE,KAAmB;QACvC,IAAM,UAAU,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClD,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACpC,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,YAAA,SAAA,CAAA,WAAW,GAAX,SAAY,GAAW;QACrB,IAAM,UAAU,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClD,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAChC,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,YAAA,SAAA,CAAA,aAAa,GAAb;;QAAc,IAAA,OAAA,EAAA,CAAiB;YAAjB,IAAA,KAAA,CAAiB,EAAjB,KAAA,UAAA,MAAiB,EAAjB,IAAiB,CAAA;YAAjB,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAiB;;QAC7B,IAAM,UAAU,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;YAClD,IAAkB,IAAA,SAAA,SAAA,IAAI,CAAA,EAAA,WAAA,OAAA,IAAA,EAAA,EAAA,CAAA,SAAA,IAAA,EAAA,WAAA,OAAA,IAAA,GAAE;gBAAnB,IAAM,GAAG,GAAA,SAAA,KAAA;gBACZ,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;aACjC;;;;;;;;;;;;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,YAAA,SAAA,CAAA,KAAK,GAAL;QACE,OAAO,IAAI,WAAW,EAAE,CAAC;IAC3B,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AA3CD,IA2CC", "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/baggage/internal/symbol.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/baggage/internal/symbol.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Symbol used to make BaggageEntryMetadata an opaque type\n */\nexport const baggageEntryMetadataSymbol = Symbol('BaggageEntryMetadata');\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH;;GAEG;;;AACI,IAAM,0BAA0B,GAAG,MAAM,CAAC,sBAAsB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1428, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/baggage/utils.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/baggage/utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DiagAPI } from '../api/diag';\nimport { BaggageImpl } from './internal/baggage-impl';\nimport { baggageEntryMetadataSymbol } from './internal/symbol';\nimport { Baggage, BaggageEntry, BaggageEntryMetadata } from './types';\n\nconst diag = DiagAPI.instance();\n\n/**\n * Create a new Baggage with optional entries\n *\n * @param entries An array of baggage entries the new baggage should contain\n */\nexport function createBaggage(\n  entries: Record<string, BaggageEntry> = {}\n): Baggage {\n  return new BaggageImpl(new Map(Object.entries(entries)));\n}\n\n/**\n * Create a serializable BaggageEntryMetadata object from a string.\n *\n * @param str string metadata. Format is currently not defined by the spec and has no special meaning.\n *\n */\nexport function baggageEntryMetadataFromString(\n  str: string\n): BaggageEntryMetadata {\n  if (typeof str !== 'string') {\n    diag.error(\n      `Cannot create baggage metadata from unknown type: ${typeof str}`\n    );\n    str = '';\n  }\n\n  return {\n    __TYPE__: baggageEntryMetadataSymbol,\n    toString() {\n      return str;\n    },\n  };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,EAAE,OAAO,EAAE,MAAM,aAAa,CAAC;AACtC,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AACtD,OAAO,EAAE,0BAA0B,EAAE,MAAM,mBAAmB,CAAC;;;;AAG/D,IAAM,IAAI,4OAAG,UAAO,CAAC,QAAQ,EAAE,CAAC;AAO1B,SAAU,aAAa,CAC3B,OAA0C;IAA1C,IAAA,YAAA,KAAA,GAAA;QAAA,UAAA,CAAA,CAA0C;IAAA;IAE1C,OAAO,wQAAI,cAAW,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC;AAQK,SAAU,8BAA8B,CAC5C,GAAW;IAEX,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,IAAI,CAAC,KAAK,CACR,uDAAqD,OAAO,GAAK,CAClE,CAAC;QACF,GAAG,GAAG,EAAE,CAAC;KACV;IAED,OAAO;QACL,QAAQ,6PAAE,6BAA0B;QACpC,QAAQ,EAAA;YACN,OAAO,GAAG,CAAC;QACb,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 1475, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/api/propagation.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/api/propagation.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '../context/types';\nimport {\n  getGlobal,\n  registerGlobal,\n  unregisterGlobal,\n} from '../internal/global-utils';\nimport { NoopTextMapPropagator } from '../propagation/NoopTextMapPropagator';\nimport {\n  defaultTextMapGetter,\n  defaultTextMapSetter,\n  TextMapGetter,\n  TextMapPropagator,\n  TextMapSetter,\n} from '../propagation/TextMapPropagator';\nimport {\n  getBaggage,\n  getActiveBaggage,\n  setBaggage,\n  deleteBaggage,\n} from '../baggage/context-helpers';\nimport { createBaggage } from '../baggage/utils';\nimport { DiagAPI } from './diag';\n\nconst API_NAME = 'propagation';\nconst NOOP_TEXT_MAP_PROPAGATOR = new NoopTextMapPropagator();\n\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Propagation API\n */\nexport class PropagationAPI {\n  private static _instance?: PropagationAPI;\n\n  /** Empty private constructor prevents end users from constructing a new instance of the API */\n  private constructor() {}\n\n  /** Get the singleton instance of the Propagator API */\n  public static getInstance(): PropagationAPI {\n    if (!this._instance) {\n      this._instance = new PropagationAPI();\n    }\n\n    return this._instance;\n  }\n\n  /**\n   * Set the current propagator.\n   *\n   * @returns true if the propagator was successfully registered, else false\n   */\n  public setGlobalPropagator(propagator: TextMapPropagator): boolean {\n    return registerGlobal(API_NAME, propagator, DiagAPI.instance());\n  }\n\n  /**\n   * Inject context into a carrier to be propagated inter-process\n   *\n   * @param context Context carrying tracing data to inject\n   * @param carrier carrier to inject context into\n   * @param setter Function used to set values on the carrier\n   */\n  public inject<Carrier>(\n    context: Context,\n    carrier: Carrier,\n    setter: TextMapSetter<Carrier> = defaultTextMapSetter\n  ): void {\n    return this._getGlobalPropagator().inject(context, carrier, setter);\n  }\n\n  /**\n   * Extract context from a carrier\n   *\n   * @param context Context which the newly created context will inherit from\n   * @param carrier Carrier to extract context from\n   * @param getter Function used to extract keys from a carrier\n   */\n  public extract<Carrier>(\n    context: Context,\n    carrier: Carrier,\n    getter: TextMapGetter<Carrier> = defaultTextMapGetter\n  ): Context {\n    return this._getGlobalPropagator().extract(context, carrier, getter);\n  }\n\n  /**\n   * Return a list of all fields which may be used by the propagator.\n   */\n  public fields(): string[] {\n    return this._getGlobalPropagator().fields();\n  }\n\n  /** Remove the global propagator */\n  public disable() {\n    unregisterGlobal(API_NAME, DiagAPI.instance());\n  }\n\n  public createBaggage = createBaggage;\n\n  public getBaggage = getBaggage;\n\n  public getActiveBaggage = getActiveBaggage;\n\n  public setBaggage = setBaggage;\n\n  public deleteBaggage = deleteBaggage;\n\n  private _getGlobalPropagator(): TextMapPropagator {\n    return getGlobal(API_NAME) || NOOP_TEXT_MAP_PROPAGATOR;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,OAAO,EACL,SAAS,EACT,cAAc,EACd,gBAAgB,GACjB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,qBAAqB,EAAE,MAAM,sCAAsC,CAAC;AAC7E,OAAO,EACL,oBAAoB,EACpB,oBAAoB,GAIrB,MAAM,kCAAkC,CAAC;AAC1C,OAAO,EACL,UAAU,EACV,gBAAgB,EAChB,UAAU,EACV,aAAa,GACd,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;;;;;;;AAEjC,IAAM,QAAQ,GAAG,aAAa,CAAC;AAC/B,IAAM,wBAAwB,GAAG,sQAAI,wBAAqB,EAAE,CAAC;AAE7D;;GAEG,CACH,IAAA,iBAAA;IAGE,6FAAA,EAA+F,CAC/F,SAAA;QA8DO,IAAA,CAAA,aAAa,iPAAG,gBAAa,CAAC;QAE9B,IAAA,CAAA,UAAU,8PAAG,aAAU,CAAC;QAExB,IAAA,CAAA,gBAAgB,8PAAG,mBAAgB,CAAC;QAEpC,IAAA,CAAA,UAAU,8PAAG,aAAU,CAAC;QAExB,IAAA,CAAA,aAAa,GAAG,2QAAa,CAAC;IAtEd,CAAC;IAExB,qDAAA,EAAuD,CACzC,eAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,cAAc,EAAE,CAAC;SACvC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;;OAIG,CACI,eAAA,SAAA,CAAA,mBAAmB,GAA1B,SAA2B,UAA6B;QACtD,QAAO,6QAAA,AAAc,EAAC,QAAQ,EAAE,UAAU,2OAAE,UAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;OAMG,CACI,eAAA,SAAA,CAAA,MAAM,GAAb,SACE,OAAgB,EAChB,OAAgB,EAChB,MAAqD;QAArD,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,6PAAA,CAAA,uBAAqD;QAAA;QAErD,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACtE,CAAC;IAED;;;;;;OAMG,CACI,eAAA,SAAA,CAAA,OAAO,GAAd,SACE,OAAgB,EAChB,OAAgB,EAChB,MAAqD;QAArD,IAAA,WAAA,KAAA,GAAA;YAAA,SAAA,6PAAA,CAAA,uBAAqD;QAAA;QAErD,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG,CACI,eAAA,SAAA,CAAA,MAAM,GAAb;QACE,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,CAAC;IAC9C,CAAC;IAED,iCAAA,EAAmC,CAC5B,eAAA,SAAA,CAAA,OAAO,GAAd;qQACE,mBAAA,AAAgB,EAAC,QAAQ,2OAAE,UAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjD,CAAC;IAYO,eAAA,SAAA,CAAA,oBAAoB,GAA5B;QACE,oQAAO,YAAA,AAAS,EAAC,QAAQ,CAAC,IAAI,wBAAwB,CAAC;IACzD,CAAC;IACH,OAAA,cAAC;AAAD,CAAC,AA/ED,IA+EC", "debugId": null}}, {"offset": {"line": 1572, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/propagation-api.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/propagation-api.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { PropagationAPI } from './api/propagation';\n/** Entrypoint for propagation API */\nexport const propagation = PropagationAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,sEAAsE;AACtE,qCAAqC;;;;AACrC,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;;AAE5C,IAAM,WAAW,mPAAG,iBAAc,CAAC,WAAW,EAAE,CAAC", "debugId": null}}, {"offset": {"line": 1598, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/trace_flags.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/trace_flags.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport enum TraceFlags {\n  /** Represents no flag set. */\n  NONE = 0x0,\n  /** Bit to represent whether trace is sampled in trace flags. */\n  SAMPLED = 0x1 << 0,\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AACH,IAAY,UAKX;AALD,CAAA,SAAY,UAAU;IACpB,4BAAA,EAA8B,CAC9B,UAAA,CAAA,UAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAU,CAAA;IACV,8DAAA,EAAgE,CAChE,UAAA,CAAA,UAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAkB,CAAA;AACpB,CAAC,EALW,UAAU,IAAA,CAAV,UAAU,GAAA,CAAA,CAAA,GAKrB", "debugId": null}}, {"offset": {"line": 1624, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/invalid-span-constants.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SpanContext } from './span_context';\nimport { TraceFlags } from './trace_flags';\n\nexport const INVALID_SPANID = '0000000000000000';\nexport const INVALID_TRACEID = '00000000000000000000000000000000';\nexport const INVALID_SPAN_CONTEXT: SpanContext = {\n  traceId: INVALID_TRACEID,\n  spanId: INVALID_SPANID,\n  traceFlags: TraceFlags.NONE,\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;AAGH,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;;AAEpC,IAAM,cAAc,GAAG,kBAAkB,CAAC;AAC1C,IAAM,eAAe,GAAG,kCAAkC,CAAC;AAC3D,IAAM,oBAAoB,GAAgB;IAC/C,OAAO,EAAE,eAAe;IACxB,MAAM,EAAE,cAAc;IACtB,UAAU,oPAAE,aAAU,CAAC,IAAI;CAC5B,CAAC", "debugId": null}}, {"offset": {"line": 1656, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/NonRecordingSpan.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/NonRecordingSpan.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Exception } from '../common/Exception';\nimport { TimeInput } from '../common/Time';\nimport { SpanAttributes } from './attributes';\nimport { INVALID_SPAN_CONTEXT } from './invalid-span-constants';\nimport { Span } from './span';\nimport { SpanContext } from './span_context';\nimport { SpanStatus } from './status';\nimport { Link } from './link';\n\n/**\n * The NonRecordingSpan is the default {@link Span} that is used when no Span\n * implementation is available. All operations are no-op including context\n * propagation.\n */\nexport class NonRecordingSpan implements Span {\n  constructor(\n    private readonly _spanContext: SpanContext = INVALID_SPAN_CONTEXT\n  ) {}\n\n  // Returns a SpanContext.\n  spanContext(): SpanContext {\n    return this._spanContext;\n  }\n\n  // By default does nothing\n  setAttribute(_key: string, _value: unknown): this {\n    return this;\n  }\n\n  // By default does nothing\n  setAttributes(_attributes: SpanAttributes): this {\n    return this;\n  }\n\n  // By default does nothing\n  addEvent(_name: string, _attributes?: SpanAttributes): this {\n    return this;\n  }\n\n  addLink(_link: Link): this {\n    return this;\n  }\n\n  addLinks(_links: Link[]): this {\n    return this;\n  }\n\n  // By default does nothing\n  setStatus(_status: SpanStatus): this {\n    return this;\n  }\n\n  // By default does nothing\n  updateName(_name: string): this {\n    return this;\n  }\n\n  // By default does nothing\n  end(_endTime?: TimeInput): void {}\n\n  // isRecording always returns false for NonRecordingSpan.\n  isRecording(): boolean {\n    return false;\n  }\n\n  // By default does nothing\n  recordException(_exception: Exception, _time?: TimeInput): void {}\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,0BAA0B,CAAC;;AAMhE;;;;GAIG,CACH,IAAA,mBAAA;IACE,SAAA,iBACmB,YAAgD;QAAhD,IAAA,iBAAA,KAAA,GAAA;YAAA,eAAA,kQAAA,CAAA,uBAAgD;QAAA;QAAhD,IAAA,CAAA,YAAY,GAAZ,YAAY,CAAoC;IAChE,CAAC;IAEJ,yBAAyB;IACzB,iBAAA,SAAA,CAAA,WAAW,GAAX;QACE,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,0BAA0B;IAC1B,iBAAA,SAAA,CAAA,YAAY,GAAZ,SAAa,IAAY,EAAE,MAAe;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0BAA0B;IAC1B,iBAAA,SAAA,CAAA,aAAa,GAAb,SAAc,WAA2B;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0BAA0B;IAC1B,iBAAA,SAAA,CAAA,QAAQ,GAAR,SAAS,KAAa,EAAE,WAA4B;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,iBAAA,SAAA,CAAA,OAAO,GAAP,SAAQ,KAAW;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,iBAAA,SAAA,CAAA,QAAQ,GAAR,SAAS,MAAc;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0BAA0B;IAC1B,iBAAA,SAAA,CAAA,SAAS,GAAT,SAAU,OAAmB;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0BAA0B;IAC1B,iBAAA,SAAA,CAAA,UAAU,GAAV,SAAW,KAAa;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0BAA0B;IAC1B,iBAAA,SAAA,CAAA,GAAG,GAAH,SAAI,QAAoB,GAAS,CAAC;IAElC,yDAAyD;IACzD,iBAAA,SAAA,CAAA,WAAW,GAAX;QACE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,0BAA0B;IAC1B,iBAAA,SAAA,CAAA,eAAe,GAAf,SAAgB,UAAqB,EAAE,KAAiB,GAAS,CAAC;IACpE,OAAA,gBAAC;AAAD,CAAC,AArDD,IAqDC", "debugId": null}}, {"offset": {"line": 1732, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/context-utils.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/context-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createContextKey } from '../context/context';\nimport { Context } from '../context/types';\nimport { Span } from './span';\nimport { SpanContext } from './span_context';\nimport { NonRecordingSpan } from './NonRecordingSpan';\nimport { ContextAPI } from '../api/context';\n\n/**\n * span key\n */\nconst SPAN_KEY = createContextKey('OpenTelemetry Context Key SPAN');\n\n/**\n * Return the span if one exists\n *\n * @param context context to get span from\n */\nexport function getSpan(context: Context): Span | undefined {\n  return (context.getValue(SPAN_KEY) as Span) || undefined;\n}\n\n/**\n * Gets the span from the current context, if one exists.\n */\nexport function getActiveSpan(): Span | undefined {\n  return getSpan(ContextAPI.getInstance().active());\n}\n\n/**\n * Set the span on a context\n *\n * @param context context to use as parent\n * @param span span to set active\n */\nexport function setSpan(context: Context, span: Span): Context {\n  return context.setValue(SPAN_KEY, span);\n}\n\n/**\n * Remove current span stored in the context\n *\n * @param context context to delete span from\n */\nexport function deleteSpan(context: Context): Context {\n  return context.deleteValue(SPAN_KEY);\n}\n\n/**\n * Wrap span context in a NoopSpan and set as span in a new\n * context\n *\n * @param context context to set active span on\n * @param spanContext span context to be wrapped\n */\nexport function setSpanContext(\n  context: Context,\n  spanContext: SpanContext\n): Context {\n  return setSpan(context, new NonRecordingSpan(spanContext));\n}\n\n/**\n * Get the span context of the span if it exists.\n *\n * @param context context to get values from\n */\nexport function getSpanContext(context: Context): SpanContext | undefined {\n  return getSpan(context)?.spanContext();\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAItD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;;;;AAE5C;;GAEG,CACH,IAAM,QAAQ,uPAAG,mBAAA,AAAgB,EAAC,gCAAgC,CAAC,CAAC;AAO9D,SAAU,OAAO,CAAC,OAAgB;IACtC,OAAQ,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAU,IAAI,SAAS,CAAC;AAC3D,CAAC;AAKK,SAAU,aAAa;IAC3B,OAAO,OAAO,6OAAC,aAAU,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;AACpD,CAAC;AAQK,SAAU,OAAO,CAAC,OAAgB,EAAE,IAAU;IAClD,OAAO,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC1C,CAAC;AAOK,SAAU,UAAU,CAAC,OAAgB;IACzC,OAAO,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACvC,CAAC;AASK,SAAU,cAAc,CAC5B,OAAgB,EAChB,WAAwB;IAExB,OAAO,OAAO,CAAC,OAAO,EAAE,2PAAI,mBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;AAC7D,CAAC;AAOK,SAAU,cAAc,CAAC,OAAgB;;IAC7C,OAAO,CAAA,KAAA,OAAO,CAAC,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,WAAW,EAAE,CAAC;AACzC,CAAC", "debugId": null}}, {"offset": {"line": 1786, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/spancontext-utils.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/spancontext-utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { INVALID_SPANID, INVALID_TRACEID } from './invalid-span-constants';\nimport { NonRecordingSpan } from './NonRecordingSpan';\nimport { Span } from './span';\nimport { SpanContext } from './span_context';\n\nconst VALID_TRACEID_REGEX = /^([0-9a-f]{32})$/i;\nconst VALID_SPANID_REGEX = /^[0-9a-f]{16}$/i;\n\nexport function isValidTraceId(traceId: string): boolean {\n  return VALID_TRACEID_REGEX.test(traceId) && traceId !== INVALID_TRACEID;\n}\n\nexport function isValidSpanId(spanId: string): boolean {\n  return VALID_SPANID_REGEX.test(spanId) && spanId !== INVALID_SPANID;\n}\n\n/**\n * Returns true if this {@link SpanContext} is valid.\n * @return true if this {@link SpanContext} is valid.\n */\nexport function isSpanContextValid(spanContext: SpanContext): boolean {\n  return (\n    isValidTraceId(spanContext.traceId) && isValidSpanId(spanContext.spanId)\n  );\n}\n\n/**\n * Wrap the given {@link SpanContext} in a new non-recording {@link Span}\n *\n * @param spanContext span context to be wrapped\n * @returns a new non-recording {@link Span} with the provided context\n */\nexport function wrapSpanContext(spanContext: SpanContext): Span {\n  return new NonRecordingSpan(spanContext);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;AACH,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAC3E,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;;;AAItD,IAAM,mBAAmB,GAAG,mBAAmB,CAAC;AAChD,IAAM,kBAAkB,GAAG,iBAAiB,CAAC;AAEvC,SAAU,cAAc,CAAC,OAAe;IAC5C,OAAO,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,OAAO,wQAAK,kBAAe,CAAC;AAC1E,CAAC;AAEK,SAAU,aAAa,CAAC,MAAc;IAC1C,OAAO,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,MAAM,wQAAK,iBAAc,CAAC;AACtE,CAAC;AAMK,SAAU,kBAAkB,CAAC,WAAwB;IACzD,OAAO,AACL,cAAc,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,CACzE,CAAC;AACJ,CAAC;AAQK,SAAU,eAAe,CAAC,WAAwB;IACtD,OAAO,2PAAI,mBAAgB,CAAC,WAAW,CAAC,CAAC;AAC3C,CAAC", "debugId": null}}, {"offset": {"line": 1828, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/NoopTracer.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/NoopTracer.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ContextAPI } from '../api/context';\nimport { Context } from '../context/types';\nimport { getSpanContext, setSpan } from '../trace/context-utils';\nimport { NonRecordingSpan } from './NonRecordingSpan';\nimport { Span } from './span';\nimport { isSpanContextValid } from './spancontext-utils';\nimport { SpanOptions } from './SpanOptions';\nimport { SpanContext } from './span_context';\nimport { Tracer } from './tracer';\n\nconst contextApi = ContextAPI.getInstance();\n\n/**\n * No-op implementations of {@link Tracer}.\n */\nexport class NoopTracer implements Tracer {\n  // startSpan starts a noop span.\n  startSpan(\n    name: string,\n    options?: SpanOptions,\n    context = contextApi.active()\n  ): Span {\n    const root = Boolean(options?.root);\n    if (root) {\n      return new NonRecordingSpan();\n    }\n\n    const parentFromContext = context && getSpanContext(context);\n\n    if (\n      isSpanContext(parentFromContext) &&\n      isSpanContextValid(parentFromContext)\n    ) {\n      return new NonRecordingSpan(parentFromContext);\n    } else {\n      return new NonRecordingSpan();\n    }\n  }\n\n  startActiveSpan<F extends (span: Span) => ReturnType<F>>(\n    name: string,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: Span) => ReturnType<F>>(\n    name: string,\n    opts: SpanOptions | undefined,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: Span) => ReturnType<F>>(\n    name: string,\n    opts: SpanOptions | undefined,\n    ctx: Context | undefined,\n    fn: F\n  ): ReturnType<F>;\n  startActiveSpan<F extends (span: Span) => ReturnType<F>>(\n    name: string,\n    arg2?: F | SpanOptions,\n    arg3?: F | Context,\n    arg4?: F\n  ): ReturnType<F> | undefined {\n    let opts: SpanOptions | undefined;\n    let ctx: Context | undefined;\n    let fn: F;\n\n    if (arguments.length < 2) {\n      return;\n    } else if (arguments.length === 2) {\n      fn = arg2 as F;\n    } else if (arguments.length === 3) {\n      opts = arg2 as SpanOptions | undefined;\n      fn = arg3 as F;\n    } else {\n      opts = arg2 as SpanOptions | undefined;\n      ctx = arg3 as Context | undefined;\n      fn = arg4 as F;\n    }\n\n    const parentContext = ctx ?? contextApi.active();\n    const span = this.startSpan(name, opts, parentContext);\n    const contextWithSpanSet = setSpan(parentContext, span);\n\n    return contextApi.with(contextWithSpanSet, fn, undefined, span);\n  }\n}\n\nfunction isSpanContext(spanContext: any): spanContext is SpanContext {\n  return (\n    typeof spanContext === 'object' &&\n    typeof spanContext['spanId'] === 'string' &&\n    typeof spanContext['traceId'] === 'string' &&\n    typeof spanContext['traceFlags'] === 'number'\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAE5C,OAAO,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AACjE,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEtD,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;;;;;AAKzD,IAAM,UAAU,+OAAG,aAAU,CAAC,WAAW,EAAE,CAAC;AAE5C;;GAEG,CACH,IAAA,aAAA;IAAA,SAAA,cAoEA,CAAC;IAnEC,gCAAgC;IAChC,WAAA,SAAA,CAAA,SAAS,GAAT,SACE,IAAY,EACZ,OAAqB,EACrB,OAA6B;QAA7B,IAAA,YAAA,KAAA,GAAA;YAAA,UAAU,UAAU,CAAC,MAAM,EAAE;QAAA;QAE7B,IAAM,IAAI,GAAG,OAAO,CAAC,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,IAAI,CAAC,CAAC;QACpC,IAAI,IAAI,EAAE;YACR,OAAO,2PAAI,mBAAgB,EAAE,CAAC;SAC/B;QAED,IAAM,iBAAiB,GAAG,OAAO,IAAI,4QAAA,AAAc,EAAC,OAAO,CAAC,CAAC;QAE7D,IACE,aAAa,CAAC,iBAAiB,CAAC,mQAChC,qBAAA,AAAkB,EAAC,iBAAiB,CAAC,EACrC;YACA,OAAO,2PAAI,mBAAgB,CAAC,iBAAiB,CAAC,CAAC;SAChD,MAAM;YACL,OAAO,2PAAI,mBAAgB,EAAE,CAAC;SAC/B;IACH,CAAC;IAiBD,WAAA,SAAA,CAAA,eAAe,GAAf,SACE,IAAY,EACZ,IAAsB,EACtB,IAAkB,EAClB,IAAQ;QAER,IAAI,IAA6B,CAAC;QAClC,IAAI,GAAwB,CAAC;QAC7B,IAAI,EAAK,CAAC;QAEV,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,OAAO;SACR,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,EAAE,GAAG,IAAS,CAAC;SAChB,MAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,IAAI,GAAG,IAA+B,CAAC;YACvC,EAAE,GAAG,IAAS,CAAC;SAChB,MAAM;YACL,IAAI,GAAG,IAA+B,CAAC;YACvC,GAAG,GAAG,IAA2B,CAAC;YAClC,EAAE,GAAG,IAAS,CAAC;SAChB;QAED,IAAM,aAAa,GAAG,GAAG,KAAA,QAAH,GAAG,KAAA,KAAA,IAAH,GAAG,GAAI,UAAU,CAAC,MAAM,EAAE,CAAC;QACjD,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;QACvD,IAAM,kBAAkB,8PAAG,UAAA,AAAO,EAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAExD,OAAO,UAAU,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAClE,CAAC;IACH,OAAA,UAAC;AAAD,CAAC,AApED,IAoEC;;AAED,SAAS,aAAa,CAAC,WAAgB;IACrC,OAAO,AACL,OAAO,WAAW,KAAK,QAAQ,IAC/B,OAAO,WAAW,CAAC,QAAQ,CAAC,KAAK,QAAQ,IACzC,OAAO,WAAW,CAAC,SAAS,CAAC,KAAK,QAAQ,IAC1C,OAAO,WAAW,CAAC,YAAY,CAAC,KAAK,QAAQ,CAC9C,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 1905, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/ProxyTracer.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/ProxyTracer.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context } from '../context/types';\nimport { NoopTracer } from './NoopTracer';\nimport { Span } from './span';\nimport { SpanOptions } from './SpanOptions';\nimport { Tracer } from './tracer';\nimport { TracerOptions } from './tracer_options';\n\nconst NOOP_TRACER = new NoopTracer();\n\n/**\n * Proxy tracer provided by the proxy tracer provider\n */\nexport class ProxyTracer implements Tracer {\n  // When a real implementation is provided, this will be it\n  private _delegate?: Tracer;\n\n  constructor(\n    private _provider: TracerDelegator,\n    public readonly name: string,\n    public readonly version?: string,\n    public readonly options?: TracerOptions\n  ) {}\n\n  startSpan(name: string, options?: SpanOptions, context?: Context): Span {\n    return this._getTracer().startSpan(name, options, context);\n  }\n\n  startActiveSpan<F extends (span: Span) => unknown>(\n    _name: string,\n    _options: F | SpanOptions,\n    _context?: F | Context,\n    _fn?: F\n  ): ReturnType<F> {\n    const tracer = this._getTracer();\n    return Reflect.apply(tracer.startActiveSpan, tracer, arguments);\n  }\n\n  /**\n   * Try to get a tracer from the proxy tracer provider.\n   * If the proxy tracer provider has no delegate, return a noop tracer.\n   */\n  private _getTracer() {\n    if (this._delegate) {\n      return this._delegate;\n    }\n\n    const tracer = this._provider.getDelegateTracer(\n      this.name,\n      this.version,\n      this.options\n    );\n\n    if (!tracer) {\n      return NOOP_TRACER;\n    }\n\n    this._delegate = tracer;\n    return this._delegate;\n  }\n}\n\nexport interface TracerDelegator {\n  getDelegateTracer(\n    name: string,\n    version?: string,\n    options?: TracerOptions\n  ): Tracer | undefined;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAM1C,IAAM,WAAW,GAAG,qPAAI,aAAU,EAAE,CAAC;AAErC;;GAEG,CACH,IAAA,cAAA;IAIE,SAAA,YACU,SAA0B,EAClB,IAAY,EACZ,OAAgB,EAChB,OAAuB;QAH/B,IAAA,CAAA,SAAS,GAAT,SAAS,CAAiB;QAClB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QACZ,IAAA,CAAA,OAAO,GAAP,OAAO,CAAS;QAChB,IAAA,CAAA,OAAO,GAAP,OAAO,CAAgB;IACtC,CAAC;IAEJ,YAAA,SAAA,CAAA,SAAS,GAAT,SAAU,IAAY,EAAE,OAAqB,EAAE,OAAiB;QAC9D,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;IAED,YAAA,SAAA,CAAA,eAAe,GAAf,SACE,KAAa,EACb,QAAyB,EACzB,QAAsB,EACtB,GAAO;QAEP,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;IAClE,CAAC;IAED;;;OAGG,CACK,YAAA,SAAA,CAAA,UAAU,GAAlB;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC7C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CACb,CAAC;QAEF,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,WAAW,CAAC;SACpB;QAED,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACH,OAAA,WAAC;AAAD,CAAC,AA/CD,IA+CC", "debugId": null}}, {"offset": {"line": 1963, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/NoopTracerProvider.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/NoopTracerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NoopTracer } from './NoopTracer';\nimport { Tracer } from './tracer';\nimport { TracerOptions } from './tracer_options';\nimport { TracerProvider } from './tracer_provider';\n\n/**\n * An implementation of the {@link TracerProvider} which returns an impotent\n * Tracer for all calls to `getTracer`.\n *\n * All operations are no-op.\n */\nexport class NoopTracerProvider implements TracerProvider {\n  getTracer(\n    _name?: string,\n    _version?: string,\n    _options?: TracerOptions\n  ): Tracer {\n    return new NoopTracer();\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;AAK1C;;;;;GAKG,CACH,IAAA,qBAAA;IAAA,SAAA,sBAQA,CAAC;IAPC,mBAAA,SAAA,CAAA,SAAS,GAAT,SACE,KAAc,EACd,QAAiB,EACjB,QAAwB;QAExB,OAAO,qPAAI,aAAU,EAAE,CAAC;IAC1B,CAAC;IACH,OAAA,kBAAC;AAAD,CAAC,AARD,IAQC", "debugId": null}}, {"offset": {"line": 2000, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/ProxyTracerProvider.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/ProxyTracerProvider.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Tracer } from './tracer';\nimport { TracerProvider } from './tracer_provider';\nimport { ProxyTracer } from './ProxyTracer';\nimport { NoopTracerProvider } from './NoopTracerProvider';\nimport { TracerOptions } from './tracer_options';\n\nconst NOOP_TRACER_PROVIDER = new NoopTracerProvider();\n\n/**\n * Tracer provider which provides {@link ProxyTracer}s.\n *\n * Before a delegate is set, tracers provided are NoOp.\n *   When a delegate is set, traces are provided from the delegate.\n *   When a delegate is set after tracers have already been provided,\n *   all tracers already provided will use the provided delegate implementation.\n */\nexport class ProxyTracerProvider implements TracerProvider {\n  private _delegate?: TracerProvider;\n\n  /**\n   * Get a {@link ProxyTracer}\n   */\n  getTracer(name: string, version?: string, options?: TracerOptions): Tracer {\n    return (\n      this.getDelegateTracer(name, version, options) ??\n      new ProxyTracer(this, name, version, options)\n    );\n  }\n\n  getDelegate(): TracerProvider {\n    return this._delegate ?? NOOP_TRACER_PROVIDER;\n  }\n\n  /**\n   * Set the delegate tracer provider\n   */\n  setDelegate(delegate: TracerProvider) {\n    this._delegate = delegate;\n  }\n\n  getDelegateTracer(\n    name: string,\n    version?: string,\n    options?: TracerOptions\n  ): Tracer | undefined {\n    return this._delegate?.getTracer(name, version, options);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;;;AAG1D,IAAM,oBAAoB,GAAG,6PAAI,qBAAkB,EAAE,CAAC;AAEtD;;;;;;;GAOG,CACH,IAAA,sBAAA;IAAA,SAAA,uBA+BA,CAAC;IA5BC;;OAEG,CACH,oBAAA,SAAA,CAAA,SAAS,GAAT,SAAU,IAAY,EAAE,OAAgB,EAAE,OAAuB;;QAC/D,OAAO,AACL,CAAA,KAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAC9C,sPAAI,cAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,oBAAA,SAAA,CAAA,WAAW,GAAX;;QACE,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,oBAAoB,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,oBAAA,SAAA,CAAA,WAAW,GAAX,SAAY,QAAwB;QAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,oBAAA,SAAA,CAAA,iBAAiB,GAAjB,SACE,IAAY,EACZ,OAAgB,EAChB,OAAuB;;QAEvB,OAAO,CAAA,KAAA,IAAI,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IACH,OAAA,mBAAC;AAAD,CAAC,AA/BD,IA+BC", "debugId": null}}, {"offset": {"line": 2058, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/api/trace.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/api/trace.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  getGlobal,\n  registerGlobal,\n  unregisterGlobal,\n} from '../internal/global-utils';\nimport { ProxyTracerProvider } from '../trace/ProxyTracerProvider';\nimport {\n  isSpanContextValid,\n  wrapSpanContext,\n} from '../trace/spancontext-utils';\nimport { Tracer } from '../trace/tracer';\nimport { TracerProvider } from '../trace/tracer_provider';\nimport {\n  deleteSpan,\n  getActiveSpan,\n  getSpan,\n  getSpanContext,\n  setSpan,\n  setSpanContext,\n} from '../trace/context-utils';\nimport { DiagAPI } from './diag';\n\nconst API_NAME = 'trace';\n\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Tracing API\n */\nexport class TraceAPI {\n  private static _instance?: TraceAPI;\n\n  private _proxyTracerProvider = new ProxyTracerProvider();\n\n  /** Empty private constructor prevents end users from constructing a new instance of the API */\n  private constructor() {}\n\n  /** Get the singleton instance of the Trace API */\n  public static getInstance(): TraceAPI {\n    if (!this._instance) {\n      this._instance = new TraceAPI();\n    }\n\n    return this._instance;\n  }\n\n  /**\n   * Set the current global tracer.\n   *\n   * @returns true if the tracer provider was successfully registered, else false\n   */\n  public setGlobalTracerProvider(provider: TracerProvider): boolean {\n    const success = registerGlobal(\n      API_NAME,\n      this._proxyTracerProvider,\n      DiagAPI.instance()\n    );\n    if (success) {\n      this._proxyTracerProvider.setDelegate(provider);\n    }\n    return success;\n  }\n\n  /**\n   * Returns the global tracer provider.\n   */\n  public getTracerProvider(): TracerProvider {\n    return getGlobal(API_NAME) || this._proxyTracerProvider;\n  }\n\n  /**\n   * Returns a tracer from the global tracer provider.\n   */\n  public getTracer(name: string, version?: string): Tracer {\n    return this.getTracerProvider().getTracer(name, version);\n  }\n\n  /** Remove the global tracer provider */\n  public disable() {\n    unregisterGlobal(API_NAME, DiagAPI.instance());\n    this._proxyTracerProvider = new ProxyTracerProvider();\n  }\n\n  public wrapSpanContext = wrapSpanContext;\n\n  public isSpanContextValid = isSpanContextValid;\n\n  public deleteSpan = deleteSpan;\n\n  public getSpan = getSpan;\n\n  public getActiveSpan = getActiveSpan;\n\n  public getSpanContext = getSpanContext;\n\n  public setSpan = setSpan;\n\n  public setSpanContext = setSpanContext;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,OAAO,EACL,SAAS,EACT,cAAc,EACd,gBAAgB,GACjB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EACL,kBAAkB,EAClB,eAAe,GAChB,MAAM,4BAA4B,CAAC;AAGpC,OAAO,EACL,UAAU,EACV,aAAa,EACb,OAAO,EACP,cAAc,EACd,OAAO,EACP,cAAc,GACf,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;;;;;;AAEjC,IAAM,QAAQ,GAAG,OAAO,CAAC;AAEzB;;GAEG,CACH,IAAA,WAAA;IAKE,6FAAA,EAA+F,CAC/F,SAAA;QAHQ,IAAA,CAAA,oBAAoB,GAAG,8PAAI,sBAAmB,EAAE,CAAC;QAmDlD,IAAA,CAAA,eAAe,8PAAG,kBAAe,CAAC;QAElC,IAAA,CAAA,kBAAkB,GAAG,gRAAkB,CAAC;QAExC,IAAA,CAAA,UAAU,0PAAG,aAAU,CAAC;QAExB,IAAA,CAAA,OAAO,0PAAG,UAAO,CAAC;QAElB,IAAA,CAAA,aAAa,0PAAG,gBAAa,CAAC;QAE9B,IAAA,CAAA,cAAc,GAAG,wQAAc,CAAC;QAEhC,IAAA,CAAA,OAAO,0PAAG,UAAO,CAAC;QAElB,IAAA,CAAA,cAAc,0PAAG,iBAAc,CAAC;IA9DhB,CAAC;IAExB,gDAAA,EAAkD,CACpC,SAAA,WAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,EAAE,CAAC;SACjC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;;OAIG,CACI,SAAA,SAAA,CAAA,uBAAuB,GAA9B,SAA+B,QAAwB;QACrD,IAAM,OAAO,GAAG,8QAAA,AAAc,EAC5B,QAAQ,EACR,IAAI,CAAC,oBAAoB,2OACzB,UAAO,CAAC,QAAQ,EAAE,CACnB,CAAC;QACF,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;SACjD;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG,CACI,SAAA,SAAA,CAAA,iBAAiB,GAAxB;QACE,oQAAO,YAAA,AAAS,EAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,oBAAoB,CAAC;IAC1D,CAAC;IAED;;OAEG,CACI,SAAA,SAAA,CAAA,SAAS,GAAhB,SAAiB,IAAY,EAAE,OAAgB;QAC7C,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED,sCAAA,EAAwC,CACjC,SAAA,SAAA,CAAA,OAAO,GAAd;qQACE,mBAAA,AAAgB,EAAC,QAAQ,2OAAE,UAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC/C,IAAI,CAAC,oBAAoB,GAAG,8PAAI,sBAAmB,EAAE,CAAC;IACxD,CAAC;IAiBH,OAAA,QAAC;AAAD,CAAC,AArED,IAqEC", "debugId": null}}, {"offset": {"line": 2139, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace-api.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace-api.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { TraceAPI } from './api/trace';\n/** Entrypoint for trace API */\nexport const trace = TraceAPI.getInstance();\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAEH,sEAAsE;AACtE,qCAAqC;;;;AACrC,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;;AAEhC,IAAM,KAAK,6OAAG,WAAQ,CAAC,WAAW,EAAE,CAAC", "debugId": null}}, {"offset": {"line": 2165, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/index.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { BaggageEntry, BaggageEntryMetadata, Baggage } from './baggage/types';\nexport { baggageEntryMetadataFromString } from './baggage/utils';\nexport { Exception } from './common/Exception';\nexport { HrTime, TimeInput } from './common/Time';\nexport { Attributes, AttributeValue } from './common/Attributes';\n\n// Context APIs\nexport { createContextKey, ROOT_CONTEXT } from './context/context';\nexport { Context, ContextManager } from './context/types';\nexport type { ContextAPI } from './api/context';\n\n// Diag APIs\nexport { DiagConsoleLogger } from './diag/consoleLogger';\nexport {\n  DiagLogFunction,\n  DiagLogger,\n  DiagLogLevel,\n  ComponentLoggerOptions,\n  DiagLoggerOptions,\n} from './diag/types';\nexport type { DiagAPI } from './api/diag';\n\n// Metrics APIs\nexport { createNoopMeter } from './metrics/NoopMeter';\nexport { MeterOptions, Meter } from './metrics/Meter';\nexport { MeterProvider } from './metrics/MeterProvider';\nexport {\n  ValueType,\n  Counter,\n  Gauge,\n  Histogram,\n  MetricOptions,\n  Observable,\n  ObservableCounter,\n  ObservableGauge,\n  ObservableUpDownCounter,\n  UpDownCounter,\n  BatchObservableCallback,\n  MetricAdvice,\n  MetricAttributes,\n  MetricAttributeValue,\n  ObservableCallback,\n} from './metrics/Metric';\nexport {\n  BatchObservableResult,\n  ObservableResult,\n} from './metrics/ObservableResult';\nexport type { MetricsAPI } from './api/metrics';\n\n// Propagation APIs\nexport {\n  TextMapPropagator,\n  TextMapSetter,\n  TextMapGetter,\n  defaultTextMapGetter,\n  defaultTextMapSetter,\n} from './propagation/TextMapPropagator';\nexport type { PropagationAPI } from './api/propagation';\n\n// Trace APIs\nexport { SpanAttributes, SpanAttributeValue } from './trace/attributes';\nexport { Link } from './trace/link';\nexport { ProxyTracer, TracerDelegator } from './trace/ProxyTracer';\nexport { ProxyTracerProvider } from './trace/ProxyTracerProvider';\nexport { Sampler } from './trace/Sampler';\nexport { SamplingDecision, SamplingResult } from './trace/SamplingResult';\nexport { SpanContext } from './trace/span_context';\nexport { SpanKind } from './trace/span_kind';\nexport { Span } from './trace/span';\nexport { SpanOptions } from './trace/SpanOptions';\nexport { SpanStatus, SpanStatusCode } from './trace/status';\nexport { TraceFlags } from './trace/trace_flags';\nexport { TraceState } from './trace/trace_state';\nexport { createTraceState } from './trace/internal/utils';\nexport { TracerProvider } from './trace/tracer_provider';\nexport { Tracer } from './trace/tracer';\nexport { TracerOptions } from './trace/tracer_options';\nexport {\n  isSpanContextValid,\n  isValidTraceId,\n  isValidSpanId,\n} from './trace/spancontext-utils';\nexport {\n  INVALID_SPANID,\n  INVALID_TRACEID,\n  INVALID_SPAN_CONTEXT,\n} from './trace/invalid-span-constants';\nexport type { TraceAPI } from './api/trace';\n\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { context } from './context-api';\nimport { diag } from './diag-api';\nimport { metrics } from './metrics-api';\nimport { propagation } from './propagation-api';\nimport { trace } from './trace-api';\n\n// Named export.\nexport { context, diag, metrics, propagation, trace };\n// Default export.\nexport default {\n  context,\n  diag,\n  metrics,\n  propagation,\n  trace,\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AA2FH,sEAAsE;AACtE,qCAAqC;AACrC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;uCAKrB;IACb,OAAO,EAAA,2OAAA,CAAA,UAAA;IACP,IAAI,EAAA,wOAAA,CAAA,OAAA;IACJ,OAAO,EAAA,2OAAA,CAAA,UAAA;IACP,WAAW,EAAA,+OAAA,CAAA,cAAA;IACX,KAAK,EAAA,yOAAA,CAAA,QAAA;CACN,CAAC", "debugId": null}}, {"offset": {"line": 2229, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/diag/consoleLogger.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/diag/consoleLogger.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DiagLogger, DiagLogFunction } from './types';\n\ntype ConsoleMapKeys = 'error' | 'warn' | 'info' | 'debug' | 'trace';\nconst consoleMap: { n: keyof DiagLogger; c: ConsoleMapKeys }[] = [\n  { n: 'error', c: 'error' },\n  { n: 'warn', c: 'warn' },\n  { n: 'info', c: 'info' },\n  { n: 'debug', c: 'debug' },\n  { n: 'verbose', c: 'trace' },\n];\n\n/**\n * A simple Immutable Console based diagnostic logger which will output any messages to the Console.\n * If you want to limit the amount of logging to a specific level or lower use the\n * {@link createLogLevelDiagLogger}\n */\nexport class DiagConsoleLogger implements DiagLogger {\n  constructor() {\n    function _consoleFunc(funcName: ConsoleMapKeys): DiagLogFunction {\n      return function (...args) {\n        if (console) {\n          // Some environments only expose the console when the F12 developer console is open\n          // eslint-disable-next-line no-console\n          let theFunc = console[funcName];\n          if (typeof theFunc !== 'function') {\n            // Not all environments support all functions\n            // eslint-disable-next-line no-console\n            theFunc = console.log;\n          }\n\n          // One last final check\n          if (typeof theFunc === 'function') {\n            return theFunc.apply(console, args);\n          }\n        }\n      };\n    }\n\n    for (let i = 0; i < consoleMap.length; i++) {\n      this[consoleMap[i].n] = _consoleFunc(consoleMap[i].c);\n    }\n  }\n\n  /** Log an error scenario that was not expected and caused the requested operation to fail. */\n  public error!: DiagLogFunction;\n\n  /**\n   * Log a warning scenario to inform the developer of an issues that should be investigated.\n   * The requested operation may or may not have succeeded or completed.\n   */\n  public warn!: DiagLogFunction;\n\n  /**\n   * Log a general informational message, this should not affect functionality.\n   * This is also the default logging level so this should NOT be used for logging\n   * debugging level information.\n   */\n  public info!: DiagLogFunction;\n\n  /**\n   * Log a general debug message that can be useful for identifying a failure.\n   * Information logged at this level may include diagnostic details that would\n   * help identify a failure scenario. Useful scenarios would be to log the execution\n   * order of async operations\n   */\n  public debug!: DiagLogFunction;\n\n  /**\n   * Log a detailed (verbose) trace level logging that can be used to identify failures\n   * where debug level logging would be insufficient, this level of tracing can include\n   * input and output parameters and as such may include PII information passing through\n   * the API. As such it is recommended that this level of tracing should not be enabled\n   * in a production environment.\n   */\n  public verbose!: DiagLogFunction;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,IAAM,UAAU,GAAiD;IAC/D;QAAE,CAAC,EAAE,OAAO;QAAE,CAAC,EAAE,OAAO;IAAA,CAAE;IAC1B;QAAE,CAAC,EAAE,MAAM;QAAE,CAAC,EAAE,MAAM;IAAA,CAAE;IACxB;QAAE,CAAC,EAAE,MAAM;QAAE,CAAC,EAAE,MAAM;IAAA,CAAE;IACxB;QAAE,CAAC,EAAE,OAAO;QAAE,CAAC,EAAE,OAAO;IAAA,CAAE;IAC1B;QAAE,CAAC,EAAE,SAAS;QAAE,CAAC,EAAE,OAAO;IAAA,CAAE;CAC7B,CAAC;AAEF;;;;GAIG,CACH,IAAA,oBAAA;IACE,SAAA;QACE,SAAS,YAAY,CAAC,QAAwB;YAC5C,OAAO;gBAAU,IAAA,OAAA,EAAA,CAAO;oBAAP,IAAA,KAAA,CAAO,EAAP,KAAA,UAAA,MAAO,EAAP,IAAO,CAAA;oBAAP,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAO;;gBACtB,IAAI,OAAO,EAAE;oBACX,mFAAmF;oBACnF,sCAAsC;oBACtC,IAAI,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAChC,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;wBACjC,6CAA6C;wBAC7C,sCAAsC;wBACtC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC;qBACvB;oBAED,uBAAuB;oBACvB,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;wBACjC,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBACrC;iBACF;YACH,CAAC,CAAC;QACJ,CAAC;QAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;YAC1C,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACvD;IACH,CAAC;IAkCH,OAAA,iBAAC;AAAD,CAAC,AA3DD,IA2DC", "debugId": null}}, {"offset": {"line": 2308, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/metrics/Metric.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/metrics/Metric.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Attributes, AttributeValue } from '../common/Attributes';\nimport { Context } from '../context/types';\nimport { BatchObservableResult, ObservableResult } from './ObservableResult';\n\n/**\n * Advisory options influencing aggregation configuration parameters.\n * @experimental\n */\nexport interface MetricAdvice {\n  /**\n   * Hint the explicit bucket boundaries for SDK if the metric is been\n   * aggregated with a HistogramAggregator.\n   */\n  explicitBucketBoundaries?: number[];\n}\n\n/**\n * Options needed for metric creation\n */\nexport interface MetricOptions {\n  /**\n   * The description of the Metric.\n   * @default ''\n   */\n  description?: string;\n\n  /**\n   * The unit of the Metric values.\n   * @default ''\n   */\n  unit?: string;\n\n  /**\n   * Indicates the type of the recorded value.\n   * @default {@link ValueType.DOUBLE}\n   */\n  valueType?: ValueType;\n\n  /**\n   * The advice influencing aggregation configuration parameters.\n   * @experimental\n   */\n  advice?: MetricAdvice;\n}\n\n/** The Type of value. It describes how the data is reported. */\nexport enum ValueType {\n  INT,\n  DOUBLE,\n}\n\n/**\n * Counter is the most common synchronous instrument. This instrument supports\n * an `Add(increment)` function for reporting a sum, and is restricted to\n * non-negative increments. The default aggregation is Sum, as for any additive\n * instrument.\n *\n * Example uses for Counter:\n * <ol>\n *   <li> count the number of bytes received. </li>\n *   <li> count the number of requests completed. </li>\n *   <li> count the number of accounts created. </li>\n *   <li> count the number of checkpoints run. </li>\n *   <li> count the number of 5xx errors. </li>\n * <ol>\n */\nexport interface Counter<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> {\n  /**\n   * Increment value of counter by the input. Inputs must not be negative.\n   */\n  add(value: number, attributes?: AttributesTypes, context?: Context): void;\n}\n\nexport interface UpDownCounter<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> {\n  /**\n   * Increment value of counter by the input. Inputs may be negative.\n   */\n  add(value: number, attributes?: AttributesTypes, context?: Context): void;\n}\n\nexport interface Gauge<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> {\n  /**\n   * Records a measurement.\n   */\n  record(value: number, attributes?: AttributesTypes, context?: Context): void;\n}\n\nexport interface Histogram<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> {\n  /**\n   * Records a measurement. Value of the measurement must not be negative.\n   */\n  record(value: number, attributes?: AttributesTypes, context?: Context): void;\n}\n\n/**\n * @deprecated please use {@link Attributes}\n */\nexport type MetricAttributes = Attributes;\n\n/**\n * @deprecated please use {@link AttributeValue}\n */\nexport type MetricAttributeValue = AttributeValue;\n\n/**\n * The observable callback for Observable instruments.\n */\nexport type ObservableCallback<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> = (\n  observableResult: ObservableResult<AttributesTypes>\n) => void | Promise<void>;\n\n/**\n * The observable callback for a batch of Observable instruments.\n */\nexport type BatchObservableCallback<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> = (\n  observableResult: BatchObservableResult<AttributesTypes>\n) => void | Promise<void>;\n\nexport interface Observable<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> {\n  /**\n   * Sets up a function that will be called whenever a metric collection is initiated.\n   *\n   * If the function is already in the list of callbacks for this Observable, the function is not added a second time.\n   */\n  addCallback(callback: ObservableCallback<AttributesTypes>): void;\n\n  /**\n   * Removes a callback previously registered with {@link Observable.addCallback}.\n   */\n  removeCallback(callback: ObservableCallback<AttributesTypes>): void;\n}\n\nexport type ObservableCounter<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> = Observable<AttributesTypes>;\nexport type ObservableUpDownCounter<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> = Observable<AttributesTypes>;\nexport type ObservableGauge<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> = Observable<AttributesTypes>;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CA+CH,8DAAA,EAAgE;;;AAChE,IAAY,SAGX;AAHD,CAAA,SAAY,SAAS;IACnB,SAAA,CAAA,SAAA,CAAA,MAAA,GAAA,EAAA,GAAA,KAAG,CAAA;IACH,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAM,CAAA;AACR,CAAC,EAHW,SAAS,IAAA,CAAT,SAAS,GAAA,CAAA,CAAA,GAGpB", "debugId": null}}, {"offset": {"line": 2334, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/SamplingResult.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/SamplingResult.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SpanAttributes } from './attributes';\nimport { TraceState } from './trace_state';\n\n/**\n * @deprecated use the one declared in @opentelemetry/sdk-trace-base instead.\n * A sampling decision that determines how a {@link Span} will be recorded\n * and collected.\n */\nexport enum SamplingDecision {\n  /**\n   * `Span.isRecording() === false`, span will not be recorded and all events\n   * and attributes will be dropped.\n   */\n  NOT_RECORD,\n  /**\n   * `Span.isRecording() === true`, but `Sampled` flag in {@link TraceFlags}\n   * MUST NOT be set.\n   */\n  RECORD,\n  /**\n   * `Span.isRecording() === true` AND `Sampled` flag in {@link TraceFlags}\n   * MUST be set.\n   */\n  RECORD_AND_SAMPLED,\n}\n\n/**\n * @deprecated use the one declared in @opentelemetry/sdk-trace-base instead.\n * A sampling result contains a decision for a {@link Span} and additional\n * attributes the sampler would like to added to the Span.\n */\nexport interface SamplingResult {\n  /**\n   * A sampling decision, refer to {@link SamplingDecision} for details.\n   */\n  decision: SamplingDecision;\n  /**\n   * The list of attributes returned by SamplingResult MUST be immutable.\n   * Caller may call {@link Sampler}.shouldSample any number of times and\n   * can safely cache the returned value.\n   */\n  attributes?: Readonly<SpanAttributes>;\n  /**\n   * A {@link TraceState} that will be associated with the {@link Span} through\n   * the new {@link SpanContext}. Samplers SHOULD return the TraceState from\n   * the passed-in {@link Context} if they do not intend to change it. Leaving\n   * the value undefined will also leave the TraceState unchanged.\n   */\n  traceState?: TraceState;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG,CAKH;;;;GAIG;;;AACH,IAAY,gBAgBX;AAhBD,CAAA,SAAY,gBAAgB;IAC1B;;;OAGG,CACH,gBAAA,CAAA,gBAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAU,CAAA;IACV;;;OAGG,CACH,gBAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAM,CAAA;IACN;;;OAGG,CACH,gBAAA,CAAA,gBAAA,CAAA,qBAAA,GAAA,EAAA,GAAA,oBAAkB,CAAA;AACpB,CAAC,EAhBW,gBAAgB,IAAA,CAAhB,gBAAgB,GAAA,CAAA,CAAA,GAgB3B", "debugId": null}}, {"offset": {"line": 2374, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/span_kind.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/span_kind.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport enum SpanKind {\n  /** Default value. Indicates that the span is used internally. */\n  INTERNAL = 0,\n\n  /**\n   * Indicates that the span covers server-side handling of an RPC or other\n   * remote request.\n   */\n  SERVER = 1,\n\n  /**\n   * Indicates that the span covers the client-side wrapper around an RPC or\n   * other remote request.\n   */\n  CLIENT = 2,\n\n  /**\n   * Indicates that the span describes producer sending a message to a\n   * broker. Unlike client and server, there is no direct critical path latency\n   * relationship between producer and consumer spans.\n   */\n  PRODUCER = 3,\n\n  /**\n   * Indicates that the span describes consumer receiving a message from a\n   * broker. Unlike client and server, there is no direct critical path latency\n   * relationship between producer and consumer spans.\n   */\n  CONSUMER = 4,\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AACH,IAAY,QA6BX;AA7BD,CAAA,SAAY,QAAQ;IAClB,+DAAA,EAAiE,CACjE,QAAA,CAAA,QAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAY,CAAA;IAEZ;;;OAGG,CACH,QAAA,CAAA,QAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IAEV;;;OAGG,CACH,QAAA,CAAA,QAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IAEV;;;;OAIG,CACH,QAAA,CAAA,QAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAY,CAAA;IAEZ;;;;OAIG,CACH,QAAA,CAAA,QAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAY,CAAA;AACd,CAAC,EA7BW,QAAQ,IAAA,CAAR,QAAQ,GAAA,CAAA,CAAA,GA6BnB", "debugId": null}}, {"offset": {"line": 2417, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/status.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/status.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport interface SpanStatus {\n  /** The status code of this message. */\n  code: SpanStatusCode;\n  /** A developer-facing error message. */\n  message?: string;\n}\n\n/**\n * An enumeration of status codes.\n */\nexport enum SpanStatusCode {\n  /**\n   * The default status.\n   */\n  UNSET = 0,\n  /**\n   * The operation has been validated by an Application developer or\n   * Operator to have completed successfully.\n   */\n  OK = 1,\n  /**\n   * The operation contains an error.\n   */\n  ERROR = 2,\n}\n"], "names": [], "mappings": "AAsBA;;GAEG;;;AACH,IAAY,cAcX;AAdD,CAAA,SAAY,cAAc;IACxB;;OAEG,CACH,cAAA,CAAA,cAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAS,CAAA;IACT;;;OAGG,CACH,cAAA,CAAA,cAAA,CAAA,KAAA,GAAA,EAAA,GAAA,IAAM,CAAA;IACN;;OAEG,CACH,cAAA,CAAA,cAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAS,CAAA;AACX,CAAC,EAdW,cAAc,IAAA,CAAd,cAAc,GAAA,CAAA,CAAA,GAczB", "debugId": null}}, {"offset": {"line": 2439, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-validators.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/internal/tracestate-validators.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nconst VALID_KEY_CHAR_RANGE = '[_0-9a-z-*/]';\nconst VALID_KEY = `[a-z]${VALID_KEY_CHAR_RANGE}{0,255}`;\nconst VALID_VENDOR_KEY = `[a-z0-9]${VALID_KEY_CHAR_RANGE}{0,240}@[a-z]${VALID_KEY_CHAR_RANGE}{0,13}`;\nconst VALID_KEY_REGEX = new RegExp(`^(?:${VALID_KEY}|${VALID_VENDOR_KEY})$`);\nconst VALID_VALUE_BASE_REGEX = /^[ -~]{0,255}[!-~]$/;\nconst INVALID_VALUE_COMMA_EQUAL_REGEX = /,|=/;\n\n/**\n * Key is opaque string up to 256 characters printable. It MUST begin with a\n * lowercase letter, and can only contain lowercase letters a-z, digits 0-9,\n * underscores _, dashes -, asterisks *, and forward slashes /.\n * For multi-tenant vendor scenarios, an at sign (@) can be used to prefix the\n * vendor name. Vendors SHOULD set the tenant ID at the beginning of the key.\n * see https://www.w3.org/TR/trace-context/#key\n */\nexport function validateKey(key: string): boolean {\n  return VALID_KEY_REGEX.test(key);\n}\n\n/**\n * Value is opaque string up to 256 characters printable ASCII RFC0020\n * characters (i.e., the range 0x20 to 0x7E) except comma , and =.\n */\nexport function validateValue(value: string): boolean {\n  return (\n    VALID_VALUE_BASE_REGEX.test(value) &&\n    !INVALID_VALUE_COMMA_EQUAL_REGEX.test(value)\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,IAAM,oBAAoB,GAAG,cAAc,CAAC;AAC5C,IAAM,SAAS,GAAG,UAAQ,oBAAoB,GAAA,SAAS,CAAC;AACxD,IAAM,gBAAgB,GAAG,aAAW,oBAAoB,GAAA,kBAAgB,oBAAoB,GAAA,QAAQ,CAAC;AACrG,IAAM,eAAe,GAAG,IAAI,MAAM,CAAC,SAAO,SAAS,GAAA,MAAI,gBAAgB,GAAA,IAAI,CAAC,CAAC;AAC7E,IAAM,sBAAsB,GAAG,qBAAqB,CAAC;AACrD,IAAM,+BAA+B,GAAG,KAAK,CAAC;AAUxC,SAAU,WAAW,CAAC,GAAW;IACrC,OAAO,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC,CAAC;AAMK,SAAU,aAAa,CAAC,KAAa;IACzC,OAAO,AACL,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,IAClC,CAAC,+BAA+B,CAAC,IAAI,CAAC,KAAK,CAAC,CAC7C,CAAC;AACJ,CAAC", "debugId": null}}, {"offset": {"line": 2473, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/internal/tracestate-impl.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/internal/tracestate-impl.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { TraceState } from '../trace_state';\nimport { validateKey, validateValue } from './tracestate-validators';\n\nconst MAX_TRACE_STATE_ITEMS = 32;\nconst MAX_TRACE_STATE_LEN = 512;\nconst LIST_MEMBERS_SEPARATOR = ',';\nconst LIST_MEMBER_KEY_VALUE_SPLITTER = '=';\n\n/**\n * TraceState must be a class and not a simple object type because of the spec\n * requirement (https://www.w3.org/TR/trace-context/#tracestate-field).\n *\n * Here is the list of allowed mutations:\n * - New key-value pair should be added into the beginning of the list\n * - The value of any key can be updated. Modified keys MUST be moved to the\n * beginning of the list.\n */\nexport class TraceStateImpl implements TraceState {\n  private _internalState: Map<string, string> = new Map();\n\n  constructor(rawTraceState?: string) {\n    if (rawTraceState) this._parse(rawTraceState);\n  }\n\n  set(key: string, value: string): TraceStateImpl {\n    // TODO: Benchmark the different approaches(map vs list) and\n    // use the faster one.\n    const traceState = this._clone();\n    if (traceState._internalState.has(key)) {\n      traceState._internalState.delete(key);\n    }\n    traceState._internalState.set(key, value);\n    return traceState;\n  }\n\n  unset(key: string): TraceStateImpl {\n    const traceState = this._clone();\n    traceState._internalState.delete(key);\n    return traceState;\n  }\n\n  get(key: string): string | undefined {\n    return this._internalState.get(key);\n  }\n\n  serialize(): string {\n    return this._keys()\n      .reduce((agg: string[], key) => {\n        agg.push(key + LIST_MEMBER_KEY_VALUE_SPLITTER + this.get(key));\n        return agg;\n      }, [])\n      .join(LIST_MEMBERS_SEPARATOR);\n  }\n\n  private _parse(rawTraceState: string) {\n    if (rawTraceState.length > MAX_TRACE_STATE_LEN) return;\n    this._internalState = rawTraceState\n      .split(LIST_MEMBERS_SEPARATOR)\n      .reverse() // Store in reverse so new keys (.set(...)) will be placed at the beginning\n      .reduce((agg: Map<string, string>, part: string) => {\n        const listMember = part.trim(); // Optional Whitespace (OWS) handling\n        const i = listMember.indexOf(LIST_MEMBER_KEY_VALUE_SPLITTER);\n        if (i !== -1) {\n          const key = listMember.slice(0, i);\n          const value = listMember.slice(i + 1, part.length);\n          if (validateKey(key) && validateValue(value)) {\n            agg.set(key, value);\n          } else {\n            // TODO: Consider to add warning log\n          }\n        }\n        return agg;\n      }, new Map());\n\n    // Because of the reverse() requirement, trunc must be done after map is created\n    if (this._internalState.size > MAX_TRACE_STATE_ITEMS) {\n      this._internalState = new Map(\n        Array.from(this._internalState.entries())\n          .reverse() // Use reverse same as original tracestate parse chain\n          .slice(0, MAX_TRACE_STATE_ITEMS)\n      );\n    }\n  }\n\n  private _keys(): string[] {\n    return Array.from(this._internalState.keys()).reverse();\n  }\n\n  private _clone(): TraceStateImpl {\n    const traceState = new TraceStateImpl();\n    traceState._internalState = new Map(this._internalState);\n    return traceState;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;;AAErE,IAAM,qBAAqB,GAAG,EAAE,CAAC;AACjC,IAAM,mBAAmB,GAAG,GAAG,CAAC;AAChC,IAAM,sBAAsB,GAAG,GAAG,CAAC;AACnC,IAAM,8BAA8B,GAAG,GAAG,CAAC;AAE3C;;;;;;;;GAQG,CACH,IAAA,iBAAA;IAGE,SAAA,eAAY,aAAsB;QAF1B,IAAA,CAAA,cAAc,GAAwB,IAAI,GAAG,EAAE,CAAC;QAGtD,IAAI,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAED,eAAA,SAAA,CAAA,GAAG,GAAH,SAAI,GAAW,EAAE,KAAa;QAC5B,4DAA4D;QAC5D,sBAAsB;QACtB,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACjC,IAAI,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACtC,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACvC;QACD,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC1C,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,eAAA,SAAA,CAAA,KAAK,GAAL,SAAM,GAAW;QACf,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QACjC,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACtC,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,eAAA,SAAA,CAAA,GAAG,GAAH,SAAI,GAAW;QACb,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAED,eAAA,SAAA,CAAA,SAAS,GAAT;QAAA,IAAA,QAAA,IAAA,CAOC;QANC,OAAO,IAAI,CAAC,KAAK,EAAE,CAChB,MAAM,CAAC,SAAC,GAAa,EAAE,GAAG;YACzB,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,8BAA8B,GAAG,KAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/D,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CACL,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAClC,CAAC;IAEO,eAAA,SAAA,CAAA,MAAM,GAAd,SAAe,aAAqB;QAClC,IAAI,aAAa,CAAC,MAAM,GAAG,mBAAmB,EAAE,OAAO;QACvD,IAAI,CAAC,cAAc,GAAG,aAAa,CAChC,KAAK,CAAC,sBAAsB,CAAC,CAC7B,OAAO,EAAE,CAAC,2EAA2E;SACrF,MAAM,CAAC,SAAC,GAAwB,EAAE,IAAY;YAC7C,IAAM,UAAU,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,qCAAqC;YACrE,IAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;YAC7D,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACZ,IAAM,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACnC,IAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBACnD,mRAAI,cAAA,AAAW,EAAC,GAAG,CAAC,mRAAI,gBAAA,AAAa,EAAC,KAAK,CAAC,EAAE;oBAC5C,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;iBACrB,MAAM;gBACL,oCAAoC;iBACrC;aACF;YACD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAEhB,gFAAgF;QAChF,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,qBAAqB,EAAE;YACpD,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CAC3B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CACtC,OAAO,EAAE,CAAC,sDAAsD;aAChE,KAAK,CAAC,CAAC,EAAE,qBAAqB,CAAC,CACnC,CAAC;SACH;IACH,CAAC;IAEO,eAAA,SAAA,CAAA,KAAK,GAAb;QACE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;IAC1D,CAAC;IAEO,eAAA,SAAA,CAAA,MAAM,GAAd;QACE,IAAM,UAAU,GAAG,IAAI,cAAc,EAAE,CAAC;QACxC,UAAU,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACzD,OAAO,UAAU,CAAC;IACpB,CAAC;IACH,OAAA,cAAC;AAAD,CAAC,AA5ED,IA4EC", "debugId": null}}, {"offset": {"line": 2573, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/trace/internal/utils.js", "sourceRoot": "", "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/node_modules/.pnpm/%40opentelemetry%2Bapi%401.9.0/node_modules/%40opentelemetry/api/src/trace/internal/utils.ts"], "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { TraceState } from '../trace_state';\nimport { TraceStateImpl } from './tracestate-impl';\n\nexport function createTraceState(rawTraceState?: string): TraceState {\n  return new TraceStateImpl(rawTraceState);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;;AAE7C,SAAU,gBAAgB,CAAC,aAAsB;IACrD,OAAO,yQAAI,iBAAc,CAAC,aAAa,CAAC,CAAC;AAC3C,CAAC", "debugId": null}}]}