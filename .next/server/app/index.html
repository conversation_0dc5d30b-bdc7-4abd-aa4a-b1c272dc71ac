<!DOCTYPE html><!--8PgKUn1Dvaei2bIeX4Xol--><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/9624ccc3f3417e4a.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-e1d0418b145739ef.js"/><script src="/_next/static/chunks/9c757011-f0a6af16cd395ae7.js" async=""></script><script src="/_next/static/chunks/276-3d0beebf6e1d4f8a.js" async=""></script><script src="/_next/static/chunks/main-app-e8b0e0ed9ad59043.js" async=""></script><script src="/_next/static/chunks/744-f1fe0b8b1084a52c.js" async=""></script><script src="/_next/static/chunks/app/page-14554aa3cf89c050.js" async=""></script><title>Every Website AI</title><meta name="description" content="AI-powered website generator"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="antialiased"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50"><header class="pt-6 sm:pt-8 pb-4 text-center"><div class="max-w-4xl mx-auto px-4 sm:px-6"><h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">Every Website AI</h1><p class="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-2">Generate any webpage instantly with AI. Just change the URL to describe what you want.</p><div class="mt-6 p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-200 max-w-xl mx-auto"><p class="text-blue-800 font-mono text-sm sm:text-base lg:text-lg break-all">everywebsite.ai/<span class="bg-blue-200 px-1 sm:px-2 py-1 rounded">your-prompt-here</span></p></div></div></header><main class="flex-1 flex items-center justify-center px-4 sm:px-6 py-8 sm:py-12"><div class="max-w-2xl w-full space-y-6 sm:space-y-8"><div class="bg-white rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8 border border-gray-100"><form class="space-y-4 sm:space-y-6"><div><label for="webpage-input" class="block text-base sm:text-lg font-semibold text-gray-800 mb-3">Try it now - enter your prompt:</label><div class="flex items-center border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent transition-all"><span class="pl-3 pr-1 text-gray-500 font-mono text-sm sm:text-base whitespace-nowrap">everywebsite.ai/</span><input id="webpage-input" type="text" placeholder="calculator" class="flex-1 pr-4 py-3 text-sm sm:text-lg border-0 outline-none font-mono bg-transparent" value=""/></div></div><button data-slot="button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md transition-all disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 shrink-0 [&amp;_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-9 px-4 has-[&gt;svg]:px-3 w-full py-3 text-base sm:text-lg font-semibold" type="submit" disabled="">Generate Webpage</button></form></div><div class="text-center"><p class="text-gray-600 mb-4 text-sm sm:text-base">Try these example URLs:</p><div class="flex flex-wrap gap-2 justify-center px-2"><button class="px-2 sm:px-3 py-1 text-xs sm:text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors font-mono break-all">/<!-- -->portfolio-website</button><button class="px-2 sm:px-3 py-1 text-xs sm:text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors font-mono break-all">/<!-- -->restaurant-menu</button><button class="px-2 sm:px-3 py-1 text-xs sm:text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors font-mono break-all">/<!-- -->landing-page-for-saas</button><button class="px-2 sm:px-3 py-1 text-xs sm:text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors font-mono break-all">/<!-- -->personal-blog</button><button class="px-2 sm:px-3 py-1 text-xs sm:text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors font-mono break-all">/<!-- -->event-invitation</button><button class="px-2 sm:px-3 py-1 text-xs sm:text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors font-mono break-all">/<!-- -->product-showcase</button></div></div></div></main><section class="py-12 sm:py-16 bg-white/50"><div class="max-w-4xl mx-auto px-4 sm:px-6"><h2 class="text-2xl sm:text-3xl font-bold text-center text-gray-900 mb-8 sm:mb-12">How it works</h2><div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8"><div class="text-center"><div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4"><span class="text-blue-600 font-bold text-lg">1</span></div><h3 class="font-semibold text-gray-800 mb-2 text-base sm:text-lg">Change the URL</h3><p class="text-gray-600 text-sm sm:text-base px-2">Add your prompt to the URL: everywebsite.ai/your-idea</p></div><div class="text-center"><div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4"><span class="text-blue-600 font-bold text-lg">2</span></div><h3 class="font-semibold text-gray-800 mb-2 text-base sm:text-lg">AI Generates</h3><p class="text-gray-600 text-sm sm:text-base px-2">Our AI instantly creates a complete, responsive webpage</p></div><div class="text-center sm:col-span-2 lg:col-span-1"><div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4"><span class="text-blue-600 font-bold text-lg">3</span></div><h3 class="font-semibold text-gray-800 mb-2 text-base sm:text-lg">That&#x27;s it!</h3><p class="text-gray-600 text-sm sm:text-base px-2">Your custom webpage is ready to use and share</p></div></div></div></section><footer class="py-6 sm:py-8 text-center text-gray-500 text-xs sm:text-sm px-4"><p class="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-0"><span>Powered by<!-- --> <a href="https://dothistask.ai" class="underline hover:text-gray-700">dothistask.ai</a></span><span class="hidden sm:inline"> • </span><a href="https://twitter.com/n3sonline" class="underline hover:text-gray-700">@n3sonline</a></p></footer></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-e1d0418b145739ef.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[5217,[],\"\"]\n3:I[7877,[],\"\"]\n4:I[6536,[],\"ClientPageRoot\"]\n5:I[7762,[\"744\",\"static/chunks/744-f1fe0b8b1084a52c.js\",\"974\",\"static/chunks/app/page-14554aa3cf89c050.js\"],\"default\"]\n8:I[7815,[],\"OutletBoundary\"]\na:I[781,[],\"AsyncMetadataOutlet\"]\nc:I[7815,[],\"ViewportBoundary\"]\ne:I[7815,[],\"MetadataBoundary\"]\nf:\"$Sreact.suspense\"\n11:I[5683,[],\"\"]\n:HL[\"/_next/static/css/9624ccc3f3417e4a.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"8PgKUn1Dvaei2bIeX4Xol\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/9624ccc3f3417e4a.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],null,[\"$\",\"$L8\",null,{\"children\":[\"$L9\",[\"$\",\"$La\",null,{\"promise\":\"$@b\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}],null],[\"$\",\"$Le\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$f\",null,{\"fallback\":null,\"children\":\"$L10\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"6:{}\n7:\"$0:f:0:1:2:children:1:props:children:0:props:params\"\n"])</script><script>self.__next_f.push([1,"d:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,"12:I[1074,[],\"IconMark\"]\nb:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Every Website AI\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"AI-powered website generator\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"$L12\",\"3\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"10:\"$b:metadata\"\n"])</script></body></html>