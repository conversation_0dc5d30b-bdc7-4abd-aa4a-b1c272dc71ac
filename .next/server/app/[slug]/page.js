const CHUNK_PUBLIC_PATH = "server/app/[slug]/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/67149_next_dist_compiled_689436f4._.js");
runtime.loadChunk("server/chunks/ssr/67149_next_dist_esm_802c278a._.js");
runtime.loadChunk("server/chunks/ssr/67149_next_dist_bdbc36ca._.js");
runtime.loadChunk("server/chunks/ssr/8e59f_@opentelemetry_api_build_esm_7b79717d._.js");
runtime.loadChunk("server/chunks/ssr/61dca_@swc_helpers_cjs__interop_require_wildcard_cjs_1f84dac3._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__25e23c09._.js");
runtime.loadChunk("server/chunks/ssr/src_app_a4430781._.js");
runtime.loadChunk("server/chunks/ssr/_dcd7731f._.js");
runtime.loadChunk("server/chunks/ssr/67149_next_dist_client_components_77e2120c._.js");
runtime.loadChunk("server/chunks/ssr/67149_next_dist_client_components_builtin_forbidden_36b68fd0.js");
runtime.loadChunk("server/chunks/ssr/67149_next_dist_client_components_builtin_unauthorized_f6550d80.js");
runtime.loadChunk("server/chunks/ssr/67149_next_dist_client_components_builtin_global-error_719edb83.js");
runtime.loadChunk("server/chunks/ssr/src_app_[slug]_layout_tsx_837c5377._.js");
runtime.loadChunk("server/chunks/ssr/src_app_[slug]_error_tsx_537a196d._.js");
runtime.loadChunk("server/chunks/ssr/src_app_[slug]_loading_tsx_1e25a0dd._.js");
runtime.loadChunk("server/chunks/ssr/src_app_[slug]_not-found_tsx_7941c9e6._.js");
runtime.loadChunk("server/chunks/ssr/67149_next_dbeb785d._.js");
runtime.loadChunk("server/chunks/ssr/98319_zod_v3_82da33c3._.js");
runtime.loadChunk("server/chunks/ssr/76634_zod-to-json-schema_dist_esm_b5c83e65._.js");
runtime.loadChunk("server/chunks/ssr/ada5c_ai_dist_index_mjs_fbcc21ab._.js");
runtime.loadChunk("server/chunks/ssr/3110c_@upstash_redis_e8eed0bf._.js");
runtime.loadChunk("server/chunks/ssr/8484d_tailwind-merge_dist_bundle-mjs_mjs_d0fad42d._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_cdda7a88._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__6ab6e4ba._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/[slug]/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/[slug]/page { GLOBAL_ERROR_MODULE => \"[project]/node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/src/app/[slug]/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/src/app/[slug]/error.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/src/app/[slug]/loading.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_9 => \"[project]/src/app/[slug]/not-found.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_10 => \"[project]/src/app/[slug]/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/[slug]/page { GLOBAL_ERROR_MODULE => \"[project]/node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/node_modules/.pnpm/next@15.4.1_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/src/app/[slug]/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/src/app/[slug]/error.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/src/app/[slug]/loading.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_9 => \"[project]/src/app/[slug]/not-found.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_10 => \"[project]/src/app/[slug]/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
