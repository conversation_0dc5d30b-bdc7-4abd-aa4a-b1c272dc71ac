(()=>{var a={};a.id=182,a.ids=[182],a.modules={225:()=>{},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1087:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,metadata:()=>e});var d=c(8487);c(225);let e={title:"Every Website AI",description:"AI-powered website generator"};function f({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:"antialiased",children:a})})}},1689:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,7910,23)),Promise.resolve().then(c.bind(c,6838))},1752:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5243,23)),Promise.resolve().then(c.t.bind(c,3274,23)),Promise.resolve().then(c.t.bind(c,4588,23)),Promise.resolve().then(c.t.bind(c,8963,23)),Promise.resolve().then(c.t.bind(c,2563,23)),Promise.resolve().then(c.t.bind(c,3947,23)),Promise.resolve().then(c.t.bind(c,7163,23)),Promise.resolve().then(c.t.bind(c,7101,23)),Promise.resolve().then(c.t.bind(c,7331,23))},2166:(a,b,c)=>{"use strict";Object.defineProperty(b,"b",{enumerable:!0,get:function(){return m}});let d=c(4422),e=c(9294),f=c(3033),g=c(4549),h=c(4813),i=c(8030),j=c(7064);c(8529);let k=c(4497),l=c(8627);function m(){let a=e.workAsyncStorage.getStore(),b=f.workUnitAsyncStorage.getStore();if(a){if(b&&"after"===b.phase&&!(0,k.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(a.forceStatic)return o(d.HeadersAdapter.seal(new Headers({})));if(b){if("cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(a.dynamicShouldError)throw Object.defineProperty(new h.StaticGenBailoutError(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(b)switch(b.type){case"prerender":var c=b;let e=n.get(c);if(e)return e;let f=(0,i.makeHangingPromise)(c.renderSignal,"`headers()`");return n.set(c,f),f;case"prerender-client":let j="`headers`";throw Object.defineProperty(new l.InvariantError(`${j} must not be used within a client component. Next.js should be preventing ${j} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":(0,g.postponeWithTracking)(a.route,"headers",b.dynamicTracking);break;case"prerender-legacy":(0,g.throwToInterruptStaticGeneration)("headers",a,b)}(0,g.trackDynamicDataInDynamicRender)(a,b)}return o((0,f.getExpectedRequestStore)("headers").headers)}c(9853);let n=new WeakMap;function o(a){let b=n.get(a);if(b)return b;let c=Promise.resolve(a);return n.set(a,c),Object.defineProperties(c,{append:{value:a.append.bind(a)},delete:{value:a.delete.bind(a)},get:{value:a.get.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},getSetCookie:{value:a.getSetCookie.bind(a)},forEach:{value:a.forEach.bind(a)},keys:{value:a.keys.bind(a)},values:{value:a.values.bind(a)},entries:{value:a.entries.bind(a)},[Symbol.iterator]:{value:a[Symbol.iterator].bind(a)}}),c}(0,j.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})})},2967:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(2004).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/not-found.tsx","default")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3320:()=>{},3390:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"forbidden",{enumerable:!0,get:function(){return d}}),c(3974).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},3780:(a,b,c)=>{Promise.resolve().then(c.bind(c,9741))},3837:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(8487);function e({children:a}){return(0,d.jsx)("div",{style:{width:"100%",height:"100vh",overflow:"auto"},children:a})}},3873:a=>{"use strict";a.exports=require("path")},4105:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(9861),e=c(9351);function f(){return(0,d.jsx)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-8",children:(0,d.jsxs)("div",{className:"text-center space-y-6 max-w-md",children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center",children:(0,d.jsx)("svg",{className:"w-8 h-8 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 9.75a3 3 0 11-6 0 3 3 0 016 0z"})})}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:"Page Not Found"}),(0,d.jsx)("p",{className:"text-gray-600",children:"The page you're looking for doesn't exist or the slug contains invalid characters."}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Try using only letters, numbers, hyphens, and underscores."})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,d.jsx)(e.$,{onClick:()=>window.location.href="/",className:"px-6",children:"Go Home"}),(0,d.jsx)(e.$,{variant:"outline",onClick:()=>window.history.back(),className:"px-6",children:"Go Back"})]}),(0,d.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,d.jsx)("p",{className:"text-sm text-gray-500 mb-3",children:"Need help? Reach out:"}),(0,d.jsxs)("a",{href:"https://twitter.com/n3sonline",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors",children:[(0,d.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})}),"Tweet @n3sonline"]})]})]}),(0,d.jsxs)("div",{className:"mt-8 text-xs text-gray-500",children:["Powered by"," ",(0,d.jsx)("a",{href:"https://dothistask.ai",className:"underline",children:"dothistask.ai"})]})]})})}},4373:a=>{"use strict";let b="undefined"!=typeof Buffer,c=/"(?:_|\\u005[Ff])(?:_|\\u005[Ff])(?:p|\\u0070)(?:r|\\u0072)(?:o|\\u006[Ff])(?:t|\\u0074)(?:o|\\u006[Ff])(?:_|\\u005[Ff])(?:_|\\u005[Ff])"\s*:/,d=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/;function e(a,e,g){null==g&&null!==e&&"object"==typeof e&&(g=e,e=void 0),b&&Buffer.isBuffer(a)&&(a=a.toString()),a&&65279===a.charCodeAt(0)&&(a=a.slice(1));let h=JSON.parse(a,e);if(null===h||"object"!=typeof h)return h;let i=g&&g.protoAction||"error",j=g&&g.constructorAction||"error";if("ignore"===i&&"ignore"===j)return h;if("ignore"!==i&&"ignore"!==j){if(!1===c.test(a)&&!1===d.test(a))return h}else if("ignore"!==i&&"ignore"===j){if(!1===c.test(a))return h}else if(!1===d.test(a))return h;return f(h,{protoAction:i,constructorAction:j,safe:g&&g.safe})}function f(a,{protoAction:b="error",constructorAction:c="error",safe:d}={}){let e=[a];for(;e.length;){let a=e;for(let f of(e=[],a)){if("ignore"!==b&&Object.prototype.hasOwnProperty.call(f,"__proto__")){if(!0===d)return null;if("error"===b)throw SyntaxError("Object contains forbidden prototype property");delete f.__proto__}if("ignore"!==c&&Object.prototype.hasOwnProperty.call(f,"constructor")&&Object.prototype.hasOwnProperty.call(f.constructor,"prototype")){if(!0===d)return null;if("error"===c)throw SyntaxError("Object contains forbidden prototype property");delete f.constructor}for(let a in f){let b=f[a];b&&"object"==typeof b&&e.push(b)}}}return a}function g(a,b,c){let d=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return e(a,b,c)}finally{Error.stackTraceLimit=d}}a.exports=g,a.exports.default=g,a.exports.parse=g,a.exports.safeParse=function(a,b){let c=Error.stackTraceLimit;Error.stackTraceLimit=0;try{return e(a,b,{safe:!0})}catch(a){return null}finally{Error.stackTraceLimit=c}},a.exports.scan=f},4539:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(8487);function e(){return(0,d.jsx)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,d.jsxs)("div",{className:"text-center space-y-6 p-8",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto"}),(0,d.jsx)("div",{className:"absolute inset-0 w-16 h-16 border-4 border-transparent border-r-indigo-400 rounded-full animate-spin mx-auto",style:{animationDirection:"reverse",animationDuration:"1.5s"}})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:"Generating your page now..."}),(0,d.jsx)("p",{className:"text-gray-600 max-w-md mx-auto",children:"Our AI is crafting a custom webpage just for you. This usually takes a few seconds."})]}),(0,d.jsxs)("div",{className:"flex space-x-2 justify-center",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-bounce"}),(0,d.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,d.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,d.jsxs)("div",{className:"mt-8 text-xs text-gray-500",children:["Powered by"," ",(0,d.jsx)("a",{href:"https://dothistask.ai",className:"underline",children:"dothistask.ai"})]})]})})}},5244:(a,b,c)=>{"use strict";c.d(b,{E3:()=>h,Yx:()=>i,OV:()=>g});let d=require("@aws-sdk/client-s3");var e=c(5872);let f=new d.S3Client({region:e._.AWS_REGION,credentials:{accessKeyId:e._.AWS_ACCESS_KEY_ID,secretAccessKey:e._.AWS_SECRET_ACCESS_KEY}});async function g(a,b){let c=`websites/${a}.html`,g="INAPPROPRIATE_PROMPT_DETECTED"===b.trim(),h=new d.PutObjectCommand({Bucket:e._.AWS_S3_BUCKET_NAME,Key:c,Body:b,ContentType:g?"text/plain":"text/html",CacheControl:"max-age=3600",Metadata:{inappropriate:g?"true":"false"}});try{return await f.send(h),c}catch(a){throw console.error("Error uploading to S3:",a),Error("Failed to upload HTML to S3")}}async function h(a){let b=`websites/${a}.html`,c=new d.GetObjectCommand({Bucket:e._.AWS_S3_BUCKET_NAME,Key:b});try{let a=await f.send(c);if(a.Body)return await a.Body.transformToString();return null}catch(a){if(a?.name==="NoSuchKey")return null;throw console.error("Error fetching from S3:",a),Error("Failed to fetch HTML from S3")}}async function i(a=5){let b=new d.ListObjectsV2Command({Bucket:e._.AWS_S3_BUCKET_NAME,Prefix:"websites/",MaxKeys:50});try{let c=await f.send(b);if(!c.Contents)return[];return c.Contents.filter(a=>a.Key&&a.Key.endsWith(".html")&&a.LastModified&&a.Size&&a.Size>100).sort((a,b)=>{let c=a.LastModified?.getTime()||0;return(b.LastModified?.getTime()||0)-c}).slice(0,a).map(a=>{let b=a.Key.replace("websites/","").replace(".html",""),c=b.replace(/[-_]/g," ").replace(/\b\w/g,a=>a.toUpperCase());return{slug:b,lastModified:a.LastModified,title:c}})}catch(a){return console.error("Error fetching recent pages from S3:",a),[]}}},5630:(a,b,c)=>{"use strict";let d;c.r(b),c.d(b,{default:()=>hR,generateMetadata:()=>hS});var e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A=c(8487),B="vercel.ai.error",C=Symbol.for(B),D=class a extends Error{constructor({name:a,message:b,cause:c}){super(b),this[e]=!0,this.name=a,this.cause=c}static isInstance(b){return a.hasMarker(b,B)}static hasMarker(a,b){let c=Symbol.for(b);return null!=a&&"object"==typeof a&&c in a&&"boolean"==typeof a[c]&&!0===a[c]}};e=C;var E=D,F="AI_APICallError",G=`vercel.ai.error.${F}`,H=Symbol.for(G),I=class extends E{constructor({message:a,url:b,requestBodyValues:c,statusCode:d,responseHeaders:e,responseBody:g,cause:h,isRetryable:i=null!=d&&(408===d||409===d||429===d||d>=500),data:j}){super({name:F,message:a,cause:h}),this[f]=!0,this.url=b,this.requestBodyValues=c,this.statusCode=d,this.responseHeaders=e,this.responseBody=g,this.isRetryable=i,this.data=j}static isInstance(a){return E.hasMarker(a,G)}};f=H;var J="AI_EmptyResponseBodyError",K=`vercel.ai.error.${J}`,L=Symbol.for(K),M=class extends E{constructor({message:a="Empty response body"}={}){super({name:J,message:a}),this[g]=!0}static isInstance(a){return E.hasMarker(a,K)}};function N(a){return null==a?"unknown error":"string"==typeof a?a:a instanceof Error?a.message:JSON.stringify(a)}g=L;var O="AI_InvalidArgumentError",P=`vercel.ai.error.${O}`,Q=Symbol.for(P),R=class extends E{constructor({message:a,cause:b,argument:c}){super({name:O,message:a,cause:b}),this[h]=!0,this.argument=c}static isInstance(a){return E.hasMarker(a,P)}};h=Q;var S="AI_InvalidPromptError",T=`vercel.ai.error.${S}`,U=Symbol.for(T),V=class extends E{constructor({prompt:a,message:b,cause:c}){super({name:S,message:`Invalid prompt: ${b}`,cause:c}),this[i]=!0,this.prompt=a}static isInstance(a){return E.hasMarker(a,T)}};i=U,Symbol.for("vercel.ai.error.AI_InvalidResponseDataError");var W="AI_JSONParseError",X=`vercel.ai.error.${W}`,Y=Symbol.for(X),Z=class extends E{constructor({text:a,cause:b}){super({name:W,message:`JSON parsing failed: Text: ${a}.
Error message: ${N(b)}`,cause:b}),this[j]=!0,this.text=a}static isInstance(a){return E.hasMarker(a,X)}};j=Y;var $="AI_LoadAPIKeyError",_=`vercel.ai.error.${$}`,aa=Symbol.for(_),ab=class extends E{constructor({message:a}){super({name:$,message:a}),this[k]=!0}static isInstance(a){return E.hasMarker(a,_)}};k=aa,Symbol.for("vercel.ai.error.AI_LoadSettingError"),Symbol.for("vercel.ai.error.AI_NoContentGeneratedError");var ac="AI_NoSuchModelError",ad=`vercel.ai.error.${ac}`,ae=Symbol.for(ad),af=class extends E{constructor({errorName:a=ac,modelId:b,modelType:c,message:d=`No such ${c}: ${b}`}){super({name:a,message:d}),this[l]=!0,this.modelId=b,this.modelType=c}static isInstance(a){return E.hasMarker(a,ad)}};l=ae,Symbol.for("vercel.ai.error.AI_TooManyEmbeddingValuesForCallError");var ag="AI_TypeValidationError",ah=`vercel.ai.error.${ag}`,ai=Symbol.for(ah),aj=class a extends E{constructor({value:a,cause:b}){super({name:ag,message:`Type validation failed: Value: ${JSON.stringify(a)}.
Error message: ${N(b)}`,cause:b}),this[m]=!0,this.value=a}static isInstance(a){return E.hasMarker(a,ah)}static wrap({value:b,cause:c}){return a.isInstance(c)&&c.value===b?c:new a({value:b,cause:c})}};m=ai;var ak="AI_UnsupportedFunctionalityError",al=`vercel.ai.error.${ak}`,am=Symbol.for(al),an=class extends E{constructor({functionality:a,message:b=`'${a}' functionality not supported.`}){super({name:ak,message:b}),this[n]=!0,this.functionality=a}static isInstance(a){return E.hasMarker(a,al)}};n=am;var ao=c(4373);async function ap(a){return null==a?Promise.resolve():new Promise(b=>setTimeout(b,a))}function aq(a){let b={};return a.headers.forEach((a,c)=>{b[c]=a}),b}var ar=({prefix:a,size:b=16,alphabet:c="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",separator:d="-"}={})=>{let e=((a,b=21)=>(c=b)=>{let d="",e=0|c;for(;e--;)d+=a[Math.random()*a.length|0];return d})(c,b);if(null==a)return e;if(c.includes(d))throw new R({argument:"separator",message:`The separator "${d}" must not be part of the alphabet "${c}".`});return b=>`${a}${d}${e(b)}`};function as(a){return a instanceof Error&&("AbortError"===a.name||"TimeoutError"===a.name)}ar();var at=Symbol.for("vercel.ai.validator");function au({value:a,schema:b}){var c;let d="object"==typeof b&&null!==b&&at in b&&!0===b[at]&&"validate"in b?b:(c=b,{[at]:!0,validate:a=>{let b=c.safeParse(a);return b.success?{success:!0,value:b.data}:{success:!1,error:b.error}}});try{if(null==d.validate)return{success:!0,value:a};let b=d.validate(a);if(b.success)return b;return{success:!1,error:aj.wrap({value:a,cause:b.error})}}catch(b){return{success:!1,error:aj.wrap({value:a,cause:b})}}}function av({text:a,schema:b}){try{let c=ao.parse(a);if(null==b)return{success:!0,value:c,rawValue:c};let d=au({value:c,schema:b});return d.success?{...d,rawValue:c}:d}catch(b){return{success:!1,error:Z.isInstance(b)?b:new Z({text:a,cause:b})}}}var aw=()=>globalThis.fetch,ax=async({url:a,headers:b,body:c,failedResponseHandler:d,successfulResponseHandler:e,abortSignal:f,fetch:g})=>ay({url:a,headers:{"Content-Type":"application/json",...b},body:{content:JSON.stringify(c),values:c},failedResponseHandler:d,successfulResponseHandler:e,abortSignal:f,fetch:g}),ay=async({url:a,headers:b={},body:c,successfulResponseHandler:d,failedResponseHandler:e,abortSignal:f,fetch:g=aw()})=>{try{let h=await g(a,{method:"POST",headers:Object.fromEntries(Object.entries(b).filter(([a,b])=>null!=b)),body:c.content,signal:f}),i=aq(h);if(!h.ok){let b;try{b=await e({response:h,url:a,requestBodyValues:c.values})}catch(b){if(as(b)||I.isInstance(b))throw b;throw new I({message:"Failed to process error response",cause:b,statusCode:h.status,url:a,responseHeaders:i,requestBodyValues:c.values})}throw b.value}try{return await d({response:h,url:a,requestBodyValues:c.values})}catch(b){if(b instanceof Error&&(as(b)||I.isInstance(b)))throw b;throw new I({message:"Failed to process successful response",cause:b,statusCode:h.status,url:a,responseHeaders:i,requestBodyValues:c.values})}}catch(b){if(as(b))throw b;if(b instanceof TypeError&&"fetch failed"===b.message){let d=b.cause;if(null!=d)throw new I({message:`Cannot connect to API: ${d.message}`,cause:d,url:a,requestBodyValues:c.values,isRetryable:!0})}throw b}};async function az(a){return"function"==typeof a&&(a=a()),Promise.resolve(a)}var{btoa:aA,atob:aB}=globalThis;function aC(a){let b=aB(a.replace(/-/g,"+").replace(/_/g,"/"));return Uint8Array.from(b,a=>a.codePointAt(0))}function aD(a){let b="";for(let c=0;c<a.length;c++)b+=String.fromCodePoint(a[c]);return aA(b)}var aE=c(9662),aF=(({errorSchema:a,errorToMessage:b,isRetryable:c})=>async({response:d,url:e,requestBodyValues:f})=>{let g=await d.text(),h=aq(d);if(""===g.trim())return{responseHeaders:h,value:new I({message:d.statusText,url:e,requestBodyValues:f,statusCode:d.status,responseHeaders:h,responseBody:g,isRetryable:null==c?void 0:c(d)})};try{let i=function({text:a,schema:b}){try{let c=ao.parse(a);if(null==b)return c;return function({value:a,schema:b}){let c=au({value:a,schema:b});if(!c.success)throw aj.wrap({value:a,cause:c.error});return c.value}({value:c,schema:b})}catch(b){if(Z.isInstance(b)||aj.isInstance(b))throw b;throw new Z({text:a,cause:b})}}({text:g,schema:a});return{responseHeaders:h,value:new I({message:b(i),url:e,requestBodyValues:f,statusCode:d.status,responseHeaders:h,responseBody:g,data:i,isRetryable:null==c?void 0:c(d,i)})}}catch(a){return{responseHeaders:h,value:new I({message:d.statusText,url:e,requestBodyValues:f,statusCode:d.status,responseHeaders:h,responseBody:g,isRetryable:null==c?void 0:c(d)})}}})({errorSchema:aE.Ik({type:aE.eu("error"),error:aE.Ik({type:aE.Yj(),message:aE.Yj()})}),errorToMessage:a=>a.error.message});function aG(a){switch(a){case"end_turn":case"stop_sequence":return"stop";case"tool_use":return"tool-calls";case"max_tokens":return"length";default:return"unknown"}}var aH=class{constructor(a,b,c){this.specificationVersion="v1",this.defaultObjectGenerationMode="tool",this.modelId=a,this.settings=b,this.config=c}supportsUrl(a){return"https:"===a.protocol}get provider(){return this.config.provider}get supportsImageUrls(){return this.config.supportsImageUrls}async getArgs({mode:a,prompt:b,maxTokens:c=4096,temperature:d,topP:e,topK:f,frequencyPenalty:g,presencePenalty:h,stopSequences:i,responseFormat:j,seed:k,providerMetadata:l}){var m,n,o;let p=a.type,q=[];null!=g&&q.push({type:"unsupported-setting",setting:"frequencyPenalty"}),null!=h&&q.push({type:"unsupported-setting",setting:"presencePenalty"}),null!=k&&q.push({type:"unsupported-setting",setting:"seed"}),null!=j&&"text"!==j.type&&q.push({type:"unsupported-setting",setting:"responseFormat",details:"JSON response format is not supported."});let{prompt:r,betas:s}=function({prompt:a,sendReasoning:b,warnings:c}){var d,e,f,g;let h,i=new Set,j=function(a){let b,c=[];for(let d of a){let{role:a}=d;switch(a){case"system":(null==b?void 0:b.type)!=="system"&&(b={type:"system",messages:[]},c.push(b)),b.messages.push(d);break;case"assistant":(null==b?void 0:b.type)!=="assistant"&&(b={type:"assistant",messages:[]},c.push(b)),b.messages.push(d);break;case"user":case"tool":(null==b?void 0:b.type)!=="user"&&(b={type:"user",messages:[]},c.push(b)),b.messages.push(d);break;default:throw Error(`Unsupported role: ${a}`)}}return c}(a),k=[];function l(a){var b;let c=null==a?void 0:a.anthropic;return null!=(b=null==c?void 0:c.cacheControl)?b:null==c?void 0:c.cache_control}for(let a=0;a<j.length;a++){let m=j[a],n=a===j.length-1,o=m.type;switch(o){case"system":if(null!=h)throw new an({functionality:"Multiple system messages that are separated by user/assistant messages"});h=m.messages.map(({content:a,providerMetadata:b})=>({type:"text",text:a,cache_control:l(b)}));break;case"user":{let a=[];for(let b of m.messages){let{role:c,content:g}=b;switch(c){case"user":for(let c=0;c<g.length;c++){let f=g[c],h=c===g.length-1,j=null!=(d=l(f.providerMetadata))?d:h?l(b.providerMetadata):void 0;switch(f.type){case"text":a.push({type:"text",text:f.text,cache_control:j});break;case"image":a.push({type:"image",source:f.image instanceof URL?{type:"url",url:f.image.toString()}:{type:"base64",media_type:null!=(e=f.mimeType)?e:"image/jpeg",data:aD(f.image)},cache_control:j});break;case"file":if("application/pdf"!==f.mimeType)throw new an({functionality:"Non-PDF files in user messages"});i.add("pdfs-2024-09-25"),a.push({type:"document",source:f.data instanceof URL?{type:"url",url:f.data.toString()}:{type:"base64",media_type:"application/pdf",data:f.data},cache_control:j})}}break;case"tool":for(let c=0;c<g.length;c++){let d=g[c],e=c===g.length-1,h=null!=(f=l(d.providerMetadata))?f:e?l(b.providerMetadata):void 0,i=null!=d.content?d.content.map(a=>{var b;switch(a.type){case"text":return{type:"text",text:a.text,cache_control:void 0};case"image":return{type:"image",source:{type:"base64",media_type:null!=(b=a.mimeType)?b:"image/jpeg",data:a.data},cache_control:void 0}}}):JSON.stringify(d.result);a.push({type:"tool_result",tool_use_id:d.toolCallId,content:i,is_error:d.isError,cache_control:h})}break;default:throw Error(`Unsupported role: ${c}`)}}k.push({role:"user",content:a});break}case"assistant":{let a=[];for(let d=0;d<m.messages.length;d++){let e=m.messages[d],f=d===m.messages.length-1,{content:h}=e;for(let d=0;d<h.length;d++){let i=h[d],j=d===h.length-1,k=null!=(g=l(i.providerMetadata))?g:j?l(e.providerMetadata):void 0;switch(i.type){case"text":a.push({type:"text",text:n&&f&&j?i.text.trim():i.text,cache_control:k});break;case"reasoning":b?a.push({type:"thinking",thinking:i.text,signature:i.signature,cache_control:k}):c.push({type:"other",message:"sending reasoning content is disabled for this model"});break;case"redacted-reasoning":a.push({type:"redacted_thinking",data:i.data,cache_control:k});break;case"tool-call":a.push({type:"tool_use",id:i.toolCallId,name:i.toolName,input:i.args,cache_control:k})}}}k.push({role:"assistant",content:a});break}default:throw Error(`Unsupported type: ${o}`)}}return{prompt:{system:h,messages:k},betas:i}}({prompt:b,sendReasoning:null==(m=this.settings.sendReasoning)||m,warnings:q}),t=function({provider:a,providerOptions:b,schema:c}){if((null==b?void 0:b[a])==null)return;let d=au({value:b[a],schema:c});if(!d.success)throw new R({argument:"providerOptions",message:`invalid ${a} provider options`,cause:d.error});return d.value}({provider:"anthropic",providerOptions:l,schema:aK}),u=(null==(n=null==t?void 0:t.thinking)?void 0:n.type)==="enabled",v=null==(o=null==t?void 0:t.thinking)?void 0:o.budgetTokens,w={model:this.modelId,max_tokens:c,temperature:d,top_k:f,top_p:e,stop_sequences:i,...u&&{thinking:{type:"enabled",budget_tokens:v}},system:r.system,messages:r.messages};if(u){if(null==v)throw new an({functionality:"thinking requires a budget"});null!=w.temperature&&(w.temperature=void 0,q.push({type:"unsupported-setting",setting:"temperature",details:"temperature is not supported when thinking is enabled"})),null!=f&&(w.top_k=void 0,q.push({type:"unsupported-setting",setting:"topK",details:"topK is not supported when thinking is enabled"})),null!=e&&(w.top_p=void 0,q.push({type:"unsupported-setting",setting:"topP",details:"topP is not supported when thinking is enabled"})),w.max_tokens=c+v}switch(p){case"regular":{let{tools:b,tool_choice:c,toolWarnings:d,betas:e}=function(a){var b;let c=(null==(b=a.tools)?void 0:b.length)?a.tools:void 0,d=[],e=new Set;if(null==c)return{tools:void 0,tool_choice:void 0,toolWarnings:d,betas:e};let f=[];for(let a of c)switch(a.type){case"function":f.push({name:a.name,description:a.description,input_schema:a.parameters});break;case"provider-defined":switch(a.id){case"anthropic.computer_20250124":e.add("computer-use-2025-01-24"),f.push({name:a.name,type:"computer_20250124",display_width_px:a.args.displayWidthPx,display_height_px:a.args.displayHeightPx,display_number:a.args.displayNumber});break;case"anthropic.computer_20241022":e.add("computer-use-2024-10-22"),f.push({name:a.name,type:"computer_20241022",display_width_px:a.args.displayWidthPx,display_height_px:a.args.displayHeightPx,display_number:a.args.displayNumber});break;case"anthropic.text_editor_20250124":e.add("computer-use-2025-01-24"),f.push({name:a.name,type:"text_editor_20250124"});break;case"anthropic.text_editor_20241022":e.add("computer-use-2024-10-22"),f.push({name:a.name,type:"text_editor_20241022"});break;case"anthropic.bash_20250124":e.add("computer-use-2025-01-24"),f.push({name:a.name,type:"bash_20250124"});break;case"anthropic.bash_20241022":e.add("computer-use-2024-10-22"),f.push({name:a.name,type:"bash_20241022"});break;default:d.push({type:"unsupported-tool",tool:a})}break;default:d.push({type:"unsupported-tool",tool:a})}let g=a.toolChoice;if(null==g)return{tools:f,tool_choice:void 0,toolWarnings:d,betas:e};let h=g.type;switch(h){case"auto":return{tools:f,tool_choice:{type:"auto"},toolWarnings:d,betas:e};case"required":return{tools:f,tool_choice:{type:"any"},toolWarnings:d,betas:e};case"none":return{tools:void 0,tool_choice:void 0,toolWarnings:d,betas:e};case"tool":return{tools:f,tool_choice:{type:"tool",name:g.toolName},toolWarnings:d,betas:e};default:throw new an({functionality:`Unsupported tool choice type: ${h}`})}}(a);return{args:{...w,tools:b,tool_choice:c},warnings:[...q,...d],betas:new Set([...s,...e])}}case"object-json":throw new an({functionality:"json-mode object generation"});case"object-tool":{let{name:b,description:c,parameters:d}=a.tool;return{args:{...w,tools:[{name:b,description:c,input_schema:d}],tool_choice:{type:"tool",name:b}},warnings:q,betas:s}}default:throw Error(`Unsupported type: ${p}`)}}async getHeaders({betas:a,headers:b}){return function(...a){return a.reduce((a,b)=>({...a,...null!=b?b:{}}),{})}(await az(this.config.headers),a.size>0?{"anthropic-beta":Array.from(a).join(",")}:{},b)}buildRequestUrl(a){var b,c,d;return null!=(d=null==(c=(b=this.config).buildRequestUrl)?void 0:c.call(b,this.config.baseURL,a))?d:`${this.config.baseURL}/messages`}transformRequestBody(a){var b,c,d;return null!=(d=null==(c=(b=this.config).transformRequestBody)?void 0:c.call(b,a))?d:a}async doGenerate(a){var b,c,d,e;let f,{args:g,warnings:h,betas:i}=await this.getArgs(a),{responseHeaders:j,value:k,rawValue:l}=await ax({url:this.buildRequestUrl(!1),headers:await this.getHeaders({betas:i,headers:a.headers}),body:this.transformRequestBody(g),failedResponseHandler:aF,successfulResponseHandler:async({response:a,url:b,requestBodyValues:c})=>{let d=await a.text(),e=av({text:d,schema:aI}),f=aq(a);if(!e.success)throw new I({message:"Invalid JSON response",cause:e.error,statusCode:a.status,responseHeaders:f,responseBody:d,url:b,requestBodyValues:c});return{responseHeaders:f,value:e.value,rawValue:e.rawValue}},abortSignal:a.abortSignal,fetch:this.config.fetch}),{messages:m,...n}=g,o="";for(let a of k.content)"text"===a.type&&(o+=a.text);if(k.content.some(a=>"tool_use"===a.type))for(let a of(f=[],k.content))"tool_use"===a.type&&f.push({toolCallType:"function",toolCallId:a.id,toolName:a.name,args:JSON.stringify(a.input)});let p=k.content.filter(a=>"redacted_thinking"===a.type||"thinking"===a.type).map(a=>"thinking"===a.type?{type:"text",text:a.thinking,signature:a.signature}:{type:"redacted",data:a.data});return{text:o,reasoning:p.length>0?p:void 0,toolCalls:f,finishReason:aG(k.stop_reason),usage:{promptTokens:k.usage.input_tokens,completionTokens:k.usage.output_tokens},rawCall:{rawPrompt:m,rawSettings:n},rawResponse:{headers:j,body:l},response:{id:null!=(b=k.id)?b:void 0,modelId:null!=(c=k.model)?c:void 0},warnings:h,providerMetadata:{anthropic:{cacheCreationInputTokens:null!=(d=k.usage.cache_creation_input_tokens)?d:null,cacheReadInputTokens:null!=(e=k.usage.cache_read_input_tokens)?e:null}},request:{body:JSON.stringify(g)}}}async doStream(a){let b,c,{args:d,warnings:e,betas:f}=await this.getArgs(a),g={...d,stream:!0},{responseHeaders:h,value:i}=await ax({url:this.buildRequestUrl(!0),headers:await this.getHeaders({betas:f,headers:a.headers}),body:this.transformRequestBody(g),failedResponseHandler:aF,successfulResponseHandler:async({response:a})=>{let b=aq(a);if(null==a.body)throw new M({});return{responseHeaders:b,value:a.body.pipeThrough(new TextDecoderStream).pipeThrough(function(){let a,b,c,d="",e=[];function f(a,b){if(""===a)return void g(b);if(a.startsWith(":"))return;let c=a.indexOf(":");if(-1===c)return void h(a,"");let d=a.slice(0,c),e=c+1;h(d,e<a.length&&" "===a[e]?a.slice(e+1):a.slice(e))}function g(d){e.length>0&&(d.enqueue({event:a,data:e.join("\n"),id:b,retry:c}),e=[],a=void 0,c=void 0)}function h(d,f){switch(d){case"event":a=f;break;case"data":e.push(f);break;case"id":b=f;break;case"retry":let g=parseInt(f,10);isNaN(g)||(c=g)}}return new TransformStream({transform(a,b){let{lines:c,incompleteLine:e}=function(a,b){let c=[],d=a;for(let a=0;a<b.length;){let e=b[a++];"\n"===e?(c.push(d),d=""):"\r"===e?(c.push(d),d="","\n"===b[a]&&a++):d+=e}return{lines:c,incompleteLine:d}}(d,a);d=e;for(let a=0;a<c.length;a++)f(c[a],b)},flush(a){f(d,a),g(a)}})}()).pipeThrough(new TransformStream({transform({data:a},b){"[DONE]"!==a&&b.enqueue(av({text:a,schema:aJ}))}}))}},abortSignal:a.abortSignal,fetch:this.config.fetch}),{messages:j,...k}=d,l="unknown",m={promptTokens:NaN,completionTokens:NaN},n={};return{stream:i.pipeThrough(new TransformStream({transform(a,d){var e,f,g,h;if(!a.success)return void d.enqueue({type:"error",error:a.error});let i=a.value;switch(i.type){case"ping":return;case"content_block_start":{let a=i.content_block.type;switch(c=a,a){case"text":case"thinking":return;case"redacted_thinking":return void d.enqueue({type:"redacted-reasoning",data:i.content_block.data});case"tool_use":n[i.index]={toolCallId:i.content_block.id,toolName:i.content_block.name,jsonText:""};return;default:throw Error(`Unsupported content block type: ${a}`)}}case"content_block_stop":if(null!=n[i.index]){let a=n[i.index];d.enqueue({type:"tool-call",toolCallType:"function",toolCallId:a.toolCallId,toolName:a.toolName,args:a.jsonText}),delete n[i.index]}c=void 0;return;case"content_block_delta":{let a=i.delta.type;switch(a){case"text_delta":return void d.enqueue({type:"text-delta",textDelta:i.delta.text});case"thinking_delta":return void d.enqueue({type:"reasoning",textDelta:i.delta.thinking});case"signature_delta":"thinking"===c&&d.enqueue({type:"reasoning-signature",signature:i.delta.signature});return;case"input_json_delta":{let a=n[i.index];d.enqueue({type:"tool-call-delta",toolCallType:"function",toolCallId:a.toolCallId,toolName:a.toolName,argsTextDelta:i.delta.partial_json}),a.jsonText+=i.delta.partial_json;return}default:throw Error(`Unsupported delta type: ${a}`)}}case"message_start":m.promptTokens=i.message.usage.input_tokens,m.completionTokens=i.message.usage.output_tokens,b={anthropic:{cacheCreationInputTokens:null!=(e=i.message.usage.cache_creation_input_tokens)?e:null,cacheReadInputTokens:null!=(f=i.message.usage.cache_read_input_tokens)?f:null}},d.enqueue({type:"response-metadata",id:null!=(g=i.message.id)?g:void 0,modelId:null!=(h=i.message.model)?h:void 0});return;case"message_delta":m.completionTokens=i.usage.output_tokens,l=aG(i.delta.stop_reason);return;case"message_stop":return void d.enqueue({type:"finish",finishReason:l,usage:m,providerMetadata:b});case"error":return void d.enqueue({type:"error",error:i.error});default:throw Error(`Unsupported chunk type: ${i}`)}}})),rawCall:{rawPrompt:j,rawSettings:k},rawResponse:{headers:h},warnings:e,request:{body:JSON.stringify(g)}}}},aI=aE.Ik({type:aE.eu("message"),id:aE.Yj().nullish(),model:aE.Yj().nullish(),content:aE.YO(aE.gM("type",[aE.Ik({type:aE.eu("text"),text:aE.Yj()}),aE.Ik({type:aE.eu("thinking"),thinking:aE.Yj(),signature:aE.Yj()}),aE.Ik({type:aE.eu("redacted_thinking"),data:aE.Yj()}),aE.Ik({type:aE.eu("tool_use"),id:aE.Yj(),name:aE.Yj(),input:aE.L5()})])),stop_reason:aE.Yj().nullish(),usage:aE.Ik({input_tokens:aE.ai(),output_tokens:aE.ai(),cache_creation_input_tokens:aE.ai().nullish(),cache_read_input_tokens:aE.ai().nullish()})}),aJ=aE.gM("type",[aE.Ik({type:aE.eu("message_start"),message:aE.Ik({id:aE.Yj().nullish(),model:aE.Yj().nullish(),usage:aE.Ik({input_tokens:aE.ai(),output_tokens:aE.ai(),cache_creation_input_tokens:aE.ai().nullish(),cache_read_input_tokens:aE.ai().nullish()})})}),aE.Ik({type:aE.eu("content_block_start"),index:aE.ai(),content_block:aE.gM("type",[aE.Ik({type:aE.eu("text"),text:aE.Yj()}),aE.Ik({type:aE.eu("thinking"),thinking:aE.Yj()}),aE.Ik({type:aE.eu("tool_use"),id:aE.Yj(),name:aE.Yj()}),aE.Ik({type:aE.eu("redacted_thinking"),data:aE.Yj()})])}),aE.Ik({type:aE.eu("content_block_delta"),index:aE.ai(),delta:aE.gM("type",[aE.Ik({type:aE.eu("input_json_delta"),partial_json:aE.Yj()}),aE.Ik({type:aE.eu("text_delta"),text:aE.Yj()}),aE.Ik({type:aE.eu("thinking_delta"),thinking:aE.Yj()}),aE.Ik({type:aE.eu("signature_delta"),signature:aE.Yj()})])}),aE.Ik({type:aE.eu("content_block_stop"),index:aE.ai()}),aE.Ik({type:aE.eu("error"),error:aE.Ik({type:aE.Yj(),message:aE.Yj()})}),aE.Ik({type:aE.eu("message_delta"),delta:aE.Ik({stop_reason:aE.Yj().nullish()}),usage:aE.Ik({output_tokens:aE.ai()})}),aE.Ik({type:aE.eu("message_stop")}),aE.Ik({type:aE.eu("ping")})]),aK=aE.Ik({thinking:aE.Ik({type:aE.KC([aE.eu("enabled"),aE.eu("disabled")]),budgetTokens:aE.ai().optional()}).optional()}),aL=aE.Ik({command:aE.Yj(),restart:aE.zM().optional()}),aM=aE.Ik({command:aE.Yj(),restart:aE.zM().optional()}),aN=aE.Ik({command:aE.k5(["view","create","str_replace","insert","undo_edit"]),path:aE.Yj(),file_text:aE.Yj().optional(),insert_line:aE.ai().int().optional(),new_str:aE.Yj().optional(),old_str:aE.Yj().optional(),view_range:aE.YO(aE.ai().int()).optional()}),aO=aE.Ik({command:aE.k5(["view","create","str_replace","insert","undo_edit"]),path:aE.Yj(),file_text:aE.Yj().optional(),insert_line:aE.ai().int().optional(),new_str:aE.Yj().optional(),old_str:aE.Yj().optional(),view_range:aE.YO(aE.ai().int()).optional()}),aP=aE.Ik({action:aE.k5(["key","type","mouse_move","left_click","left_click_drag","right_click","middle_click","double_click","screenshot","cursor_position"]),coordinate:aE.YO(aE.ai().int()).optional(),text:aE.Yj().optional()}),aQ=aE.Ik({action:aE.k5(["key","hold_key","type","cursor_position","mouse_move","left_mouse_down","left_mouse_up","left_click","left_click_drag","right_click","middle_click","double_click","triple_click","scroll","wait","screenshot"]),coordinate:aE.PV([aE.ai().int(),aE.ai().int()]).optional(),duration:aE.ai().optional(),scroll_amount:aE.ai().optional(),scroll_direction:aE.k5(["up","down","left","right"]).optional(),start_coordinate:aE.PV([aE.ai().int(),aE.ai().int()]).optional(),text:aE.Yj().optional()}),aR={bash_20241022:function(a={}){return{type:"provider-defined",id:"anthropic.bash_20241022",args:{},parameters:aL,execute:a.execute,experimental_toToolResultContent:a.experimental_toToolResultContent}},bash_20250124:function(a={}){return{type:"provider-defined",id:"anthropic.bash_20250124",args:{},parameters:aM,execute:a.execute,experimental_toToolResultContent:a.experimental_toToolResultContent}},textEditor_20241022:function(a={}){return{type:"provider-defined",id:"anthropic.text_editor_20241022",args:{},parameters:aN,execute:a.execute,experimental_toToolResultContent:a.experimental_toToolResultContent}},textEditor_20250124:function(a={}){return{type:"provider-defined",id:"anthropic.text_editor_20250124",args:{},parameters:aO,execute:a.execute,experimental_toToolResultContent:a.experimental_toToolResultContent}},computer_20241022:function(a){return{type:"provider-defined",id:"anthropic.computer_20241022",args:{displayWidthPx:a.displayWidthPx,displayHeightPx:a.displayHeightPx,displayNumber:a.displayNumber},parameters:aP,execute:a.execute,experimental_toToolResultContent:a.experimental_toToolResultContent}},computer_20250124:function(a){return{type:"provider-defined",id:"anthropic.computer_20250124",args:{displayWidthPx:a.displayWidthPx,displayHeightPx:a.displayHeightPx,displayNumber:a.displayNumber},parameters:aQ,execute:a.execute,experimental_toToolResultContent:a.experimental_toToolResultContent}}};function aS(a={}){var b,c;let d=null!=(b=null==(c=a.baseURL)?void 0:c.replace(/\/$/,""))?b:"https://api.anthropic.com/v1",e=()=>({"anthropic-version":"2023-06-01","x-api-key":function({apiKey:a,environmentVariableName:b,apiKeyParameterName:c="apiKey",description:d}){if("string"==typeof a)return a;if(null!=a)throw new ab({message:`${d} API key must be a string.`});if("undefined"==typeof process)throw new ab({message:`${d} API key is missing. Pass it using the '${c}' parameter. Environment variables is not supported in this environment.`});if(null==(a=process.env[b]))throw new ab({message:`${d} API key is missing. Pass it using the '${c}' parameter or the ${b} environment variable.`});if("string"!=typeof a)throw new ab({message:`${d} API key must be a string. The value of the ${b} environment variable is not a string.`});return a}({apiKey:a.apiKey,environmentVariableName:"ANTHROPIC_API_KEY",description:"Anthropic"}),...a.headers}),f=(b,c={})=>new aH(b,c,{provider:"anthropic.messages",baseURL:d,headers:e,fetch:a.fetch,supportsImageUrls:!0}),g=function(a,b){if(new.target)throw Error("The Anthropic model function cannot be called with the new keyword.");return f(a,b)};return g.languageModel=f,g.chat=f,g.messages=f,g.textEmbeddingModel=a=>{throw new af({modelId:a,modelType:"textEmbeddingModel"})},g.tools=aR,g}aS();var aT=c(7366),aU=c(7759);let aV=Symbol("Let zodToJsonSchema decide on which parser to use"),aW={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref",openAiAnyTypeName:"OpenAiAnyType"},aX=(a,b)=>{let c=0;for(;c<a.length&&c<b.length&&a[c]===b[c];c++);return[(a.length-c).toString(),...b.slice(c)].join("/")};function aY(a){if("openAi"!==a.target)return{};let b=[...a.basePath,a.definitionPath,a.openAiAnyTypeName];return a.flags.hasReferencedOpenAiAnyType=!0,{$ref:"relative"===a.$refStrategy?aX(b,a.currentPath):b.join("/")}}function aZ(a,b,c,d){d?.errorMessages&&c&&(a.errorMessage={...a.errorMessage,[b]:c})}function a$(a,b,c,d,e){a[b]=c,aZ(a,b,d,e)}function a_(a,b){return ba(a.type._def,b)}let a0={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(void 0===d&&(d=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),d),ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function a1(a,b){let c={type:"string"};if(a.checks)for(let d of a.checks)switch(d.kind){case"min":a$(c,"minLength","number"==typeof c.minLength?Math.max(c.minLength,d.value):d.value,d.message,b);break;case"max":a$(c,"maxLength","number"==typeof c.maxLength?Math.min(c.maxLength,d.value):d.value,d.message,b);break;case"email":switch(b.emailStrategy){case"format:email":a4(c,"email",d.message,b);break;case"format:idn-email":a4(c,"idn-email",d.message,b);break;case"pattern:zod":a5(c,a0.email,d.message,b)}break;case"url":a4(c,"uri",d.message,b);break;case"uuid":a4(c,"uuid",d.message,b);break;case"regex":a5(c,d.regex,d.message,b);break;case"cuid":a5(c,a0.cuid,d.message,b);break;case"cuid2":a5(c,a0.cuid2,d.message,b);break;case"startsWith":a5(c,RegExp(`^${a2(d.value,b)}`),d.message,b);break;case"endsWith":a5(c,RegExp(`${a2(d.value,b)}$`),d.message,b);break;case"datetime":a4(c,"date-time",d.message,b);break;case"date":a4(c,"date",d.message,b);break;case"time":a4(c,"time",d.message,b);break;case"duration":a4(c,"duration",d.message,b);break;case"length":a$(c,"minLength","number"==typeof c.minLength?Math.max(c.minLength,d.value):d.value,d.message,b),a$(c,"maxLength","number"==typeof c.maxLength?Math.min(c.maxLength,d.value):d.value,d.message,b);break;case"includes":a5(c,RegExp(a2(d.value,b)),d.message,b);break;case"ip":"v6"!==d.version&&a4(c,"ipv4",d.message,b),"v4"!==d.version&&a4(c,"ipv6",d.message,b);break;case"base64url":a5(c,a0.base64url,d.message,b);break;case"jwt":a5(c,a0.jwt,d.message,b);break;case"cidr":"v6"!==d.version&&a5(c,a0.ipv4Cidr,d.message,b),"v4"!==d.version&&a5(c,a0.ipv6Cidr,d.message,b);break;case"emoji":a5(c,a0.emoji(),d.message,b);break;case"ulid":a5(c,a0.ulid,d.message,b);break;case"base64":switch(b.base64Strategy){case"format:binary":a4(c,"binary",d.message,b);break;case"contentEncoding:base64":a$(c,"contentEncoding","base64",d.message,b);break;case"pattern:zod":a5(c,a0.base64,d.message,b)}break;case"nanoid":a5(c,a0.nanoid,d.message,b)}return c}function a2(a,b){return"escape"===b.patternStrategy?function(a){let b="";for(let c=0;c<a.length;c++)a3.has(a[c])||(b+="\\"),b+=a[c];return b}(a):a}let a3=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function a4(a,b,c,d){a.format||a.anyOf?.some(a=>a.format)?(a.anyOf||(a.anyOf=[]),a.format&&(a.anyOf.push({format:a.format,...a.errorMessage&&d.errorMessages&&{errorMessage:{format:a.errorMessage.format}}}),delete a.format,a.errorMessage&&(delete a.errorMessage.format,0===Object.keys(a.errorMessage).length&&delete a.errorMessage)),a.anyOf.push({format:b,...c&&d.errorMessages&&{errorMessage:{format:c}}})):a$(a,"format",b,c,d)}function a5(a,b,c,d){a.pattern||a.allOf?.some(a=>a.pattern)?(a.allOf||(a.allOf=[]),a.pattern&&(a.allOf.push({pattern:a.pattern,...a.errorMessage&&d.errorMessages&&{errorMessage:{pattern:a.errorMessage.pattern}}}),delete a.pattern,a.errorMessage&&(delete a.errorMessage.pattern,0===Object.keys(a.errorMessage).length&&delete a.errorMessage)),a.allOf.push({pattern:a6(b,d),...c&&d.errorMessages&&{errorMessage:{pattern:c}}})):a$(a,"pattern",a6(b,d),c,d)}function a6(a,b){if(!b.applyRegexFlags||!a.flags)return a.source;let c={i:a.flags.includes("i"),m:a.flags.includes("m"),s:a.flags.includes("s")},d=c.i?a.source.toLowerCase():a.source,e="",f=!1,g=!1,h=!1;for(let a=0;a<d.length;a++){if(f){e+=d[a],f=!1;continue}if(c.i){if(g){if(d[a].match(/[a-z]/)){h?(e+=d[a],e+=`${d[a-2]}-${d[a]}`.toUpperCase(),h=!1):"-"===d[a+1]&&d[a+2]?.match(/[a-z]/)?(e+=d[a],h=!0):e+=`${d[a]}${d[a].toUpperCase()}`;continue}}else if(d[a].match(/[a-z]/)){e+=`[${d[a]}${d[a].toUpperCase()}]`;continue}}if(c.m){if("^"===d[a]){e+=`(^|(?<=[\r
]))`;continue}else if("$"===d[a]){e+=`($|(?=[\r
]))`;continue}}if(c.s&&"."===d[a]){e+=g?`${d[a]}\r
`:`[${d[a]}\r
]`;continue}e+=d[a],"\\"===d[a]?f=!0:g&&"]"===d[a]?g=!1:g||"["!==d[a]||(g=!0)}try{new RegExp(e)}catch{return console.warn(`Could not convert regex pattern at ${b.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),a.source}return e}function a7(a,b){if("openAi"===b.target&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),"openApi3"===b.target&&a.keyType?._def.typeName===aE.kY.ZodEnum)return{type:"object",required:a.keyType._def.values,properties:a.keyType._def.values.reduce((c,d)=>({...c,[d]:ba(a.valueType._def,{...b,currentPath:[...b.currentPath,"properties",d]})??aY(b)}),{}),additionalProperties:b.rejectedAdditionalProperties};let c={type:"object",additionalProperties:ba(a.valueType._def,{...b,currentPath:[...b.currentPath,"additionalProperties"]})??b.allowedAdditionalProperties};if("openApi3"===b.target)return c;if(a.keyType?._def.typeName===aE.kY.ZodString&&a.keyType._def.checks?.length){let{type:d,...e}=a1(a.keyType._def,b);return{...c,propertyNames:e}}if(a.keyType?._def.typeName===aE.kY.ZodEnum)return{...c,propertyNames:{enum:a.keyType._def.values}};if(a.keyType?._def.typeName===aE.kY.ZodBranded&&a.keyType._def.type._def.typeName===aE.kY.ZodString&&a.keyType._def.type._def.checks?.length){let{type:d,...e}=a_(a.keyType._def,b);return{...c,propertyNames:e}}return c}let a8={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"},a9=(a,b)=>{let c=(a.options instanceof Map?Array.from(a.options.values()):a.options).map((a,c)=>ba(a._def,{...b,currentPath:[...b.currentPath,"anyOf",`${c}`]})).filter(a=>!!a&&(!b.strictUnions||"object"==typeof a&&Object.keys(a).length>0));return c.length?{anyOf:c}:void 0};function ba(a,b,c=!1){let d=b.seen.get(a);if(b.override){let e=b.override?.(a,b,d,c);if(e!==aV)return e}if(d&&!c){let a=bb(d,b);if(void 0!==a)return a}let e={def:a,path:b.currentPath,jsonSchema:void 0};b.seen.set(a,e);let f=((a,b,c)=>{switch(b){case aE.kY.ZodString:return a1(a,c);case aE.kY.ZodNumber:var d,e,f,g,h,i,j,k,l,m=a,n=c;let o={type:"number"};if(!m.checks)return o;for(let a of m.checks)switch(a.kind){case"int":o.type="integer",aZ(o,"type",a.message,n);break;case"min":"jsonSchema7"===n.target?a.inclusive?a$(o,"minimum",a.value,a.message,n):a$(o,"exclusiveMinimum",a.value,a.message,n):(a.inclusive||(o.exclusiveMinimum=!0),a$(o,"minimum",a.value,a.message,n));break;case"max":"jsonSchema7"===n.target?a.inclusive?a$(o,"maximum",a.value,a.message,n):a$(o,"exclusiveMaximum",a.value,a.message,n):(a.inclusive||(o.exclusiveMaximum=!0),a$(o,"maximum",a.value,a.message,n));break;case"multipleOf":a$(o,"multipleOf",a.value,a.message,n)}return o;case aE.kY.ZodObject:return function(a,b){let c="openAi"===b.target,d={type:"object",properties:{}},e=[],f=a.shape();for(let a in f){let g=f[a];if(void 0===g||void 0===g._def)continue;let h=function(a){try{return a.isOptional()}catch{return!0}}(g);h&&c&&("ZodOptional"===g._def.typeName&&(g=g._def.innerType),g.isNullable()||(g=g.nullable()),h=!1);let i=ba(g._def,{...b,currentPath:[...b.currentPath,"properties",a],propertyPath:[...b.currentPath,"properties",a]});void 0!==i&&(d.properties[a]=i,h||e.push(a))}e.length&&(d.required=e);let g=function(a,b){if("ZodNever"!==a.catchall._def.typeName)return ba(a.catchall._def,{...b,currentPath:[...b.currentPath,"additionalProperties"]});switch(a.unknownKeys){case"passthrough":return b.allowedAdditionalProperties;case"strict":return b.rejectedAdditionalProperties;case"strip":return"strict"===b.removeAdditionalStrategy?b.allowedAdditionalProperties:b.rejectedAdditionalProperties}}(a,b);return void 0!==g&&(d.additionalProperties=g),d}(a,c);case aE.kY.ZodBigInt:var p=a,q=c;let r={type:"integer",format:"int64"};if(!p.checks)return r;for(let a of p.checks)switch(a.kind){case"min":"jsonSchema7"===q.target?a.inclusive?a$(r,"minimum",a.value,a.message,q):a$(r,"exclusiveMinimum",a.value,a.message,q):(a.inclusive||(r.exclusiveMinimum=!0),a$(r,"minimum",a.value,a.message,q));break;case"max":"jsonSchema7"===q.target?a.inclusive?a$(r,"maximum",a.value,a.message,q):a$(r,"exclusiveMaximum",a.value,a.message,q):(a.inclusive||(r.exclusiveMaximum=!0),a$(r,"maximum",a.value,a.message,q));break;case"multipleOf":a$(r,"multipleOf",a.value,a.message,q)}return r;case aE.kY.ZodBoolean:return{type:"boolean"};case aE.kY.ZodDate:return function a(b,c,d){let e=d??c.dateStrategy;if(Array.isArray(e))return{anyOf:e.map((d,e)=>a(b,c,d))};switch(e){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":var f=b,g=c;let h={type:"integer",format:"unix-time"};if("openApi3"===g.target)return h;for(let a of f.checks)switch(a.kind){case"min":a$(h,"minimum",a.value,a.message,g);break;case"max":a$(h,"maximum",a.value,a.message,g)}return h}}(a,c);case aE.kY.ZodUndefined:return{not:aY(c)};case aE.kY.ZodNull:return"openApi3"===c.target?{enum:["null"],nullable:!0}:{type:"null"};case aE.kY.ZodArray:var s=a,t=c;let u={type:"array"};return s.type?._def&&s.type?._def?.typeName!==aE.kY.ZodAny&&(u.items=ba(s.type._def,{...t,currentPath:[...t.currentPath,"items"]})),s.minLength&&a$(u,"minItems",s.minLength.value,s.minLength.message,t),s.maxLength&&a$(u,"maxItems",s.maxLength.value,s.maxLength.message,t),s.exactLength&&(a$(u,"minItems",s.exactLength.value,s.exactLength.message,t),a$(u,"maxItems",s.exactLength.value,s.exactLength.message,t)),u;case aE.kY.ZodUnion:case aE.kY.ZodDiscriminatedUnion:var v=a,w=c;if("openApi3"===w.target)return a9(v,w);let x=v.options instanceof Map?Array.from(v.options.values()):v.options;if(x.every(a=>a._def.typeName in a8&&(!a._def.checks||!a._def.checks.length))){let a=x.reduce((a,b)=>{let c=a8[b._def.typeName];return c&&!a.includes(c)?[...a,c]:a},[]);return{type:a.length>1?a:a[0]}}if(x.every(a=>"ZodLiteral"===a._def.typeName&&!a.description)){let a=x.reduce((a,b)=>{let c=typeof b._def.value;switch(c){case"string":case"number":case"boolean":return[...a,c];case"bigint":return[...a,"integer"];case"object":if(null===b._def.value)return[...a,"null"];default:return a}},[]);if(a.length===x.length){let b=a.filter((a,b,c)=>c.indexOf(a)===b);return{type:b.length>1?b:b[0],enum:x.reduce((a,b)=>a.includes(b._def.value)?a:[...a,b._def.value],[])}}}else if(x.every(a=>"ZodEnum"===a._def.typeName))return{type:"string",enum:x.reduce((a,b)=>[...a,...b._def.values.filter(b=>!a.includes(b))],[])};return a9(v,w);case aE.kY.ZodIntersection:var y=a,z=c;let A=[ba(y.left._def,{...z,currentPath:[...z.currentPath,"allOf","0"]}),ba(y.right._def,{...z,currentPath:[...z.currentPath,"allOf","1"]})].filter(a=>!!a),B="jsonSchema2019-09"===z.target?{unevaluatedProperties:!1}:void 0,C=[];return A.forEach(a=>{if((!("type"in a)||"string"!==a.type)&&"allOf"in a)C.push(...a.allOf),void 0===a.unevaluatedProperties&&(B=void 0);else{let b=a;if("additionalProperties"in a&&!1===a.additionalProperties){let{additionalProperties:c,...d}=a;b=d}else B=void 0;C.push(b)}}),C.length?{allOf:C,...B}:void 0;case aE.kY.ZodTuple:return d=a,e=c,d.rest?{type:"array",minItems:d.items.length,items:d.items.map((a,b)=>ba(a._def,{...e,currentPath:[...e.currentPath,"items",`${b}`]})).reduce((a,b)=>void 0===b?a:[...a,b],[]),additionalItems:ba(d.rest._def,{...e,currentPath:[...e.currentPath,"additionalItems"]})}:{type:"array",minItems:d.items.length,maxItems:d.items.length,items:d.items.map((a,b)=>ba(a._def,{...e,currentPath:[...e.currentPath,"items",`${b}`]})).reduce((a,b)=>void 0===b?a:[...a,b],[])};case aE.kY.ZodRecord:return a7(a,c);case aE.kY.ZodLiteral:var D=a,E=c;let F=typeof D.value;return"bigint"!==F&&"number"!==F&&"boolean"!==F&&"string"!==F?{type:Array.isArray(D.value)?"array":"object"}:"openApi3"===E.target?{type:"bigint"===F?"integer":F,enum:[D.value]}:{type:"bigint"===F?"integer":F,const:D.value};case aE.kY.ZodEnum:return{type:"string",enum:Array.from(a.values)};case aE.kY.ZodNativeEnum:var G=a;let H=G.values,I=Object.keys(G.values).filter(a=>"number"!=typeof H[H[a]]).map(a=>H[a]),J=Array.from(new Set(I.map(a=>typeof a)));return{type:1===J.length?"string"===J[0]?"string":"number":["string","number"],enum:I};case aE.kY.ZodNullable:var K=a,L=c;if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(K.innerType._def.typeName)&&(!K.innerType._def.checks||!K.innerType._def.checks.length))return"openApi3"===L.target?{type:a8[K.innerType._def.typeName],nullable:!0}:{type:[a8[K.innerType._def.typeName],"null"]};if("openApi3"===L.target){let a=ba(K.innerType._def,{...L,currentPath:[...L.currentPath]});return a&&"$ref"in a?{allOf:[a],nullable:!0}:a&&{...a,nullable:!0}}let M=ba(K.innerType._def,{...L,currentPath:[...L.currentPath,"anyOf","0"]});return M&&{anyOf:[M,{type:"null"}]};case aE.kY.ZodOptional:var N=a,O=c;if(O.currentPath.toString()===O.propertyPath?.toString())return ba(N.innerType._def,O);let P=ba(N.innerType._def,{...O,currentPath:[...O.currentPath,"anyOf","1"]});return P?{anyOf:[{not:aY(O)},P]}:aY(O);case aE.kY.ZodMap:return f=a,"record"===(g=c).mapStrategy?a7(f,g):{type:"array",maxItems:125,items:{type:"array",items:[ba(f.keyType._def,{...g,currentPath:[...g.currentPath,"items","items","0"]})||aY(g),ba(f.valueType._def,{...g,currentPath:[...g.currentPath,"items","items","1"]})||aY(g)],minItems:2,maxItems:2}};case aE.kY.ZodSet:var Q=a,R=c;let S={type:"array",uniqueItems:!0,items:ba(Q.valueType._def,{...R,currentPath:[...R.currentPath,"items"]})};return Q.minSize&&a$(S,"minItems",Q.minSize.value,Q.minSize.message,R),Q.maxSize&&a$(S,"maxItems",Q.maxSize.value,Q.maxSize.message,R),S;case aE.kY.ZodLazy:return()=>a.getter()._def;case aE.kY.ZodPromise:return ba(a.type._def,c);case aE.kY.ZodNaN:case aE.kY.ZodNever:return"openAi"===(h=c).target?void 0:{not:aY({...h,currentPath:[...h.currentPath,"not"]})};case aE.kY.ZodEffects:return i=a,"input"===(j=c).effectStrategy?ba(i.schema._def,j):aY(j);case aE.kY.ZodAny:case aE.kY.ZodUnknown:return aY(c);case aE.kY.ZodDefault:return k=a,l=c,{...ba(k.innerType._def,l),default:k.defaultValue()};case aE.kY.ZodBranded:return a_(a,c);case aE.kY.ZodReadonly:case aE.kY.ZodCatch:return ba(a.innerType._def,c);case aE.kY.ZodPipeline:var T=a,U=c;if("input"===U.pipeStrategy)return ba(T.in._def,U);if("output"===U.pipeStrategy)return ba(T.out._def,U);let V=ba(T.in._def,{...U,currentPath:[...U.currentPath,"allOf","0"]}),W=ba(T.out._def,{...U,currentPath:[...U.currentPath,"allOf",V?"1":"0"]});return{allOf:[V,W].filter(a=>void 0!==a)};case aE.kY.ZodFunction:case aE.kY.ZodVoid:case aE.kY.ZodSymbol:default:return}})(a,a.typeName,b),g="function"==typeof f?ba(f(),b):f;if(g&&bc(a,b,g),b.postProcess){let c=b.postProcess(g,a,b);return e.jsonSchema=g,c}return e.jsonSchema=g,g}let bb=(a,b)=>{switch(b.$refStrategy){case"root":return{$ref:a.path.join("/")};case"relative":return{$ref:aX(b.currentPath,a.path)};case"none":case"seen":if(a.path.length<b.currentPath.length&&a.path.every((a,c)=>b.currentPath[c]===a))return console.warn(`Recursive reference detected at ${b.currentPath.join("/")}! Defaulting to any`),aY(b);return"seen"===b.$refStrategy?aY(b):void 0}},bc=(a,b,c)=>(a.description&&(c.description=a.description,b.markdownDescription&&(c.markdownDescription=a.description)),c);var bd={code:"0",name:"text",parse:a=>{if("string"!=typeof a)throw Error('"text" parts expect a string value.');return{type:"text",value:a}}},be={code:"3",name:"error",parse:a=>{if("string"!=typeof a)throw Error('"error" parts expect a string value.');return{type:"error",value:a}}},bf={code:"4",name:"assistant_message",parse:a=>{if(null==a||"object"!=typeof a||!("id"in a)||!("role"in a)||!("content"in a)||"string"!=typeof a.id||"string"!=typeof a.role||"assistant"!==a.role||!Array.isArray(a.content)||!a.content.every(a=>null!=a&&"object"==typeof a&&"type"in a&&"text"===a.type&&"text"in a&&null!=a.text&&"object"==typeof a.text&&"value"in a.text&&"string"==typeof a.text.value))throw Error('"assistant_message" parts expect an object with an "id", "role", and "content" property.');return{type:"assistant_message",value:a}}},bg={code:"5",name:"assistant_control_data",parse:a=>{if(null==a||"object"!=typeof a||!("threadId"in a)||!("messageId"in a)||"string"!=typeof a.threadId||"string"!=typeof a.messageId)throw Error('"assistant_control_data" parts expect an object with a "threadId" and "messageId" property.');return{type:"assistant_control_data",value:{threadId:a.threadId,messageId:a.messageId}}}},bh={code:"6",name:"data_message",parse:a=>{if(null==a||"object"!=typeof a||!("role"in a)||!("data"in a)||"string"!=typeof a.role||"data"!==a.role)throw Error('"data_message" parts expect an object with a "role" and "data" property.');return{type:"data_message",value:a}}},bi=[bd,be,bf,bg,bh];bd.code,be.code,bf.code,bg.code,bh.code,bd.name,bd.code,be.name,be.code,bf.name,bf.code,bg.name,bg.code,bh.name,bh.code,bi.map(a=>a.code);var bj=[{code:"0",name:"text",parse:a=>{if("string"!=typeof a)throw Error('"text" parts expect a string value.');return{type:"text",value:a}}},{code:"2",name:"data",parse:a=>{if(!Array.isArray(a))throw Error('"data" parts expect an array value.');return{type:"data",value:a}}},{code:"3",name:"error",parse:a=>{if("string"!=typeof a)throw Error('"error" parts expect a string value.');return{type:"error",value:a}}},{code:"8",name:"message_annotations",parse:a=>{if(!Array.isArray(a))throw Error('"message_annotations" parts expect an array value.');return{type:"message_annotations",value:a}}},{code:"9",name:"tool_call",parse:a=>{if(null==a||"object"!=typeof a||!("toolCallId"in a)||"string"!=typeof a.toolCallId||!("toolName"in a)||"string"!=typeof a.toolName||!("args"in a)||"object"!=typeof a.args)throw Error('"tool_call" parts expect an object with a "toolCallId", "toolName", and "args" property.');return{type:"tool_call",value:a}}},{code:"a",name:"tool_result",parse:a=>{if(null==a||"object"!=typeof a||!("toolCallId"in a)||"string"!=typeof a.toolCallId||!("result"in a))throw Error('"tool_result" parts expect an object with a "toolCallId" and a "result" property.');return{type:"tool_result",value:a}}},{code:"b",name:"tool_call_streaming_start",parse:a=>{if(null==a||"object"!=typeof a||!("toolCallId"in a)||"string"!=typeof a.toolCallId||!("toolName"in a)||"string"!=typeof a.toolName)throw Error('"tool_call_streaming_start" parts expect an object with a "toolCallId" and "toolName" property.');return{type:"tool_call_streaming_start",value:a}}},{code:"c",name:"tool_call_delta",parse:a=>{if(null==a||"object"!=typeof a||!("toolCallId"in a)||"string"!=typeof a.toolCallId||!("argsTextDelta"in a)||"string"!=typeof a.argsTextDelta)throw Error('"tool_call_delta" parts expect an object with a "toolCallId" and "argsTextDelta" property.');return{type:"tool_call_delta",value:a}}},{code:"d",name:"finish_message",parse:a=>{if(null==a||"object"!=typeof a||!("finishReason"in a)||"string"!=typeof a.finishReason)throw Error('"finish_message" parts expect an object with a "finishReason" property.');let b={finishReason:a.finishReason};return"usage"in a&&null!=a.usage&&"object"==typeof a.usage&&"promptTokens"in a.usage&&"completionTokens"in a.usage&&(b.usage={promptTokens:"number"==typeof a.usage.promptTokens?a.usage.promptTokens:NaN,completionTokens:"number"==typeof a.usage.completionTokens?a.usage.completionTokens:NaN}),{type:"finish_message",value:b}}},{code:"e",name:"finish_step",parse:a=>{if(null==a||"object"!=typeof a||!("finishReason"in a)||"string"!=typeof a.finishReason)throw Error('"finish_step" parts expect an object with a "finishReason" property.');let b={finishReason:a.finishReason,isContinued:!1};return"usage"in a&&null!=a.usage&&"object"==typeof a.usage&&"promptTokens"in a.usage&&"completionTokens"in a.usage&&(b.usage={promptTokens:"number"==typeof a.usage.promptTokens?a.usage.promptTokens:NaN,completionTokens:"number"==typeof a.usage.completionTokens?a.usage.completionTokens:NaN}),"isContinued"in a&&"boolean"==typeof a.isContinued&&(b.isContinued=a.isContinued),{type:"finish_step",value:b}}},{code:"f",name:"start_step",parse:a=>{if(null==a||"object"!=typeof a||!("messageId"in a)||"string"!=typeof a.messageId)throw Error('"start_step" parts expect an object with an "id" property.');return{type:"start_step",value:{messageId:a.messageId}}}},{code:"g",name:"reasoning",parse:a=>{if("string"!=typeof a)throw Error('"reasoning" parts expect a string value.');return{type:"reasoning",value:a}}},{code:"h",name:"source",parse:a=>{if(null==a||"object"!=typeof a)throw Error('"source" parts expect a Source object.');return{type:"source",value:a}}},{code:"i",name:"redacted_reasoning",parse:a=>{if(null==a||"object"!=typeof a||!("data"in a)||"string"!=typeof a.data)throw Error('"redacted_reasoning" parts expect an object with a "data" property.');return{type:"redacted_reasoning",value:{data:a.data}}}},{code:"j",name:"reasoning_signature",parse:a=>{if(null==a||"object"!=typeof a||!("signature"in a)||"string"!=typeof a.signature)throw Error('"reasoning_signature" parts expect an object with a "signature" property.');return{type:"reasoning_signature",value:{signature:a.signature}}}},{code:"k",name:"file",parse:a=>{if(null==a||"object"!=typeof a||!("data"in a)||"string"!=typeof a.data||!("mimeType"in a)||"string"!=typeof a.mimeType)throw Error('"file" parts expect an object with a "data" and "mimeType" property.');return{type:"file",value:a}}}];function bk(a,b){let c=bj.find(b=>b.name===a);if(!c)throw Error(`Invalid stream part type: ${a}`);return`${c.code}:${JSON.stringify(b)}
`}Object.fromEntries(bj.map(a=>[a.code,a])),Object.fromEntries(bj.map(a=>[a.name,a.code])),bj.map(a=>a.code);var bl=Symbol.for("vercel.ai.schema");function bm(a){return"object"==typeof a&&null!==a&&bl in a&&!0===a[bl]&&"jsonSchema"in a&&"validate"in a?a:function(a,{validate:b}={}){return{[bl]:!0,_type:void 0,[at]:!0,jsonSchema:a,validate:b}}(((a,b)=>{let c=(a=>{let b,c="string"==typeof(b=a)?{...aW,name:b}:{...aW,...b},d=void 0!==c.name?[...c.basePath,c.definitionPath,c.name]:c.basePath;return{...c,flags:{hasReferencedOpenAiAnyType:!1},currentPath:d,propertyPath:void 0,seen:new Map(Object.entries(c.definitions).map(([a,b])=>[b._def,{def:b._def,path:[...c.basePath,c.definitionPath,a],jsonSchema:void 0}]))}})(b),d="object"==typeof b&&b.definitions?Object.entries(b.definitions).reduce((a,[b,d])=>({...a,[b]:ba(d._def,{...c,currentPath:[...c.basePath,c.definitionPath,b]},!0)??aY(c)}),{}):void 0,e="string"==typeof b?b:b?.nameStrategy==="title"?void 0:b?.name,f=ba(a._def,void 0===e?c:{...c,currentPath:[...c.basePath,c.definitionPath,e]},!1)??aY(c),g="object"==typeof b&&void 0!==b.name&&"title"===b.nameStrategy?b.name:void 0;void 0!==g&&(f.title=g),c.flags.hasReferencedOpenAiAnyType&&(d||(d={}),d[c.openAiAnyTypeName]||(d[c.openAiAnyTypeName]={type:["string","number","integer","boolean","array","null"],items:{$ref:"relative"===c.$refStrategy?"1":[...c.basePath,c.definitionPath,c.openAiAnyTypeName].join("/")}}));let h=void 0===e?d?{...f,[c.definitionPath]:d}:f:{$ref:[..."relative"===c.$refStrategy?[]:c.basePath,c.definitionPath,e].join("/"),[c.definitionPath]:{...d,[e]:f}};return"jsonSchema7"===c.target?h.$schema="http://json-schema.org/draft-07/schema#":("jsonSchema2019-09"===c.target||"openAi"===c.target)&&(h.$schema="https://json-schema.org/draft/2019-09/schema#"),"openAi"===c.target&&("anyOf"in h||"oneOf"in h||"allOf"in h||"type"in h&&Array.isArray(h.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),h})(a,{$refStrategy:"none",target:"jsonSchema7"}),{validate:b=>{let c=a.safeParse(b);return c.success?{success:!0,value:c.data}:{success:!1,error:c.error}}})}var bn=Object.defineProperty,bo=(a,b)=>{for(var c in b)bn(a,c,{get:b[c],enumerable:!0})};function bp(a,{contentType:b,dataStreamVersion:c}){let d=new Headers(null!=a?a:{});return d.has("Content-Type")||d.set("Content-Type",b),void 0!==c&&d.set("X-Vercel-AI-Data-Stream",c),d}var bq=class extends E{constructor(){super({name:"AI_UnsupportedModelVersionError",message:'Unsupported model version. AI SDK 4 only supports models that implement specification version "v1". Please upgrade to AI SDK 5 to use this model.'})}},br="AI_InvalidArgumentError",bs=`vercel.ai.error.${br}`,bt=Symbol.for(bs),bu=class extends E{constructor({parameter:a,value:b,message:c}){super({name:br,message:`Invalid argument for parameter ${a}: ${c}`}),this[o]=!0,this.parameter=a,this.value=b}static isInstance(a){return E.hasMarker(a,bs)}};o=bt;var bv="AI_RetryError",bw=`vercel.ai.error.${bv}`,bx=Symbol.for(bw),by=class extends E{constructor({message:a,reason:b,errors:c}){super({name:bv,message:a}),this[p]=!0,this.reason=b,this.errors=c,this.lastError=c[c.length-1]}static isInstance(a){return E.hasMarker(a,bw)}};async function bz(a,{maxRetries:b,delayInMs:c,backoffFactor:d},e=[]){try{return await a()}catch(i){if(as(i)||0===b)throw i;let f=null==i?"unknown error":"string"==typeof i?i:i instanceof Error?i.message:JSON.stringify(i),g=[...e,i],h=g.length;if(h>b)throw new by({message:`Failed after ${h} attempts. Last error: ${f}`,reason:"maxRetriesExceeded",errors:g});if(i instanceof Error&&I.isInstance(i)&&!0===i.isRetryable&&h<=b)return await ap(c),bz(a,{maxRetries:b,delayInMs:d*c,backoffFactor:d},g);if(1===h)throw i;throw new by({message:`Failed after ${h} attempts with non-retryable error: '${f}'`,reason:"errorNotRetryable",errors:g})}}function bA({operationId:a,telemetry:b}){return{"operation.name":`${a}${(null==b?void 0:b.functionId)!=null?` ${b.functionId}`:""}`,"resource.name":null==b?void 0:b.functionId,"ai.operationId":a,"ai.telemetry.functionId":null==b?void 0:b.functionId}}p=bx;var bB={startSpan:()=>bC,startActiveSpan:(a,b,c,d)=>"function"==typeof b?b(bC):"function"==typeof c?c(bC):"function"==typeof d?d(bC):void 0},bC={spanContext:()=>bD,setAttribute(){return this},setAttributes(){return this},addEvent(){return this},addLink(){return this},addLinks(){return this},setStatus(){return this},updateName(){return this},end(){return this},isRecording:()=>!1,recordException(){return this}},bD={traceId:"",spanId:"",traceFlags:0};function bE({name:a,tracer:b,attributes:c,fn:d,endWhenDone:e=!0}){return b.startActiveSpan(a,{attributes:c},async a=>{try{let b=await d(a);return e&&a.end(),b}catch(b){try{bF(a,b)}finally{a.end()}throw b}})}function bF(a,b){b instanceof Error?(a.recordException({name:b.name,message:b.message,stack:b.stack}),a.setStatus({code:aU.s.ERROR,message:b.message})):a.setStatus({code:aU.s.ERROR})}function bG({telemetry:a,attributes:b}){return(null==a?void 0:a.isEnabled)!==!0?{}:Object.entries(b).reduce((b,[c,d])=>{if(void 0===d)return b;if("object"==typeof d&&"input"in d&&"function"==typeof d.input){if((null==a?void 0:a.recordInputs)===!1)return b;let e=d.input();return void 0===e?b:{...b,[c]:e}}if("object"==typeof d&&"output"in d&&"function"==typeof d.output){if((null==a?void 0:a.recordOutputs)===!1)return b;let e=d.output();return void 0===e?b:{...b,[c]:e}}return{...b,[c]:d}},{})}Symbol.for("vercel.ai.error.AI_NoImageGeneratedError");var bH=class{constructor({data:a,mimeType:b}){let c=a instanceof Uint8Array;this.base64Data=c?void 0:a,this.uint8ArrayData=c?a:void 0,this.mimeType=b}get base64(){return null==this.base64Data&&(this.base64Data=aD(this.uint8ArrayData)),this.base64Data}get uint8Array(){return null==this.uint8ArrayData&&(this.uint8ArrayData=aC(this.base64Data)),this.uint8ArrayData}},bI=[{mimeType:"image/gif",bytesPrefix:[71,73,70],base64Prefix:"R0lG"},{mimeType:"image/png",bytesPrefix:[137,80,78,71],base64Prefix:"iVBORw"},{mimeType:"image/jpeg",bytesPrefix:[255,216],base64Prefix:"/9j/"},{mimeType:"image/webp",bytesPrefix:[82,73,70,70],base64Prefix:"UklGRg"},{mimeType:"image/bmp",bytesPrefix:[66,77],base64Prefix:"Qk"},{mimeType:"image/tiff",bytesPrefix:[73,73,42,0],base64Prefix:"SUkqAA"},{mimeType:"image/tiff",bytesPrefix:[77,77,0,42],base64Prefix:"TU0AKg"},{mimeType:"image/avif",bytesPrefix:[0,0,0,32,102,116,121,112,97,118,105,102],base64Prefix:"AAAAIGZ0eXBhdmlm"},{mimeType:"image/heic",bytesPrefix:[0,0,0,32,102,116,121,112,104,101,105,99],base64Prefix:"AAAAIGZ0eXBoZWlj"}],bJ="AI_NoObjectGeneratedError",bK=`vercel.ai.error.${bJ}`,bL=Symbol.for(bK),bM=class extends E{constructor({message:a="No object generated.",cause:b,text:c,response:d,usage:e,finishReason:f}){super({name:bJ,message:a,cause:b}),this[q]=!0,this.text=c,this.response=d,this.usage=e,this.finishReason=f}static isInstance(a){return E.hasMarker(a,bK)}};q=bL;var bN="AI_DownloadError",bO=`vercel.ai.error.${bN}`,bP=Symbol.for(bO),bQ=class extends E{constructor({url:a,statusCode:b,statusText:c,cause:d,message:e=null==d?`Failed to download ${a}: ${b} ${c}`:`Failed to download ${a}: ${d}`}){super({name:bN,message:e,cause:d}),this[r]=!0,this.url=a,this.statusCode=b,this.statusText=c}static isInstance(a){return E.hasMarker(a,bO)}};async function bR({url:a}){var b;let c=a.toString();try{let a=await fetch(c);if(!a.ok)throw new bQ({url:c,statusCode:a.status,statusText:a.statusText});return{data:new Uint8Array(await a.arrayBuffer()),mimeType:null!=(b=a.headers.get("content-type"))?b:void 0}}catch(a){if(bQ.isInstance(a))throw a;throw new bQ({url:c,cause:a})}}r=bP;var bS="AI_InvalidDataContentError",bT=`vercel.ai.error.${bS}`,bU=Symbol.for(bT),bV=class extends E{constructor({content:a,cause:b,message:c=`Invalid data content. Expected a base64 string, Uint8Array, ArrayBuffer, or Buffer, but got ${typeof a}.`}){super({name:bS,message:c,cause:b}),this[s]=!0,this.content=a}static isInstance(a){return E.hasMarker(a,bT)}};s=bU;var bW=aE.KC([aE.Yj(),aE.Nl(Uint8Array),aE.Nl(ArrayBuffer),aE.Ie(a=>{var b,c;return null!=(c=null==(b=globalThis.Buffer)?void 0:b.isBuffer(a))&&c},{message:"Must be a Buffer"})]);function bX(a){return"string"==typeof a?a:a instanceof ArrayBuffer?aD(new Uint8Array(a)):aD(a)}function bY(a){if(a instanceof Uint8Array)return a;if("string"==typeof a)try{return aC(a)}catch(b){throw new bV({message:"Invalid data content. Content string is not a base64-encoded media.",content:a,cause:b})}if(a instanceof ArrayBuffer)return new Uint8Array(a);throw new bV({content:a})}var bZ="AI_InvalidMessageRoleError",b$=`vercel.ai.error.${bZ}`,b_=Symbol.for(b$),b0=class extends E{constructor({role:a,message:b=`Invalid message role: '${a}'. Must be one of: "system", "user", "assistant", "tool".`}){super({name:bZ,message:b}),this[t]=!0,this.role=a}static isInstance(a){return E.hasMarker(a,b$)}};async function b1({prompt:a,modelSupportsImageUrls:b=!0,modelSupportsUrl:c=()=>!1,downloadImplementation:d=bR}){let e=await b2(a.messages,d,b,c);return[...null!=a.system?[{role:"system",content:a.system}]:[],...a.messages.map(a=>(function(a,b){var c,d,e,f,g,h;let i=a.role;switch(i){case"system":return{role:"system",content:a.content,providerMetadata:null!=(c=a.providerOptions)?c:a.experimental_providerMetadata};case"user":if("string"==typeof a.content)return{role:"user",content:[{type:"text",text:a.content}],providerMetadata:null!=(d=a.providerOptions)?d:a.experimental_providerMetadata};return{role:"user",content:a.content.map(a=>(function(a,b){var c,d,e,f;let g,h,i;if("text"===a.type)return{type:"text",text:a.text,providerMetadata:null!=(c=a.providerOptions)?c:a.experimental_providerMetadata};let j=a.mimeType,k=a.type;switch(k){case"image":g=a.image;break;case"file":g=a.data;break;default:throw Error(`Unsupported part type: ${k}`)}try{h="string"==typeof g?new URL(g):g}catch(a){h=g}if(h instanceof URL)if("data:"===h.protocol){let{mimeType:a,base64Content:b}=function(a){try{let[b,c]=a.split(",");return{mimeType:b.split(";")[0].split(":")[1],base64Content:c}}catch(a){return{mimeType:void 0,base64Content:void 0}}}(h.toString());if(null==a||null==b)throw Error(`Invalid data URL format in part ${k}`);j=a,i=bY(b)}else{let a=b[h.toString()];a?(i=a.data,null!=j||(j=a.mimeType)):i=h}else i=bY(h);switch(k){case"image":return i instanceof Uint8Array&&(j=null!=(d=function({data:a,signatures:b}){let c="string"==typeof a&&a.startsWith("SUQz")||"string"!=typeof a&&a.length>10&&73===a[0]&&68===a[1]&&51===a[2]?(a=>{let b="string"==typeof a?aC(a):a,c=(127&b[6])<<21|(127&b[7])<<14|(127&b[8])<<7|127&b[9];return b.slice(c+10)})(a):a;for(let a of b)if("string"==typeof c?c.startsWith(a.base64Prefix):c.length>=a.bytesPrefix.length&&a.bytesPrefix.every((a,b)=>c[b]===a))return a.mimeType}({data:i,signatures:bI}))?d:j),{type:"image",image:i,mimeType:j,providerMetadata:null!=(e=a.providerOptions)?e:a.experimental_providerMetadata};case"file":if(null==j)throw Error("Mime type is missing for file part");return{type:"file",data:i instanceof Uint8Array?bX(i):i,filename:a.filename,mimeType:j,providerMetadata:null!=(f=a.providerOptions)?f:a.experimental_providerMetadata}}})(a,b)).filter(a=>"text"!==a.type||""!==a.text),providerMetadata:null!=(e=a.providerOptions)?e:a.experimental_providerMetadata};case"assistant":if("string"==typeof a.content)return{role:"assistant",content:[{type:"text",text:a.content}],providerMetadata:null!=(f=a.providerOptions)?f:a.experimental_providerMetadata};return{role:"assistant",content:a.content.filter(a=>"text"!==a.type||""!==a.text).map(a=>{var b;let c=null!=(b=a.providerOptions)?b:a.experimental_providerMetadata;switch(a.type){case"file":return{type:"file",data:a.data instanceof URL?a.data:bX(a.data),filename:a.filename,mimeType:a.mimeType,providerMetadata:c};case"reasoning":return{type:"reasoning",text:a.text,signature:a.signature,providerMetadata:c};case"redacted-reasoning":return{type:"redacted-reasoning",data:a.data,providerMetadata:c};case"text":return{type:"text",text:a.text,providerMetadata:c};case"tool-call":return{type:"tool-call",toolCallId:a.toolCallId,toolName:a.toolName,args:a.args,providerMetadata:c}}}),providerMetadata:null!=(g=a.providerOptions)?g:a.experimental_providerMetadata};case"tool":return{role:"tool",content:a.content.map(a=>{var b;return{type:"tool-result",toolCallId:a.toolCallId,toolName:a.toolName,result:a.result,content:a.experimental_content,isError:a.isError,providerMetadata:null!=(b=a.providerOptions)?b:a.experimental_providerMetadata}}),providerMetadata:null!=(h=a.providerOptions)?h:a.experimental_providerMetadata};default:throw new b0({role:i})}})(a,e))]}async function b2(a,b,c,d){let e=a.filter(a=>"user"===a.role).map(a=>a.content).filter(a=>Array.isArray(a)).flat().filter(a=>"image"===a.type||"file"===a.type).filter(a=>"image"!==a.type||!0!==c).map(a=>"image"===a.type?a.image:a.data).map(a=>"string"==typeof a&&(a.startsWith("http:")||a.startsWith("https:"))?new URL(a):a).filter(a=>a instanceof URL).filter(a=>!d(a));return Object.fromEntries((await Promise.all(e.map(async a=>({url:a,data:await b({url:a})})))).map(({url:a,data:b})=>[a.toString(),b]))}function b3(a){var b,c,d;let e=[];for(let f of a){let a;try{a=new URL(f.url)}catch(a){throw Error(`Invalid URL: ${f.url}`)}switch(a.protocol){case"http:":case"https:":if(null==(b=f.contentType)?void 0:b.startsWith("image/"))e.push({type:"image",image:a});else{if(!f.contentType)throw Error("If the attachment is not an image, it must specify a content type");e.push({type:"file",data:a,mimeType:f.contentType})}break;case"data:":{let a,b,g;try{[a,b]=f.url.split(","),g=a.split(";")[0].split(":")[1]}catch(a){throw Error(`Error processing data URL: ${f.url}`)}if(null==g||null==b)throw Error(`Invalid data URL format: ${f.url}`);if(null==(c=f.contentType)?void 0:c.startsWith("image/"))e.push({type:"image",image:bY(b)});else if(null==(d=f.contentType)?void 0:d.startsWith("text/"))e.push({type:"text",text:function(a){try{return new TextDecoder().decode(a)}catch(a){throw Error("Error decoding Uint8Array to text")}}(bY(b))});else{if(!f.contentType)throw Error("If the attachment is not an image or text, it must specify a content type");e.push({type:"file",data:b,mimeType:f.contentType})}break}default:throw Error(`Unsupported URL protocol: ${a.protocol}`)}}return e}t=b_;var b4="AI_MessageConversionError",b5=`vercel.ai.error.${b4}`,b6=Symbol.for(b5),b7=class extends E{constructor({originalMessage:a,message:b}){super({name:b4,message:b}),this[u]=!0,this.originalMessage=a}static isInstance(a){return E.hasMarker(a,b5)}};u=b6;var b8=aE.RZ(()=>aE.KC([aE.ch(),aE.Yj(),aE.ai(),aE.zM(),aE.g1(aE.Yj(),b8),aE.YO(b8)])),b9=aE.g1(aE.Yj(),aE.g1(aE.Yj(),b8)),ca=aE.YO(aE.KC([aE.Ik({type:aE.eu("text"),text:aE.Yj()}),aE.Ik({type:aE.eu("image"),data:aE.Yj(),mimeType:aE.Yj().optional()})])),cb=aE.Ik({type:aE.eu("text"),text:aE.Yj(),providerOptions:b9.optional(),experimental_providerMetadata:b9.optional()}),cc=aE.Ik({type:aE.eu("image"),image:aE.KC([bW,aE.Nl(URL)]),mimeType:aE.Yj().optional(),providerOptions:b9.optional(),experimental_providerMetadata:b9.optional()}),cd=aE.Ik({type:aE.eu("file"),data:aE.KC([bW,aE.Nl(URL)]),filename:aE.Yj().optional(),mimeType:aE.Yj(),providerOptions:b9.optional(),experimental_providerMetadata:b9.optional()}),ce=aE.Ik({type:aE.eu("reasoning"),text:aE.Yj(),providerOptions:b9.optional(),experimental_providerMetadata:b9.optional()}),cf=aE.Ik({type:aE.eu("redacted-reasoning"),data:aE.Yj(),providerOptions:b9.optional(),experimental_providerMetadata:b9.optional()}),cg=aE.Ik({type:aE.eu("tool-call"),toolCallId:aE.Yj(),toolName:aE.Yj(),args:aE.L5(),providerOptions:b9.optional(),experimental_providerMetadata:b9.optional()}),ch=aE.Ik({type:aE.eu("tool-result"),toolCallId:aE.Yj(),toolName:aE.Yj(),result:aE.L5(),content:ca.optional(),isError:aE.zM().optional(),providerOptions:b9.optional(),experimental_providerMetadata:b9.optional()}),ci=aE.Ik({role:aE.eu("system"),content:aE.Yj(),providerOptions:b9.optional(),experimental_providerMetadata:b9.optional()}),cj=aE.Ik({role:aE.eu("user"),content:aE.KC([aE.Yj(),aE.YO(aE.KC([cb,cc,cd]))]),providerOptions:b9.optional(),experimental_providerMetadata:b9.optional()}),ck=aE.Ik({role:aE.eu("assistant"),content:aE.KC([aE.Yj(),aE.YO(aE.KC([cb,cd,ce,cf,cg]))]),providerOptions:b9.optional(),experimental_providerMetadata:b9.optional()}),cl=aE.Ik({role:aE.eu("tool"),content:aE.YO(ch),providerOptions:b9.optional(),experimental_providerMetadata:b9.optional()}),cm=aE.KC([ci,cj,ck,cl]);function cn(a){return"object"==typeof a&&null!==a&&("function"===a.role||"data"===a.role||"toolInvocations"in a||"parts"in a||"experimental_attachments"in a)?"has-ui-specific-parts":"object"==typeof a&&null!==a&&"content"in a&&(Array.isArray(a.content)||"experimental_providerMetadata"in a||"providerOptions"in a)?"has-core-specific-parts":"object"==typeof a&&null!==a&&"role"in a&&"content"in a&&"string"==typeof a.content&&["system","user","assistant","tool"].includes(a.role)?"message":"other"}function co(a){return"image"===a.type?{...a,image:a.image instanceof Uint8Array?bX(a.image):a.image}:a}ar({prefix:"aiobj",size:24}),ar({prefix:"aiobj",size:24});var cp="AI_NoOutputSpecifiedError",cq=`vercel.ai.error.${cp}`,cr=Symbol.for(cq),cs=class extends E{constructor({message:a="No output specified."}={}){super({name:cp,message:a}),this[v]=!0}static isInstance(a){return E.hasMarker(a,cq)}};v=cr;var ct="AI_ToolExecutionError",cu=`vercel.ai.error.${ct}`,cv=Symbol.for(cu),cw=class extends E{constructor({toolArgs:a,toolName:b,toolCallId:c,cause:d,message:e=`Error executing tool ${b}: ${N(d)}`}){super({name:ct,message:e,cause:d}),this[w]=!0,this.toolArgs=a,this.toolName=b,this.toolCallId=c}static isInstance(a){return E.hasMarker(a,cu)}};w=cv;var cx=/^([\s\S]*?)(\s+)(\S*)$/,cy="AI_InvalidToolArgumentsError",cz=`vercel.ai.error.${cy}`,cA=Symbol.for(cz),cB=class extends E{constructor({toolArgs:a,toolName:b,cause:c,message:d=`Invalid arguments for tool ${b}: ${N(c)}`}){super({name:cy,message:d,cause:c}),this[x]=!0,this.toolArgs=a,this.toolName=b}static isInstance(a){return E.hasMarker(a,cz)}};x=cA;var cC="AI_NoSuchToolError",cD=`vercel.ai.error.${cC}`,cE=Symbol.for(cD),cF=class extends E{constructor({toolName:a,availableTools:b,message:c=`Model tried to call unavailable tool '${a}'. ${void 0===b?"No tools are available.":`Available tools: ${b.join(", ")}.`}`}){super({name:cC,message:c}),this[y]=!0,this.toolName=a,this.availableTools=b}static isInstance(a){return E.hasMarker(a,cD)}};y=cE;var cG="AI_ToolCallRepairError",cH=`vercel.ai.error.${cG}`,cI=Symbol.for(cH),cJ=class extends E{constructor({cause:a,originalError:b,message:c=`Error repairing tool call: ${N(a)}`}){super({name:cG,message:c,cause:a}),this[z]=!0,this.originalError=b}static isInstance(a){return E.hasMarker(a,cH)}};async function cK({toolCall:a,tools:b,repairToolCall:c,system:d,messages:e}){if(null==b)throw new cF({toolName:a.toolName});try{return await cL({toolCall:a,tools:b})}catch(g){if(null==c||!(cF.isInstance(g)||cB.isInstance(g)))throw g;let f=null;try{f=await c({toolCall:a,tools:b,parameterSchema:({toolName:a})=>bm(b[a].parameters).jsonSchema,system:d,messages:e,error:g})}catch(a){throw new cJ({cause:a,originalError:g})}if(null==f)throw g;return await cL({toolCall:f,tools:b})}}async function cL({toolCall:a,tools:b}){let c=a.toolName,d=b[c];if(null==d)throw new cF({toolName:a.toolName,availableTools:Object.keys(b)});let e=bm(d.parameters),f=""===a.args.trim()?au({value:{},schema:e}):av({text:a.args,schema:e});if(!1===f.success)throw new cB({toolName:c,toolArgs:a.args,cause:f.error});return{type:"tool-call",toolCallId:a.toolCallId,toolName:c,args:f.value}}function cM(a){let b=a.filter(a=>"text"===a.type).map(a=>a.text).join("");return b.length>0?b:void 0}z=cI;var cN=ar({prefix:"aitxt",size:24}),cO=ar({prefix:"msg",size:24});async function cP({model:a,tools:b,toolChoice:c,system:d,prompt:e,messages:f,maxRetries:g,abortSignal:h,headers:i,maxSteps:j=1,experimental_generateMessageId:k=cO,experimental_output:l,experimental_continueSteps:m=!1,experimental_telemetry:n,experimental_providerMetadata:o,providerOptions:p=o,experimental_activeTools:q,experimental_prepareStep:r,experimental_repairToolCall:s,_internal:{generateId:t=cN,currentDate:u=()=>new Date}={},onStepFinish:v,...w}){var x;if("string"==typeof a||"v1"!==a.specificationVersion)throw new bq;if(j<1)throw new bu({parameter:"maxSteps",value:j,message:"maxSteps must be at least 1"});let{maxRetries:y,retry:z}=function({maxRetries:a}){if(null!=a){if(!Number.isInteger(a))throw new bu({parameter:"maxRetries",value:a,message:"maxRetries must be an integer"});if(a<0)throw new bu({parameter:"maxRetries",value:a,message:"maxRetries must be >= 0"})}let b=null!=a?a:2;return{maxRetries:b,retry:(({maxRetries:a=2,initialDelayInMs:b=2e3,backoffFactor:c=2}={})=>async d=>bz(d,{maxRetries:a,delayInMs:b,backoffFactor:c}))({maxRetries:b})}}({maxRetries:g}),A=function({model:a,settings:b,telemetry:c,headers:d}){var e;return{"ai.model.provider":a.provider,"ai.model.id":a.modelId,...Object.entries(b).reduce((a,[b,c])=>(a[`ai.settings.${b}`]=c,a),{}),...Object.entries(null!=(e=null==c?void 0:c.metadata)?e:{}).reduce((a,[b,c])=>(a[`ai.telemetry.metadata.${b}`]=c,a),{}),...Object.entries(null!=d?d:{}).reduce((a,[b,c])=>(void 0!==c&&(a[`ai.request.headers.${b}`]=c),a),{})}}({model:a,telemetry:n,headers:i,settings:{...w,maxRetries:y}}),B=function({prompt:a,tools:b}){if(null==a.prompt&&null==a.messages)throw new V({prompt:a,message:"prompt or messages must be defined"});if(null!=a.prompt&&null!=a.messages)throw new V({prompt:a,message:"prompt and messages cannot be defined at the same time"});if(null!=a.system&&"string"!=typeof a.system)throw new V({prompt:a,message:"system must be a string"});if(null!=a.prompt){if("string"!=typeof a.prompt)throw new V({prompt:a,message:"prompt must be a string"});return{type:"prompt",system:a.system,messages:[{role:"user",content:a.prompt}]}}if(null!=a.messages){let c="ui-messages"===function(a){if(!Array.isArray(a))throw new V({prompt:a,message:`messages must be an array of CoreMessage or UIMessage
Received non-array value: ${JSON.stringify(a)}`,cause:a});if(0===a.length)return"messages";let b=a.map(cn);if(b.some(a=>"has-ui-specific-parts"===a))return"ui-messages";let c=b.findIndex(a=>"has-core-specific-parts"!==a&&"message"!==a);if(-1===c)return"messages";throw new V({prompt:a,message:`messages must be an array of CoreMessage or UIMessage
Received message of type: "${b[c]}" at index ${c}
messages[${c}]: ${JSON.stringify(a[c])}`,cause:a})}(a.messages)?function(a,b){var c,d;let e=null!=(c=null==b?void 0:b.tools)?c:{},f=[];for(let b=0;b<a.length;b++){let c=a[b],g=b===a.length-1,{role:h,content:i,experimental_attachments:j}=c;switch(h){case"system":f.push({role:"system",content:i});break;case"user":if(null==c.parts)f.push({role:"user",content:j?[{type:"text",text:i},...b3(j)]:i});else{let a=c.parts.filter(a=>"text"===a.type).map(a=>({type:"text",text:a.text}));f.push({role:"user",content:j?[...a,...b3(j)]:a})}break;case"assistant":{if(null!=c.parts){let a=function(){let a=[];for(let b of h)switch(b.type){case"file":case"text":a.push(b);break;case"reasoning":for(let c of b.details)switch(c.type){case"text":a.push({type:"reasoning",text:c.text,signature:c.signature});break;case"redacted":a.push({type:"redacted-reasoning",data:c.data})}break;case"tool-invocation":a.push({type:"tool-call",toolCallId:b.toolInvocation.toolCallId,toolName:b.toolInvocation.toolName,args:b.toolInvocation.args});break;default:throw Error(`Unsupported part: ${b}`)}f.push({role:"assistant",content:a});let d=h.filter(a=>"tool-invocation"===a.type).map(a=>a.toolInvocation);d.length>0&&f.push({role:"tool",content:d.map(a=>{if(!("result"in a))throw new b7({originalMessage:c,message:"ToolInvocation must have a result: "+JSON.stringify(a)});let{toolCallId:b,toolName:d,result:f}=a,g=e[d];return(null==g?void 0:g.experimental_toToolResultContent)!=null?{type:"tool-result",toolCallId:b,toolName:d,result:g.experimental_toToolResultContent(f),experimental_content:g.experimental_toToolResultContent(f)}:{type:"tool-result",toolCallId:b,toolName:d,result:f}})}),h=[],g=!1,b++},b=0,g=!1,h=[];for(let e of c.parts)switch(e.type){case"text":g&&a(),h.push(e);break;case"file":case"reasoning":h.push(e);break;case"tool-invocation":(null!=(d=e.toolInvocation.step)?d:0)!==b&&a(),h.push(e),g=!0}a();break}let a=c.toolInvocations;if(null==a||0===a.length){f.push({role:"assistant",content:i});break}let b=a.reduce((a,b)=>{var c;return Math.max(a,null!=(c=b.step)?c:0)},0);for(let d=0;d<=b;d++){let b=a.filter(a=>{var b;return(null!=(b=a.step)?b:0)===d});0!==b.length&&(f.push({role:"assistant",content:[...g&&i&&0===d?[{type:"text",text:i}]:[],...b.map(({toolCallId:a,toolName:b,args:c})=>({type:"tool-call",toolCallId:a,toolName:b,args:c}))]}),f.push({role:"tool",content:b.map(a=>{if(!("result"in a))throw new b7({originalMessage:c,message:"ToolInvocation must have a result: "+JSON.stringify(a)});let{toolCallId:b,toolName:d,result:f}=a,g=e[d];return(null==g?void 0:g.experimental_toToolResultContent)!=null?{type:"tool-result",toolCallId:b,toolName:d,result:g.experimental_toToolResultContent(f),experimental_content:g.experimental_toToolResultContent(f)}:{type:"tool-result",toolCallId:b,toolName:d,result:f}})}))}i&&!g&&f.push({role:"assistant",content:i});break}case"data":break;default:throw new b7({originalMessage:c,message:`Unsupported role: ${h}`})}}return f}(a.messages,{tools:b}):a.messages;if(0===c.length)throw new V({prompt:a,message:"messages must not be empty"});let d=au({value:c,schema:aE.YO(cm)});if(!d.success)throw new V({prompt:a,message:`message must be a CoreMessage or a UI message
Validation error: ${d.error.message}`,cause:d.error});return{type:"messages",messages:c,system:a.system}}throw Error("unreachable")}({prompt:{system:null!=(x=null==l?void 0:l.injectIntoSystemPrompt({system:d,model:a}))?x:d,prompt:e,messages:f},tools:b}),C=function({isEnabled:a=!1,tracer:b}={}){return a?b||aT.u.getTracer("ai"):bB}(n);return bE({name:"ai.generateText",attributes:bG({telemetry:n,attributes:{...bA({operationId:"ai.generateText",telemetry:n}),...A,"ai.model.provider":a.provider,"ai.model.id":a.modelId,"ai.prompt":{input:()=>JSON.stringify({system:d,prompt:e,messages:f})},"ai.settings.maxSteps":j}}),tracer:C,fn:async e=>{var f,g,o,x,y,D,E,F,G,H,I,J,K,L,M;let N,O=function({maxTokens:a,temperature:b,topP:c,topK:d,presencePenalty:e,frequencyPenalty:f,stopSequences:g,seed:h}){if(null!=a){if(!Number.isInteger(a))throw new bu({parameter:"maxTokens",value:a,message:"maxTokens must be an integer"});if(a<1)throw new bu({parameter:"maxTokens",value:a,message:"maxTokens must be >= 1"})}if(null!=b&&"number"!=typeof b)throw new bu({parameter:"temperature",value:b,message:"temperature must be a number"});if(null!=c&&"number"!=typeof c)throw new bu({parameter:"topP",value:c,message:"topP must be a number"});if(null!=d&&"number"!=typeof d)throw new bu({parameter:"topK",value:d,message:"topK must be a number"});if(null!=e&&"number"!=typeof e)throw new bu({parameter:"presencePenalty",value:e,message:"presencePenalty must be a number"});if(null!=f&&"number"!=typeof f)throw new bu({parameter:"frequencyPenalty",value:f,message:"frequencyPenalty must be a number"});if(null!=h&&!Number.isInteger(h))throw new bu({parameter:"seed",value:h,message:"seed must be an integer"});return{maxTokens:a,temperature:null!=b?b:0,topP:c,topK:d,presencePenalty:e,frequencyPenalty:f,stopSequences:null!=g&&g.length>0?g:void 0,seed:h}}(w),P=[],Q=[],R=[],S=0,T=[],U="",V=[],W=[],X={completionTokens:0,promptTokens:0,totalTokens:0},Y="initial";do{let e=0===S?B.type:"messages",J=[...B.messages,...T],K=await (null==r?void 0:r({model:a,steps:W,maxSteps:j,stepNumber:S})),L=null!=(f=null==K?void 0:K.toolChoice)?f:c,Z=null!=(g=null==K?void 0:K.experimental_activeTools)?g:q,$=null!=(o=null==K?void 0:K.model)?o:a,_=await b1({prompt:{type:e,system:B.system,messages:J},modelSupportsImageUrls:$.supportsImageUrls,modelSupportsUrl:null==(x=$.supportsUrl)?void 0:x.bind($)}),aa={type:"regular",...function({tools:a,toolChoice:b,activeTools:c}){return null!=a&&Object.keys(a).length>0?{tools:(null!=c?Object.entries(a).filter(([a])=>c.includes(a)):Object.entries(a)).map(([a,b])=>{let c=b.type;switch(c){case void 0:case"function":return{type:"function",name:a,description:b.description,parameters:bm(b.parameters).jsonSchema};case"provider-defined":return{type:"provider-defined",name:a,id:b.id,args:b.args};default:throw Error(`Unsupported tool type: ${c}`)}}),toolChoice:null==b?{type:"auto"}:"string"==typeof b?{type:b}:{type:"tool",toolName:b.toolName}}:{tools:void 0,toolChoice:void 0}}({tools:b,toolChoice:L,activeTools:Z})};N=await z(()=>bE({name:"ai.generateText.doGenerate",attributes:bG({telemetry:n,attributes:{...bA({operationId:"ai.generateText.doGenerate",telemetry:n}),...A,"ai.model.provider":$.provider,"ai.model.id":$.modelId,"ai.prompt.format":{input:()=>e},"ai.prompt.messages":{input:()=>JSON.stringify(_.map(a=>({...a,content:"string"==typeof a.content?a.content:a.content.map(co)})))},"ai.prompt.tools":{input:()=>{var a;return null==(a=aa.tools)?void 0:a.map(a=>JSON.stringify(a))}},"ai.prompt.toolChoice":{input:()=>null!=aa.toolChoice?JSON.stringify(aa.toolChoice):void 0},"gen_ai.system":$.provider,"gen_ai.request.model":$.modelId,"gen_ai.request.frequency_penalty":w.frequencyPenalty,"gen_ai.request.max_tokens":w.maxTokens,"gen_ai.request.presence_penalty":w.presencePenalty,"gen_ai.request.stop_sequences":w.stopSequences,"gen_ai.request.temperature":w.temperature,"gen_ai.request.top_k":w.topK,"gen_ai.request.top_p":w.topP}}),tracer:C,fn:async b=>{var c,d,f,g,j,k;let m=await $.doGenerate({mode:aa,...O,inputFormat:e,responseFormat:null==l?void 0:l.responseFormat({model:a}),prompt:_,providerMetadata:p,abortSignal:h,headers:i}),o={id:null!=(d=null==(c=m.response)?void 0:c.id)?d:t(),timestamp:null!=(g=null==(f=m.response)?void 0:f.timestamp)?g:u(),modelId:null!=(k=null==(j=m.response)?void 0:j.modelId)?k:$.modelId};return b.setAttributes(bG({telemetry:n,attributes:{"ai.response.finishReason":m.finishReason,"ai.response.text":{output:()=>m.text},"ai.response.toolCalls":{output:()=>JSON.stringify(m.toolCalls)},"ai.response.id":o.id,"ai.response.model":o.modelId,"ai.response.timestamp":o.timestamp.toISOString(),"ai.response.providerMetadata":JSON.stringify(m.providerMetadata),"ai.usage.promptTokens":m.usage.promptTokens,"ai.usage.completionTokens":m.usage.completionTokens,"gen_ai.response.finish_reasons":[m.finishReason],"gen_ai.response.id":o.id,"gen_ai.response.model":o.modelId,"gen_ai.usage.input_tokens":m.usage.promptTokens,"gen_ai.usage.output_tokens":m.usage.completionTokens}})),{...m,response:o}}})),P=await Promise.all((null!=(y=N.toolCalls)?y:[]).map(a=>cK({toolCall:a,tools:b,repairToolCall:s,system:d,messages:J}))),Q=null==b?[]:await cQ({toolCalls:P,tools:b,tracer:C,telemetry:n,messages:J,abortSignal:h});let ab=function({promptTokens:a,completionTokens:b}){return{promptTokens:a,completionTokens:b,totalTokens:a+b}}(N.usage);X={promptTokens:(M=X).promptTokens+ab.promptTokens,completionTokens:M.completionTokens+ab.completionTokens,totalTokens:M.totalTokens+ab.totalTokens};let ac="done";++S<j&&(m&&"length"===N.finishReason&&0===P.length?ac="continue":P.length>0&&Q.length===P.length&&(ac="tool-result"));let ad=null!=(D=N.text)?D:"",ae="continue"===Y&&U.trimEnd()!==U?ad.trimStart():ad,af="continue"===ac?function(a){let b=function(a){let b=a.match(cx);return b?{prefix:b[1],whitespace:b[2],suffix:b[3]}:void 0}(a);return b?b.prefix+b.whitespace:a}(ae):ae;if(U="continue"===ac||"continue"===Y?U+af:af,R=cS(N.reasoning),V.push(...null!=(E=N.sources)?E:[]),"continue"===Y){let a=T[T.length-1];"string"==typeof a.content?a.content+=af:a.content.push({text:af,type:"text"})}else T.push(...function({text:a="",files:b,reasoning:c,tools:d,toolCalls:e,toolResults:f,messageId:g,generateMessageId:h}){let i=[],j=[];return c.length>0&&j.push(...c.map(a=>"text"===a.type?{...a,type:"reasoning"}:{...a,type:"redacted-reasoning"})),b.length>0&&j.push(...b.map(a=>({type:"file",data:a.base64,mimeType:a.mimeType}))),a.length>0&&j.push({type:"text",text:a}),e.length>0&&j.push(...e),j.length>0&&i.push({role:"assistant",content:j,id:g}),f.length>0&&i.push({role:"tool",id:h(),content:f.map(a=>{let b=d[a.toolName];return(null==b?void 0:b.experimental_toToolResultContent)!=null?{type:"tool-result",toolCallId:a.toolCallId,toolName:a.toolName,result:b.experimental_toToolResultContent(a.result),experimental_content:b.experimental_toToolResultContent(a.result)}:{type:"tool-result",toolCallId:a.toolCallId,toolName:a.toolName,result:a.result}})}),i}({text:U,files:cT(N.files),reasoning:cS(N.reasoning),tools:null!=b?b:{},toolCalls:P,toolResults:Q,messageId:k(),generateMessageId:k}));let ag={stepType:Y,text:af,reasoning:cM(R),reasoningDetails:R,files:cT(N.files),sources:null!=(F=N.sources)?F:[],toolCalls:P,toolResults:Q,finishReason:N.finishReason,usage:ab,warnings:N.warnings,logprobs:N.logprobs,request:null!=(G=N.request)?G:{},response:{...N.response,headers:null==(H=N.rawResponse)?void 0:H.headers,body:null==(I=N.rawResponse)?void 0:I.body,messages:structuredClone(T)},providerMetadata:N.providerMetadata,experimental_providerMetadata:N.providerMetadata,isContinued:"continue"===ac};W.push(ag),await (null==v?void 0:v(ag)),Y=ac}while("done"!==Y);return e.setAttributes(bG({telemetry:n,attributes:{"ai.response.finishReason":N.finishReason,"ai.response.text":{output:()=>N.text},"ai.response.toolCalls":{output:()=>JSON.stringify(N.toolCalls)},"ai.usage.promptTokens":N.usage.promptTokens,"ai.usage.completionTokens":N.usage.completionTokens,"ai.response.providerMetadata":JSON.stringify(N.providerMetadata)}})),new cR({text:U,files:cT(N.files),reasoning:cM(R),reasoningDetails:R,sources:V,outputResolver:()=>{if(null==l)throw new cs;return l.parseOutput({text:U},{response:N.response,usage:X,finishReason:N.finishReason})},toolCalls:P,toolResults:Q,finishReason:N.finishReason,usage:X,warnings:N.warnings,request:null!=(J=N.request)?J:{},response:{...N.response,headers:null==(K=N.rawResponse)?void 0:K.headers,body:null==(L=N.rawResponse)?void 0:L.body,messages:T},logprobs:N.logprobs,steps:W,providerMetadata:N.providerMetadata})}})}async function cQ({toolCalls:a,tools:b,tracer:c,telemetry:d,messages:e,abortSignal:f}){return(await Promise.all(a.map(async({toolCallId:a,toolName:g,args:h})=>{let i=b[g];if((null==i?void 0:i.execute)==null)return;let j=await bE({name:"ai.toolCall",attributes:bG({telemetry:d,attributes:{...bA({operationId:"ai.toolCall",telemetry:d}),"ai.toolCall.name":g,"ai.toolCall.id":a,"ai.toolCall.args":{output:()=>JSON.stringify(h)}}}),tracer:c,fn:async b=>{try{let c=await i.execute(h,{toolCallId:a,messages:e,abortSignal:f});try{b.setAttributes(bG({telemetry:d,attributes:{"ai.toolCall.result":{output:()=>JSON.stringify(c)}}}))}catch(a){}return c}catch(c){throw bF(b,c),new cw({toolCallId:a,toolName:g,toolArgs:h,cause:c})}}});return{type:"tool-result",toolCallId:a,toolName:g,args:h,result:j}}))).filter(a=>null!=a)}var cR=class{constructor(a){this.text=a.text,this.files=a.files,this.reasoning=a.reasoning,this.reasoningDetails=a.reasoningDetails,this.toolCalls=a.toolCalls,this.toolResults=a.toolResults,this.finishReason=a.finishReason,this.usage=a.usage,this.warnings=a.warnings,this.request=a.request,this.response=a.response,this.steps=a.steps,this.experimental_providerMetadata=a.providerMetadata,this.providerMetadata=a.providerMetadata,this.logprobs=a.logprobs,this.outputResolver=a.outputResolver,this.sources=a.sources}get experimental_output(){return this.outputResolver()}};function cS(a){return null==a?[]:"string"==typeof a?[{type:"text",text:a}]:a}function cT(a){var b;return null!=(b=null==a?void 0:a.map(a=>new bH(a)))?b:[]}bo({},{object:()=>cV,text:()=>cU}),Symbol.for("vercel.ai.error.AI_InvalidStreamPartError"),Symbol.for("vercel.ai.error.AI_MCPClientError");var cU=()=>({type:"text",responseFormat:()=>({type:"text"}),injectIntoSystemPrompt:({system:a})=>a,parsePartial:({text:a})=>({partial:a}),parseOutput:({text:a})=>a}),cV=({schema:a})=>{let b=bm(a);return{type:"object",responseFormat:({model:a})=>({type:"json",schema:a.supportsStructuredOutputs?b.jsonSchema:void 0}),injectIntoSystemPrompt:({system:a,model:c})=>c.supportsStructuredOutputs?a:function({prompt:a,schema:b,schemaPrefix:c=null!=b?"JSON schema:":void 0,schemaSuffix:d=null!=b?"You MUST answer with a JSON object that matches the JSON schema above.":"You MUST answer with JSON."}){return[null!=a&&a.length>0?a:void 0,null!=a&&a.length>0?"":void 0,c,null!=b?JSON.stringify(b):void 0,d].filter(a=>null!=a).join("\n")}({prompt:a,schema:b.jsonSchema}),parsePartial({text:a}){let b=function(a){if(void 0===a)return{value:void 0,state:"undefined-input"};let b=av({text:a});return b.success?{value:b.value,state:"successful-parse"}:(b=av({text:function(a){let b=["ROOT"],c=-1,d=null;function e(a,e,f){switch(a){case'"':c=e,b.pop(),b.push(f),b.push("INSIDE_STRING");break;case"f":case"t":case"n":c=e,d=e,b.pop(),b.push(f),b.push("INSIDE_LITERAL");break;case"-":b.pop(),b.push(f),b.push("INSIDE_NUMBER");break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":c=e,b.pop(),b.push(f),b.push("INSIDE_NUMBER");break;case"{":c=e,b.pop(),b.push(f),b.push("INSIDE_OBJECT_START");break;case"[":c=e,b.pop(),b.push(f),b.push("INSIDE_ARRAY_START")}}function f(a,d){switch(a){case",":b.pop(),b.push("INSIDE_OBJECT_AFTER_COMMA");break;case"}":c=d,b.pop()}}function g(a,d){switch(a){case",":b.pop(),b.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":c=d,b.pop()}}for(let h=0;h<a.length;h++){let i=a[h];switch(b[b.length-1]){case"ROOT":e(i,h,"FINISH");break;case"INSIDE_OBJECT_START":switch(i){case'"':b.pop(),b.push("INSIDE_OBJECT_KEY");break;case"}":c=h,b.pop()}break;case"INSIDE_OBJECT_AFTER_COMMA":'"'===i&&(b.pop(),b.push("INSIDE_OBJECT_KEY"));break;case"INSIDE_OBJECT_KEY":'"'===i&&(b.pop(),b.push("INSIDE_OBJECT_AFTER_KEY"));break;case"INSIDE_OBJECT_AFTER_KEY":":"===i&&(b.pop(),b.push("INSIDE_OBJECT_BEFORE_VALUE"));break;case"INSIDE_OBJECT_BEFORE_VALUE":e(i,h,"INSIDE_OBJECT_AFTER_VALUE");break;case"INSIDE_OBJECT_AFTER_VALUE":f(i,h);break;case"INSIDE_STRING":switch(i){case'"':b.pop(),c=h;break;case"\\":b.push("INSIDE_STRING_ESCAPE");break;default:c=h}break;case"INSIDE_ARRAY_START":"]"===i?(c=h,b.pop()):(c=h,e(i,h,"INSIDE_ARRAY_AFTER_VALUE"));break;case"INSIDE_ARRAY_AFTER_VALUE":switch(i){case",":b.pop(),b.push("INSIDE_ARRAY_AFTER_COMMA");break;case"]":c=h,b.pop();break;default:c=h}break;case"INSIDE_ARRAY_AFTER_COMMA":e(i,h,"INSIDE_ARRAY_AFTER_VALUE");break;case"INSIDE_STRING_ESCAPE":b.pop(),c=h;break;case"INSIDE_NUMBER":switch(i){case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":c=h;break;case"e":case"E":case"-":case".":break;case",":b.pop(),"INSIDE_ARRAY_AFTER_VALUE"===b[b.length-1]&&g(i,h),"INSIDE_OBJECT_AFTER_VALUE"===b[b.length-1]&&f(i,h);break;case"}":b.pop(),"INSIDE_OBJECT_AFTER_VALUE"===b[b.length-1]&&f(i,h);break;case"]":b.pop(),"INSIDE_ARRAY_AFTER_VALUE"===b[b.length-1]&&g(i,h);break;default:b.pop()}break;case"INSIDE_LITERAL":{let e=a.substring(d,h+1);"false".startsWith(e)||"true".startsWith(e)||"null".startsWith(e)?c=h:(b.pop(),"INSIDE_OBJECT_AFTER_VALUE"===b[b.length-1]?f(i,h):"INSIDE_ARRAY_AFTER_VALUE"===b[b.length-1]&&g(i,h))}}}let h=a.slice(0,c+1);for(let c=b.length-1;c>=0;c--)switch(b[c]){case"INSIDE_STRING":h+='"';break;case"INSIDE_OBJECT_KEY":case"INSIDE_OBJECT_AFTER_KEY":case"INSIDE_OBJECT_AFTER_COMMA":case"INSIDE_OBJECT_START":case"INSIDE_OBJECT_BEFORE_VALUE":case"INSIDE_OBJECT_AFTER_VALUE":h+="}";break;case"INSIDE_ARRAY_START":case"INSIDE_ARRAY_AFTER_COMMA":case"INSIDE_ARRAY_AFTER_VALUE":h+="]";break;case"INSIDE_LITERAL":{let b=a.substring(d,a.length);"true".startsWith(b)?h+="true".slice(b.length):"false".startsWith(b)?h+="false".slice(b.length):"null".startsWith(b)&&(h+="null".slice(b.length))}}return h}(a)})).success?{value:b.value,state:"repaired-parse"}:{value:void 0,state:"failed-parse"}}(a);switch(b.state){case"failed-parse":case"undefined-input":return;case"repaired-parse":case"successful-parse":return{partial:b.value};default:{let a=b.state;throw Error(`Unsupported parse state: ${a}`)}}},parseOutput({text:a},c){let d=av({text:a});if(!d.success)throw new bM({message:"No object generated: could not parse the response.",cause:d.error,text:a,response:c.response,usage:c.usage,finishReason:c.finishReason});let e=au({value:d.value,schema:b});if(!e.success)throw new bM({message:"No object generated: response did not match schema.",cause:e.error,text:a,response:c.response,usage:c.usage,finishReason:c.finishReason});return e.value}}};function cW(a,b){let c,d,e=a.getReader(),f=b.getReader(),g=!1,h=!1;async function i(a){try{null==c&&(c=e.read());let b=await c;c=void 0,b.done?a.close():a.enqueue(b.value)}catch(b){a.error(b)}}async function j(a){try{null==d&&(d=f.read());let b=await d;d=void 0,b.done?a.close():a.enqueue(b.value)}catch(b){a.error(b)}}return new ReadableStream({async pull(a){try{if(g)return void await j(a);if(h)return void await i(a);null==c&&(c=e.read()),null==d&&(d=f.read());let{result:b,reader:k}=await Promise.race([c.then(a=>({result:a,reader:e})),d.then(a=>({result:a,reader:f}))]);b.done||a.enqueue(b.value),k===e?(c=void 0,b.done&&(await j(a),g=!0)):(d=void 0,b.done&&(h=!0,await i(a)))}catch(b){a.error(b)}},cancel(){e.cancel(),f.cancel()}})}ar({prefix:"aitxt",size:24}),ar({prefix:"msg",size:24}),Symbol.for("vercel.ai.error.AI_NoSuchProviderError");var cX=aE.Ik({name:aE.Yj(),version:aE.Yj()}).passthrough(),cY=aE.Ik({_meta:aE.lq(aE.Ik({}).passthrough())}).passthrough(),cZ=aE.Ik({method:aE.Yj(),params:aE.lq(cY)}),c$=aE.Ik({experimental:aE.lq(aE.Ik({}).passthrough()),logging:aE.lq(aE.Ik({}).passthrough()),prompts:aE.lq(aE.Ik({listChanged:aE.lq(aE.zM())}).passthrough()),resources:aE.lq(aE.Ik({subscribe:aE.lq(aE.zM()),listChanged:aE.lq(aE.zM())}).passthrough()),tools:aE.lq(aE.Ik({listChanged:aE.lq(aE.zM())}).passthrough())}).passthrough();cY.extend({protocolVersion:aE.Yj(),capabilities:c$,serverInfo:cX,instructions:aE.lq(aE.Yj())});var c_=cY.extend({nextCursor:aE.lq(aE.Yj())}),c0=aE.Ik({name:aE.Yj(),description:aE.lq(aE.Yj()),inputSchema:aE.Ik({type:aE.eu("object"),properties:aE.lq(aE.Ik({}).passthrough())}).passthrough()}).passthrough();c_.extend({tools:aE.YO(c0)});var c1=aE.Ik({type:aE.eu("text"),text:aE.Yj()}).passthrough(),c2=aE.Ik({type:aE.eu("image"),data:aE.Yj().base64(),mimeType:aE.Yj()}).passthrough(),c3=aE.Ik({uri:aE.Yj(),mimeType:aE.lq(aE.Yj())}).passthrough(),c4=c3.extend({text:aE.Yj()}),c5=c3.extend({blob:aE.Yj().base64()}),c6=aE.Ik({type:aE.eu("resource"),resource:aE.KC([c4,c5])}).passthrough();cY.extend({content:aE.YO(aE.KC([c1,c2,c6])),isError:aE.zM().default(!1).optional()}).or(cY.extend({toolResult:aE.L5()}));var c7=aE.Ik({jsonrpc:aE.eu("2.0"),id:aE.KC([aE.Yj(),aE.ai().int()])}).merge(cZ).strict(),c8=aE.Ik({jsonrpc:aE.eu("2.0"),id:aE.KC([aE.Yj(),aE.ai().int()]),result:cY}).strict(),c9=aE.Ik({jsonrpc:aE.eu("2.0"),id:aE.KC([aE.Yj(),aE.ai().int()]),error:aE.Ik({code:aE.ai().int(),message:aE.Yj(),data:aE.lq(aE.L5())})}).strict(),da=aE.Ik({jsonrpc:aE.eu("2.0")}).merge(aE.Ik({method:aE.Yj(),params:aE.lq(cY)})).strict();function db(a={}){let b=new TextEncoder,c="";return new TransformStream({async start(){a.onStart&&await a.onStart()},async transform(d,e){e.enqueue(b.encode(d)),c+=d,a.onToken&&await a.onToken(d),a.onText&&"string"==typeof d&&await a.onText(d)},async flush(){a.onCompletion&&await a.onCompletion(c),a.onFinal&&await a.onFinal(c)}})}function dc(a,b){return a.pipeThrough(new TransformStream({transform:async(a,b)=>{var c;if("string"==typeof a)return void b.enqueue(a);if("event"in a){"on_chat_model_stream"===a.event&&dg(null==(c=a.data)?void 0:c.chunk,b);return}dg(a,b)}})).pipeThrough(db(b)).pipeThrough(new TextDecoderStream).pipeThrough(new TransformStream({transform:async(a,b)=>{b.enqueue(bk("text",a))}}))}function dd(a,b){return dc(a,b).pipeThrough(new TextEncoderStream)}function de(a,b){var c;let d=dc(a,null==b?void 0:b.callbacks).pipeThrough(new TextEncoderStream),e=null==b?void 0:b.data,f=null==b?void 0:b.init;return new Response(e?cW(e.stream,d):d,{status:null!=(c=null==f?void 0:f.status)?c:200,statusText:null==f?void 0:f.statusText,headers:bp(null==f?void 0:f.headers,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}function df(a,b){b.dataStream.merge(dc(a,b.callbacks))}function dg(a,b){if("string"==typeof a.content)b.enqueue(a.content);else for(let c of a.content)"text"===c.type&&b.enqueue(c.text)}function dh(a,b){var c;let d,e=(d=!0,a=>(d&&(a=a.trimStart())&&(d=!1),a));return(c=a[Symbol.asyncIterator](),new ReadableStream({async pull(a){try{let{value:b,done:d}=await c.next();d?a.close():a.enqueue(b)}catch(b){a.error(b)}},cancel(){}})).pipeThrough(new TransformStream({async transform(a,b){b.enqueue(e(a.delta))}})).pipeThrough(db(b)).pipeThrough(new TextDecoderStream).pipeThrough(new TransformStream({transform:async(a,b)=>{b.enqueue(bk("text",a))}}))}function di(a,b){return dh(a,b).pipeThrough(new TextEncoderStream)}function dj(a,b={}){var c;let{init:d,data:e,callbacks:f}=b,g=dh(a,f).pipeThrough(new TextEncoderStream);return new Response(e?cW(e.stream,g):g,{status:null!=(c=null==d?void 0:d.status)?c:200,statusText:null==d?void 0:d.statusText,headers:bp(null==d?void 0:d.headers,{contentType:"text/plain; charset=utf-8",dataStreamVersion:"v1"})})}function dk(a,b){b.dataStream.merge(dh(a,b.callbacks))}aE.KC([c7,da,c8,c9]),bo({},{mergeIntoDataStream:()=>df,toDataStream:()=>dd,toDataStreamResponse:()=>de}),bo({},{mergeIntoDataStream:()=>dk,toDataStream:()=>di,toDataStreamResponse:()=>dj});var dl=c(5872);let dm=aS({apiKey:dl._.ANTHROPIC_API_KEY}),dn=`You are an expert web developer and designer. Your task is to generate a complete, modern, and visually appealing HTML webpage based on the given purpose/description.

IMPORTANT SAFETY REQUIREMENT:
- If the user request is for NSFW content, illegal activities, harmful content, or triggers your safety system in any way, respond with ONLY the text "INAPPROPRIATE_PROMPT_DETECTED" and nothing else.
- Do not generate any HTML, explanations, or other content for inappropriate requests.

REQUIREMENTS:
1. Generate a complete HTML5 document with proper DOCTYPE, head, and body sections
2. Include responsive design using modern CSS (flexbox/grid)
3. Use semantic HTML elements (header, main, section, article, footer, etc.)
4. Include proper meta tags for SEO and viewport
5. Add attractive styling with CSS (embedded in <style> tags in the head)
6. Use modern color schemes and typography
7. Make it desktop and mobile-responsive
8. Include interactive elements where appropriate (buttons, forms, etc.)
9. Add proper accessibility attributes (alt text, ARIA labels, etc.)
10. Use modern CSS features like CSS Grid, Flexbox, custom properties (CSS variables)
11. You must include two following adverts:
  - Advert to https://www.dothistask.ai (logo is https://www.dothistask.ai/icon.png)
    - The copy for this advert must be specific to the use-case for this page + the purpose of dothistask.ai (help knowledge workers find AI solutions for their jobs)
    - The advert (logo + text) must link to https://dothistask.ai
  - Advert to https://everywebsite.ai (no logo)
    - The copy for this advert should indiciate that this page was generated by https://everywebsite.ai and they can generate new pages by just changing the url
    - The advert (text) must link to https://everywebsite.ai
11. You must include a subtle advert to https://dothistask.ai (logo is https://www.dothistask.ai/icon.png)
  - The copy for this advert must be specific to the use-case for this page + the purpose of dothistask.ai (help knowledge workers find AI solutions for their jobs)
  - The advert (logo + text) must link to https://dothistask.ai
12. You have no backend and cannot implement any features that require a backend (database, APIs, AI, etc.) You need to generate the webpage with this in mind
13. Do not include a footer, copyright, etc.. Just a functional webpage
14. You don't have access to images besides the Dothistask logo, you should rely on emojis or svgs that you can define. 

STYLE GUIDELINES:
- Use a modern, clean design aesthetic
- Implement a cohesive color palette
- Use proper typography hierarchy
- Add subtle animations/transitions for better UX
- Ensure good contrast ratios for accessibility
- Use modern CSS techniques (no inline styles except for the main <style> tag)

OUTPUT FORMAT:
- Return ONLY the complete HTML document
- No markdown code blocks or explanations
- Start with <!DOCTYPE html> and end with </html>
- Ensure the HTML is valid and well-formatted
- Limit the output to 4000 tokens

The webpage should be production-ready and look professional.`;async function dp(a){try{let{text:b}=await cP({model:dm("claude-3-5-sonnet-20241022"),system:dn,prompt:`Generate a complete HTML webpage that solves this purpose: "${a}"
      
      Make sure the webpage is:
      - Fully functional and complete
      - Visually appealing and modern
      - Responsive across all devices
      - Accessible and SEO-friendly
      - Professional and production-ready
      
      The purpose "${a}" should guide the content, design, and functionality of the webpage.`,maxTokens:4e3,temperature:.7});return b}catch(a){throw console.error("Error generating webpage with Claude:",a),Error("Failed to generate webpage content")}}var dq=c(5244);let dr=require("node:crypto"),ds=dr.webcrypto?.subtle||{};var dt=Object.defineProperty;((a,b)=>{for(var c in b)dt(a,c,{get:b[c],enumerable:!0})})({},{UpstashError:()=>du,UrlError:()=>dv});var du=class extends Error{constructor(a){super(a),this.name="UpstashError"}},dv=class extends Error{constructor(a){super(`Upstash Redis client was passed an invalid URL. You should pass a URL starting with https. Received: "${a}". `),this.name="UrlError"}};function dw(a){try{return function a(b){let c=Array.isArray(b)?b.map(b=>{try{return a(b)}catch{return b}}):JSON.parse(b);return"number"==typeof c&&c.toString()!==b?b:c}(a)}catch{return a}}function dx(a){return[a[0],...dw(a.slice(1))]}function dy(a){let[b,c]=a,d=[];for(let a=0;a<c.length;a+=2)d.push({key:c[a],type:c[a+1]});return[b,d]}var dz=class{baseUrl;headers;options;readYourWrites;upstashSyncToken="";hasCredentials;retry;constructor(a){if(this.options={backend:a.options?.backend,agent:a.agent,responseEncoding:a.responseEncoding??"base64",cache:a.cache,signal:a.signal,keepAlive:a.keepAlive??!0},this.upstashSyncToken="",this.readYourWrites=a.readYourWrites??!0,this.baseUrl=(a.baseUrl||"").replace(/\/$/,""),this.baseUrl&&!/^https?:\/\/[^\s#$./?].\S*$/.test(this.baseUrl))throw new dv(this.baseUrl);this.headers={"Content-Type":"application/json",...a.headers},this.hasCredentials=!!(this.baseUrl&&this.headers.authorization.split(" ")[1]),"base64"===this.options.responseEncoding&&(this.headers["Upstash-Encoding"]="base64"),this.retry="boolean"!=typeof a.retry||a.retry?{attempts:a.retry?.retries??5,backoff:a.retry?.backoff??(a=>50*Math.exp(a))}:{attempts:1,backoff:()=>0}}mergeTelemetry(a){this.headers=dC(this.headers,"Upstash-Telemetry-Runtime",a.runtime),this.headers=dC(this.headers,"Upstash-Telemetry-Platform",a.platform),this.headers=dC(this.headers,"Upstash-Telemetry-Sdk",a.sdk)}async request(a){let b=function(...a){let b={};for(let c of a)if(c)for(let[a,d]of Object.entries(c))null!=d&&(b[a]=d);return b}(this.headers,a.headers??{}),c=[this.baseUrl,...a.path??[]].join("/"),d="text/event-stream"===b.Accept,e=a.signal??this.options.signal,f="function"==typeof e,g={cache:this.options.cache,method:"POST",headers:b,body:JSON.stringify(a.body),keepalive:this.options.keepAlive,agent:this.options.agent,signal:f?e():e,backend:this.options.backend};if(this.hasCredentials||console.warn("[Upstash Redis] Redis client was initialized without url or token. Failed to execute command."),this.readYourWrites){let a=this.upstashSyncToken;this.headers["upstash-sync-token"]=a}let h=null,i=null;for(let a=0;a<=this.retry.attempts;a++)try{h=await fetch(c,g);break}catch(b){if(g.signal?.aborted&&f)throw b;if(g.signal?.aborted){h=new Response(new Blob([JSON.stringify({result:g.signal.reason??"Aborted"})]),{status:200,statusText:g.signal.reason??"Aborted"});break}i=b,a<this.retry.attempts&&await new Promise(b=>setTimeout(b,this.retry.backoff(a)))}if(!h)throw i??Error("Exhausted all retries");if(!h.ok){let b=await h.json();throw new du(`${b.error}, command was: ${JSON.stringify(a.body)}`)}if(this.readYourWrites){let a=h.headers;this.upstashSyncToken=a.get("upstash-sync-token")??""}if(d&&a&&a.onMessage&&h.body){let b=h.body.getReader(),c=new TextDecoder;return(async()=>{try{for(;;){let{value:d,done:e}=await b.read();if(e)break;for(let b of c.decode(d).split("\n"))if(b.startsWith("data: ")){let c=b.slice(6);a.onMessage?.(c)}}}catch(a){a instanceof Error&&"AbortError"===a.name||console.error("Stream reading error:",a)}finally{try{await b.cancel()}catch{}}})(),{result:1}}let j=await h.json();if(this.readYourWrites){let a=h.headers;this.upstashSyncToken=a.get("upstash-sync-token")??""}return"base64"===this.options.responseEncoding?Array.isArray(j)?j.map(({result:a,error:b})=>({result:dB(a),error:b})):{result:dB(j.result),error:j.error}:j}};function dA(a){let b="";try{let c=atob(a),d=c.length,e=new Uint8Array(d);for(let a=0;a<d;a++)e[a]=c.charCodeAt(a);b=new TextDecoder().decode(e)}catch{b=a}return b}function dB(a){let b;switch(typeof a){case"undefined":return a;case"number":b=a;break;case"object":b=Array.isArray(a)?a.map(a=>"string"==typeof a?dA(a):Array.isArray(a)?a.map(a=>dB(a)):a):null;break;case"string":b="OK"===a?"OK":dA(a)}return b}function dC(a,b,c){return c&&(a[b]=a[b]?[a[b],c].join(","):c),a}var dD=a=>{switch(typeof a){case"string":case"number":case"boolean":return a;default:return JSON.stringify(a)}},dE=class{command;serialize;deserialize;headers;path;onMessage;isStreaming;signal;constructor(a,b){if(this.serialize=dD,this.deserialize=b?.automaticDeserialization===void 0||b.automaticDeserialization?b?.deserialize??dw:a=>a,this.command=a.map(a=>this.serialize(a)),this.headers=b?.headers,this.path=b?.path,this.onMessage=b?.streamOptions?.onMessage,this.isStreaming=b?.streamOptions?.isStreaming??!1,this.signal=b?.streamOptions?.signal,b?.latencyLogging){let a=this.exec.bind(this);this.exec=async b=>{let c=performance.now(),d=await a(b),e=(performance.now()-c).toFixed(2);return console.log(`Latency for \x1b[38;2;19;185;39m${this.command[0].toString().toUpperCase()}\x1b[0m: \x1b[38;2;0;255;255m${e} ms\x1b[0m`),d}}}async exec(a){let{result:b,error:c}=await a.request({body:this.command,path:this.path,upstashSyncToken:a.upstashSyncToken,headers:this.headers,onMessage:this.onMessage,isStreaming:this.isStreaming,signal:this.signal});if(c)throw new du(c);if(void 0===b)throw TypeError("Request did not return a result");return this.deserialize(b)}},dF=class extends dE{constructor(a,b){let c=["hrandfield",a[0]];"number"==typeof a[1]&&c.push(a[1]),a[2]&&c.push("WITHVALUES"),super(c,{deserialize:a[2]?a=>(function(a){if(0===a.length)return null;let b={};for(let c=0;c<a.length;c+=2){let d=a[c],e=a[c+1];try{b[d]=JSON.parse(e)}catch{b[d]=e}}return b})(a):b?.deserialize,...b})}},dG=class extends dE{constructor(a,b){super(["append",...a],b)}},dH=class extends dE{constructor([a,b,c],d){let e=["bitcount",a];"number"==typeof b&&e.push(b),"number"==typeof c&&e.push(c),super(e,d)}},dI=class{constructor(a,b,c,d=a=>a.exec(this.client)){this.client=b,this.opts=c,this.execOperation=d,this.command=["bitfield",...a]}command;chain(...a){return this.command.push(...a),this}get(...a){return this.chain("get",...a)}set(...a){return this.chain("set",...a)}incrby(...a){return this.chain("incrby",...a)}overflow(a){return this.chain("overflow",a)}exec(){let a=new dE(this.command,this.opts);return this.execOperation(a)}},dJ=class extends dE{constructor(a,b){super(["bitop",...a],b)}},dK=class extends dE{constructor(a,b){super(["bitpos",...a],b)}},dL=class extends dE{constructor([a,b,c],d){super(["COPY",a,b,...c?.replace?["REPLACE"]:[]],{...d,deserialize:a=>a>0?"COPIED":"NOT_COPIED"})}},dM=class extends dE{constructor(a){super(["dbsize"],a)}},dN=class extends dE{constructor(a,b){super(["decr",...a],b)}},dO=class extends dE{constructor(a,b){super(["decrby",...a],b)}},dP=class extends dE{constructor(a,b){super(["del",...a],b)}},dQ=class extends dE{constructor(a,b){super(["echo",...a],b)}},dR=class extends dE{constructor([a,b,c],d){super(["eval_ro",a,b.length,...b,...c??[]],d)}},dS=class extends dE{constructor([a,b,c],d){super(["eval",a,b.length,...b,...c??[]],d)}},dT=class extends dE{constructor([a,b,c],d){super(["evalsha_ro",a,b.length,...b,...c??[]],d)}},dU=class extends dE{constructor([a,b,c],d){super(["evalsha",a,b.length,...b,...c??[]],d)}},dV=class extends dE{constructor(a,b){super(a.map(a=>"string"==typeof a?a:String(a)),b)}},dW=class extends dE{constructor(a,b){super(["exists",...a],b)}},dX=class extends dE{constructor(a,b){super(["expire",...a.filter(Boolean)],b)}},dY=class extends dE{constructor(a,b){super(["expireat",...a],b)}},dZ=class extends dE{constructor(a,b){let c=["flushall"];a&&a.length>0&&a[0].async&&c.push("async"),super(c,b)}},d$=class extends dE{constructor([a],b){let c=["flushdb"];a?.async&&c.push("async"),super(c,b)}},d_=class extends dE{constructor([a,b,...c],d){let e=["geoadd",a];"nx"in b&&b.nx?e.push("nx"):"xx"in b&&b.xx&&e.push("xx"),"ch"in b&&b.ch&&e.push("ch"),"latitude"in b&&b.latitude&&e.push(b.longitude,b.latitude,b.member),e.push(...c.flatMap(({latitude:a,longitude:b,member:c})=>[b,a,c])),super(e,d)}},d0=class extends dE{constructor([a,b,c,d="M"],e){super(["GEODIST",a,b,c,d],e)}},d1=class extends dE{constructor(a,b){let[c]=a;super(["GEOHASH",c,...Array.isArray(a[1])?a[1]:a.slice(1)],b)}},d2=class extends dE{constructor(a,b){let[c]=a;super(["GEOPOS",c,...Array.isArray(a[1])?a[1]:a.slice(1)],{deserialize:a=>(function(a){let b=[];for(let c of a)c?.[0]&&c?.[1]&&b.push({lng:Number.parseFloat(c[0]),lat:Number.parseFloat(c[1])});return b})(a),...b})}},d3=class extends dE{constructor([a,b,c,d,e],f){let g=["GEOSEARCH",a];("FROMMEMBER"===b.type||"frommember"===b.type)&&g.push(b.type,b.member),("FROMLONLAT"===b.type||"fromlonlat"===b.type)&&g.push(b.type,b.coordinate.lon,b.coordinate.lat),("BYRADIUS"===c.type||"byradius"===c.type)&&g.push(c.type,c.radius,c.radiusType),("BYBOX"===c.type||"bybox"===c.type)&&g.push(c.type,c.rect.width,c.rect.height,c.rectType),g.push(d),e?.count&&g.push("COUNT",e.count.limit,...e.count.any?["ANY"]:[]),super([...g,...e?.withCoord?["WITHCOORD"]:[],...e?.withDist?["WITHDIST"]:[],...e?.withHash?["WITHHASH"]:[]],{deserialize:a=>e?.withCoord||e?.withDist||e?.withHash?a.map(a=>{let b=1,c={};try{c.member=JSON.parse(a[0])}catch{c.member=a[0]}return e.withDist&&(c.dist=Number.parseFloat(a[b++])),e.withHash&&(c.hash=a[b++].toString()),e.withCoord&&(c.coord={long:Number.parseFloat(a[b][0]),lat:Number.parseFloat(a[b][1])}),c}):a.map(a=>{try{return{member:JSON.parse(a)}}catch{return{member:a}}}),...f})}},d4=class extends dE{constructor([a,b,c,d,e,f],g){let h=["GEOSEARCHSTORE",a,b];("FROMMEMBER"===c.type||"frommember"===c.type)&&h.push(c.type,c.member),("FROMLONLAT"===c.type||"fromlonlat"===c.type)&&h.push(c.type,c.coordinate.lon,c.coordinate.lat),("BYRADIUS"===d.type||"byradius"===d.type)&&h.push(d.type,d.radius,d.radiusType),("BYBOX"===d.type||"bybox"===d.type)&&h.push(d.type,d.rect.width,d.rect.height,d.rectType),h.push(e),f?.count&&h.push("COUNT",f.count.limit,...f.count.any?["ANY"]:[]),super([...h,...f?.storeDist?["STOREDIST"]:[]],g)}},d5=class extends dE{constructor(a,b){super(["get",...a],b)}},d6=class extends dE{constructor(a,b){super(["getbit",...a],b)}},d7=class extends dE{constructor(a,b){super(["getdel",...a],b)}},d8=class extends dE{constructor([a,b],c){let d=["getex",a];b&&("ex"in b&&"number"==typeof b.ex?d.push("ex",b.ex):"px"in b&&"number"==typeof b.px?d.push("px",b.px):"exat"in b&&"number"==typeof b.exat?d.push("exat",b.exat):"pxat"in b&&"number"==typeof b.pxat?d.push("pxat",b.pxat):"persist"in b&&b.persist&&d.push("persist")),super(d,c)}},d9=class extends dE{constructor(a,b){super(["getrange",...a],b)}},ea=class extends dE{constructor(a,b){super(["getset",...a],b)}},eb=class extends dE{constructor(a,b){super(["hdel",...a],b)}},ec=class extends dE{constructor(a,b){super(["hexists",...a],b)}},ed=class extends dE{constructor(a,b){let[c,d,e,f]=a,g=Array.isArray(d)?d:[d];super(["hexpire",c,e,...f?[f]:[],"FIELDS",g.length,...g],b)}},ee=class extends dE{constructor(a,b){let[c,d,e,f]=a,g=Array.isArray(d)?d:[d];super(["hexpireat",c,e,...f?[f]:[],"FIELDS",g.length,...g],b)}},ef=class extends dE{constructor(a,b){let[c,d]=a,e=Array.isArray(d)?d:[d];super(["hexpiretime",c,"FIELDS",e.length,...e],b)}},eg=class extends dE{constructor(a,b){let[c,d]=a,e=Array.isArray(d)?d:[d];super(["hpersist",c,"FIELDS",e.length,...e],b)}},eh=class extends dE{constructor(a,b){let[c,d,e,f]=a,g=Array.isArray(d)?d:[d];super(["hpexpire",c,e,...f?[f]:[],"FIELDS",g.length,...g],b)}},ei=class extends dE{constructor(a,b){let[c,d,e,f]=a,g=Array.isArray(d)?d:[d];super(["hpexpireat",c,e,...f?[f]:[],"FIELDS",g.length,...g],b)}},ej=class extends dE{constructor(a,b){let[c,d]=a,e=Array.isArray(d)?d:[d];super(["hpexpiretime",c,"FIELDS",e.length,...e],b)}},ek=class extends dE{constructor(a,b){let[c,d]=a,e=Array.isArray(d)?d:[d];super(["hpttl",c,"FIELDS",e.length,...e],b)}},el=class extends dE{constructor(a,b){super(["hget",...a],b)}},em=class extends dE{constructor(a,b){super(["hgetall",...a],{deserialize:a=>(function(a){if(0===a.length)return null;let b={};for(let c=0;c<a.length;c+=2){let d=a[c],e=a[c+1];try{let a=!Number.isNaN(Number(e))&&!Number.isSafeInteger(Number(e));b[d]=a?e:JSON.parse(e)}catch{b[d]=e}}return b})(a),...b})}},en=class extends dE{constructor(a,b){super(["hincrby",...a],b)}},eo=class extends dE{constructor(a,b){super(["hincrbyfloat",...a],b)}},ep=class extends dE{constructor([a],b){super(["hkeys",a],b)}},eq=class extends dE{constructor(a,b){super(["hlen",...a],b)}},er=class extends dE{constructor([a,...b],c){super(["hmget",a,...b],{deserialize:a=>(function(a,b){if(b.every(a=>null===a))return null;let c={};for(let[d,e]of a.entries())try{c[e]=JSON.parse(b[d])}catch{c[e]=b[d]}return c})(b,a),...c})}},es=class extends dE{constructor([a,b],c){super(["hmset",a,...Object.entries(b).flatMap(([a,b])=>[a,b])],c)}},et=class extends dE{constructor([a,b,c],d){let e=["hscan",a,b];c?.match&&e.push("match",c.match),"number"==typeof c?.count&&e.push("count",c.count),super(e,{deserialize:dx,...d})}},eu=class extends dE{constructor([a,b],c){super(["hset",a,...Object.entries(b).flatMap(([a,b])=>[a,b])],c)}},ev=class extends dE{constructor(a,b){super(["hsetnx",...a],b)}},ew=class extends dE{constructor(a,b){super(["hstrlen",...a],b)}},ex=class extends dE{constructor(a,b){let[c,d]=a,e=Array.isArray(d)?d:[d];super(["httl",c,"FIELDS",e.length,...e],b)}},ey=class extends dE{constructor(a,b){super(["hvals",...a],b)}},ez=class extends dE{constructor(a,b){super(["incr",...a],b)}},eA=class extends dE{constructor(a,b){super(["incrby",...a],b)}},eB=class extends dE{constructor(a,b){super(["incrbyfloat",...a],b)}},eC=class extends dE{constructor(a,b){super(["JSON.ARRAPPEND",...a],b)}},eD=class extends dE{constructor(a,b){super(["JSON.ARRINDEX",...a],b)}},eE=class extends dE{constructor(a,b){super(["JSON.ARRINSERT",...a],b)}},eF=class extends dE{constructor(a,b){super(["JSON.ARRLEN",a[0],a[1]??"$"],b)}},eG=class extends dE{constructor(a,b){super(["JSON.ARRPOP",...a],b)}},eH=class extends dE{constructor(a,b){let c=a[1]??"$";super(["JSON.ARRTRIM",a[0],c,a[2]??0,a[3]??0],b)}},eI=class extends dE{constructor(a,b){super(["JSON.CLEAR",...a],b)}},eJ=class extends dE{constructor(a,b){super(["JSON.DEL",...a],b)}},eK=class extends dE{constructor(a,b){super(["JSON.FORGET",...a],b)}},eL=class extends dE{constructor(a,b){let c=["JSON.GET"];"string"==typeof a[1]?c.push(...a):(c.push(a[0]),a[1]&&(a[1].indent&&c.push("INDENT",a[1].indent),a[1].newline&&c.push("NEWLINE",a[1].newline),a[1].space&&c.push("SPACE",a[1].space)),c.push(...a.slice(2))),super(c,b)}},eM=class extends dE{constructor(a,b){super(["JSON.MERGE",...a],b)}},eN=class extends dE{constructor(a,b){super(["JSON.MGET",...a[0],a[1]],b)}},eO=class extends dE{constructor(a,b){let c=["JSON.MSET"];for(let b of a)c.push(b.key,b.path,b.value);super(c,b)}},eP=class extends dE{constructor(a,b){super(["JSON.NUMINCRBY",...a],b)}},eQ=class extends dE{constructor(a,b){super(["JSON.NUMMULTBY",...a],b)}},eR=class extends dE{constructor(a,b){super(["JSON.OBJKEYS",...a],b)}},eS=class extends dE{constructor(a,b){super(["JSON.OBJLEN",...a],b)}},eT=class extends dE{constructor(a,b){super(["JSON.RESP",...a],b)}},eU=class extends dE{constructor(a,b){let c=["JSON.SET",a[0],a[1],a[2]];a[3]&&(a[3].nx?c.push("NX"):a[3].xx&&c.push("XX")),super(c,b)}},eV=class extends dE{constructor(a,b){super(["JSON.STRAPPEND",...a],b)}},eW=class extends dE{constructor(a,b){super(["JSON.STRLEN",...a],b)}},eX=class extends dE{constructor(a,b){super(["JSON.TOGGLE",...a],b)}},eY=class extends dE{constructor(a,b){super(["JSON.TYPE",...a],b)}},eZ=class extends dE{constructor(a,b){super(["keys",...a],b)}},e$=class extends dE{constructor(a,b){super(["lindex",...a],b)}},e_=class extends dE{constructor(a,b){super(["linsert",...a],b)}},e0=class extends dE{constructor(a,b){super(["llen",...a],b)}},e1=class extends dE{constructor(a,b){super(["lmove",...a],b)}},e2=class extends dE{constructor(a,b){let[c,d,e,f]=a;super(["LMPOP",c,...d,e,...f?["COUNT",f]:[]],b)}},e3=class extends dE{constructor(a,b){super(["lpop",...a],b)}},e4=class extends dE{constructor(a,b){let c=["lpos",a[0],a[1]];"number"==typeof a[2]?.rank&&c.push("rank",a[2].rank),"number"==typeof a[2]?.count&&c.push("count",a[2].count),"number"==typeof a[2]?.maxLen&&c.push("maxLen",a[2].maxLen),super(c,b)}},e5=class extends dE{constructor(a,b){super(["lpush",...a],b)}},e6=class extends dE{constructor(a,b){super(["lpushx",...a],b)}},e7=class extends dE{constructor(a,b){super(["lrange",...a],b)}},e8=class extends dE{constructor(a,b){super(["lrem",...a],b)}},e9=class extends dE{constructor(a,b){super(["lset",...a],b)}},fa=class extends dE{constructor(a,b){super(["ltrim",...a],b)}},fb=class extends dE{constructor(a,b){super(["mget",...Array.isArray(a[0])?a[0]:a],b)}},fc=class extends dE{constructor([a],b){super(["mset",...Object.entries(a).flatMap(([a,b])=>[a,b])],b)}},fd=class extends dE{constructor([a],b){super(["msetnx",...Object.entries(a).flat()],b)}},fe=class extends dE{constructor(a,b){super(["persist",...a],b)}},ff=class extends dE{constructor(a,b){super(["pexpire",...a],b)}},fg=class extends dE{constructor(a,b){super(["pexpireat",...a],b)}},fh=class extends dE{constructor(a,b){super(["pfadd",...a],b)}},fi=class extends dE{constructor(a,b){super(["pfcount",...a],b)}},fj=class extends dE{constructor(a,b){super(["pfmerge",...a],b)}},fk=class extends dE{constructor(a,b){let c=["ping"];a?.[0]!==void 0&&c.push(a[0]),super(c,b)}},fl=class extends dE{constructor(a,b){super(["psetex",...a],b)}},fm=class extends dE{constructor(a,b){super(["pttl",...a],b)}},fn=class extends dE{constructor(a,b){super(["publish",...a],b)}},fo=class extends dE{constructor(a){super(["randomkey"],a)}},fp=class extends dE{constructor(a,b){super(["rename",...a],b)}},fq=class extends dE{constructor(a,b){super(["renamenx",...a],b)}},fr=class extends dE{constructor(a,b){super(["rpop",...a],b)}},fs=class extends dE{constructor(a,b){super(["rpush",...a],b)}},ft=class extends dE{constructor(a,b){super(["rpushx",...a],b)}},fu=class extends dE{constructor(a,b){super(["sadd",...a],b)}},fv=class extends dE{constructor([a,b],c){let d=["scan",a];b?.match&&d.push("match",b.match),"number"==typeof b?.count&&d.push("count",b.count),b&&"withType"in b&&!0===b.withType?d.push("withtype"):b&&"type"in b&&b.type&&b.type.length>0&&d.push("type",b.type),super(d,{deserialize:b?.withType?dy:dx,...c})}},fw=class extends dE{constructor(a,b){super(["scard",...a],b)}},fx=class extends dE{constructor(a,b){super(["script","exists",...a],{deserialize:a=>a,...b})}},fy=class extends dE{constructor([a],b){let c=["script","flush"];a?.sync?c.push("sync"):a?.async&&c.push("async"),super(c,b)}},fz=class extends dE{constructor(a,b){super(["script","load",...a],b)}},fA=class extends dE{constructor(a,b){super(["sdiff",...a],b)}},fB=class extends dE{constructor(a,b){super(["sdiffstore",...a],b)}},fC=class extends dE{constructor([a,b,c],d){let e=["set",a,b];c&&("nx"in c&&c.nx?e.push("nx"):"xx"in c&&c.xx&&e.push("xx"),"get"in c&&c.get&&e.push("get"),"ex"in c&&"number"==typeof c.ex?e.push("ex",c.ex):"px"in c&&"number"==typeof c.px?e.push("px",c.px):"exat"in c&&"number"==typeof c.exat?e.push("exat",c.exat):"pxat"in c&&"number"==typeof c.pxat?e.push("pxat",c.pxat):"keepTtl"in c&&c.keepTtl&&e.push("keepTtl")),super(e,d)}},fD=class extends dE{constructor(a,b){super(["setbit",...a],b)}},fE=class extends dE{constructor(a,b){super(["setex",...a],b)}},fF=class extends dE{constructor(a,b){super(["setnx",...a],b)}},fG=class extends dE{constructor(a,b){super(["setrange",...a],b)}},fH=class extends dE{constructor(a,b){super(["sinter",...a],b)}},fI=class extends dE{constructor(a,b){super(["sinterstore",...a],b)}},fJ=class extends dE{constructor(a,b){super(["sismember",...a],b)}},fK=class extends dE{constructor(a,b){super(["smembers",...a],b)}},fL=class extends dE{constructor(a,b){super(["smismember",a[0],...a[1]],b)}},fM=class extends dE{constructor(a,b){super(["smove",...a],b)}},fN=class extends dE{constructor([a,b],c){let d=["spop",a];"number"==typeof b&&d.push(b),super(d,c)}},fO=class extends dE{constructor([a,b],c){let d=["srandmember",a];"number"==typeof b&&d.push(b),super(d,c)}},fP=class extends dE{constructor(a,b){super(["srem",...a],b)}},fQ=class extends dE{constructor([a,b,c],d){let e=["sscan",a,b];c?.match&&e.push("match",c.match),"number"==typeof c?.count&&e.push("count",c.count),super(e,{deserialize:dx,...d})}},fR=class extends dE{constructor(a,b){super(["strlen",...a],b)}},fS=class extends dE{constructor(a,b){super(["sunion",...a],b)}},fT=class extends dE{constructor(a,b){super(["sunionstore",...a],b)}},fU=class extends dE{constructor(a){super(["time"],a)}},fV=class extends dE{constructor(a,b){super(["touch",...a],b)}},fW=class extends dE{constructor(a,b){super(["ttl",...a],b)}},fX=class extends dE{constructor(a,b){super(["type",...a],b)}},fY=class extends dE{constructor(a,b){super(["unlink",...a],b)}},fZ=class extends dE{constructor([a,b,c],d){super(["XACK",a,b,...Array.isArray(c)?[...c]:[c]],d)}},f$=class extends dE{constructor([a,b,c,d],e){let f=["XADD",a];for(let[a,e]of(d&&(d.nomkStream&&f.push("NOMKSTREAM"),d.trim&&(f.push(d.trim.type,d.trim.comparison,d.trim.threshold),void 0!==d.trim.limit&&f.push("LIMIT",d.trim.limit))),f.push(b),Object.entries(c)))f.push(a,e);super(f,e)}},f_=class extends dE{constructor([a,b,c,d,e,f],g){let h=[];f?.count&&h.push("COUNT",f.count),f?.justId&&h.push("JUSTID"),super(["XAUTOCLAIM",a,b,c,d,e,...h],g)}},f0=class extends dE{constructor([a,b,c,d,e,f],g){let h=Array.isArray(e)?[...e]:[e],i=[];f?.idleMS&&i.push("IDLE",f.idleMS),f?.idleMS&&i.push("TIME",f.timeMS),f?.retryCount&&i.push("RETRYCOUNT",f.retryCount),f?.force&&i.push("FORCE"),f?.justId&&i.push("JUSTID"),f?.lastId&&i.push("LASTID",f.lastId),super(["XCLAIM",a,b,c,d,...h,...i],g)}},f1=class extends dE{constructor([a,b],c){super(["XDEL",a,...Array.isArray(b)?[...b]:[b]],c)}},f2=class extends dE{constructor([a,b],c){let d=["XGROUP"];switch(b.type){case"CREATE":d.push("CREATE",a,b.group,b.id),b.options&&(b.options.MKSTREAM&&d.push("MKSTREAM"),void 0!==b.options.ENTRIESREAD&&d.push("ENTRIESREAD",b.options.ENTRIESREAD.toString()));break;case"CREATECONSUMER":d.push("CREATECONSUMER",a,b.group,b.consumer);break;case"DELCONSUMER":d.push("DELCONSUMER",a,b.group,b.consumer);break;case"DESTROY":d.push("DESTROY",a,b.group);break;case"SETID":d.push("SETID",a,b.group,b.id),b.options?.ENTRIESREAD!==void 0&&d.push("ENTRIESREAD",b.options.ENTRIESREAD.toString());break;default:throw Error("Invalid XGROUP")}super(d,c)}},f3=class extends dE{constructor([a,b],c){let d=[];"CONSUMERS"===b.type?d.push("CONSUMERS",a,b.group):d.push("GROUPS",a),super(["XINFO",...d],c)}},f4=class extends dE{constructor(a,b){super(["XLEN",...a],b)}},f5=class extends dE{constructor([a,b,c,d,e,f],g){super(["XPENDING",a,b,...f?.idleTime?["IDLE",f.idleTime]:[],c,d,e,...f?.consumer===void 0?[]:Array.isArray(f.consumer)?[...f.consumer]:[f.consumer]],g)}},f6=class extends dE{constructor([a,b,c,d],e){let f=["XRANGE",a,b,c];"number"==typeof d&&f.push("COUNT",d),super(f,{deserialize:a=>(function(a){let b={};for(let c of a)for(let a=0;a<c.length;a+=2){let d=c[a],e=c[a+1];d in b||(b[d]={});for(let a=0;a<e.length;a+=2){let c=e[a],f=e[a+1];try{b[d][c]=JSON.parse(f)}catch{b[d][c]=f}}}return b})(a),...e})}},f7=class extends dE{constructor([a,b,c],d){if(Array.isArray(a)&&Array.isArray(b)&&a.length!==b.length)throw Error("ERR Unbalanced XREAD list of streams: for each stream key an ID or '$' must be specified");let e=[];"number"==typeof c?.count&&e.push("COUNT",c.count),"number"==typeof c?.blockMS&&e.push("BLOCK",c.blockMS),e.push("STREAMS",...Array.isArray(a)?[...a]:[a],...Array.isArray(b)?[...b]:[b]),super(["XREAD",...e],d)}},f8=class extends dE{constructor([a,b,c,d,e],f){if(Array.isArray(c)&&Array.isArray(d)&&c.length!==d.length)throw Error("ERR Unbalanced XREADGROUP list of streams: for each stream key an ID or '$' must be specified");let g=[];"number"==typeof e?.count&&g.push("COUNT",e.count),"number"==typeof e?.blockMS&&g.push("BLOCK",e.blockMS),"boolean"==typeof e?.NOACK&&e.NOACK&&g.push("NOACK"),g.push("STREAMS",...Array.isArray(c)?[...c]:[c],...Array.isArray(d)?[...d]:[d]),super(["XREADGROUP","GROUP",a,b,...g],f)}},f9=class extends dE{constructor([a,b,c,d],e){let f=["XREVRANGE",a,b,c];"number"==typeof d&&f.push("COUNT",d),super(f,{deserialize:a=>(function(a){let b={};for(let c of a)for(let a=0;a<c.length;a+=2){let d=c[a],e=c[a+1];d in b||(b[d]={});for(let a=0;a<e.length;a+=2){let c=e[a],f=e[a+1];try{b[d][c]=JSON.parse(f)}catch{b[d][c]=f}}}return b})(a),...e})}},ga=class extends dE{constructor([a,b],c){let{limit:d,strategy:e,threshold:f,exactness:g="~"}=b;super(["XTRIM",a,e,g,f,...d?["LIMIT",d]:[]],c)}},gb=class extends dE{constructor([a,b,...c],d){let e=["zadd",a];"nx"in b&&b.nx?e.push("nx"):"xx"in b&&b.xx&&e.push("xx"),"ch"in b&&b.ch&&e.push("ch"),"incr"in b&&b.incr&&e.push("incr"),"lt"in b&&b.lt?e.push("lt"):"gt"in b&&b.gt&&e.push("gt"),"score"in b&&"member"in b&&e.push(b.score,b.member),e.push(...c.flatMap(({score:a,member:b})=>[a,b])),super(e,d)}},gc=class extends dE{constructor(a,b){super(["zcard",...a],b)}},gd=class extends dE{constructor(a,b){super(["zcount",...a],b)}},ge=class extends dE{constructor(a,b){super(["zincrby",...a],b)}},gf=class extends dE{constructor([a,b,c,d],e){let f=["zinterstore",a,b];Array.isArray(c)?f.push(...c):f.push(c),d&&("weights"in d&&d.weights?f.push("weights",...d.weights):"weight"in d&&"number"==typeof d.weight&&f.push("weights",d.weight),"aggregate"in d&&f.push("aggregate",d.aggregate)),super(f,e)}},gg=class extends dE{constructor(a,b){super(["zlexcount",...a],b)}},gh=class extends dE{constructor([a,b],c){let d=["zpopmax",a];"number"==typeof b&&d.push(b),super(d,c)}},gi=class extends dE{constructor([a,b],c){let d=["zpopmin",a];"number"==typeof b&&d.push(b),super(d,c)}},gj=class extends dE{constructor([a,b,c,d],e){let f=["zrange",a,b,c];d?.byScore&&f.push("byscore"),d?.byLex&&f.push("bylex"),d?.rev&&f.push("rev"),d?.count!==void 0&&void 0!==d.offset&&f.push("limit",d.offset,d.count),d?.withScores&&f.push("withscores"),super(f,e)}},gk=class extends dE{constructor(a,b){super(["zrank",...a],b)}},gl=class extends dE{constructor(a,b){super(["zrem",...a],b)}},gm=class extends dE{constructor(a,b){super(["zremrangebylex",...a],b)}},gn=class extends dE{constructor(a,b){super(["zremrangebyrank",...a],b)}},go=class extends dE{constructor(a,b){super(["zremrangebyscore",...a],b)}},gp=class extends dE{constructor(a,b){super(["zrevrank",...a],b)}},gq=class extends dE{constructor([a,b,c],d){let e=["zscan",a,b];c?.match&&e.push("match",c.match),"number"==typeof c?.count&&e.push("count",c.count),super(e,{deserialize:dx,...d})}},gr=class extends dE{constructor(a,b){super(["zscore",...a],b)}},gs=class extends dE{constructor([a,b,c],d){let e=["zunion",a];Array.isArray(b)?e.push(...b):e.push(b),c&&("weights"in c&&c.weights?e.push("weights",...c.weights):"weight"in c&&"number"==typeof c.weight&&e.push("weights",c.weight),"aggregate"in c&&e.push("aggregate",c.aggregate),c.withScores&&e.push("withscores")),super(e,d)}},gt=class extends dE{constructor([a,b,c,d],e){let f=["zunionstore",a,b];Array.isArray(c)?f.push(...c):f.push(c),d&&("weights"in d&&d.weights?f.push("weights",...d.weights):"weight"in d&&"number"==typeof d.weight&&f.push("weights",d.weight),"aggregate"in d&&f.push("aggregate",d.aggregate)),super(f,e)}},gu=class extends dE{constructor(a,b){super(["zdiffstore",...a],b)}},gv=class extends dE{constructor(a,b){let[c,d]=a;super(["zmscore",c,...d],b)}},gw=class{client;commands;commandOptions;multiExec;constructor(a){if(this.client=a.client,this.commands=[],this.commandOptions=a.commandOptions,this.multiExec=a.multiExec??!1,this.commandOptions?.latencyLogging){let a=this.exec.bind(this);this.exec=async b=>{let c=performance.now(),d=await (b?a(b):a()),e=(performance.now()-c).toFixed(2);return console.log(`Latency for \x1b[38;2;19;185;39m${this.multiExec?["MULTI-EXEC"]:["PIPELINE"].toString().toUpperCase()}\x1b[0m: \x1b[38;2;0;255;255m${e} ms\x1b[0m`),d}}}exec=async a=>{if(0===this.commands.length)throw Error("Pipeline is empty");let b=this.multiExec?["multi-exec"]:["pipeline"],c=await this.client.request({path:b,body:Object.values(this.commands).map(a=>a.command)});return a?.keepErrors?c.map(({error:a,result:b},c)=>({error:a,result:this.commands[c].deserialize(b)})):c.map(({error:a,result:b},c)=>{if(a)throw new du(`Command ${c+1} [ ${this.commands[c].command[0]} ] failed: ${a}`);return this.commands[c].deserialize(b)})};length(){return this.commands.length}chain(a){return this.commands.push(a),this}append=(...a)=>this.chain(new dG(a,this.commandOptions));bitcount=(...a)=>this.chain(new dH(a,this.commandOptions));bitfield=(...a)=>new dI(a,this.client,this.commandOptions,this.chain.bind(this));bitop=(a,b,c,...d)=>this.chain(new dJ([a,b,c,...d],this.commandOptions));bitpos=(...a)=>this.chain(new dK(a,this.commandOptions));copy=(...a)=>this.chain(new dL(a,this.commandOptions));zdiffstore=(...a)=>this.chain(new gu(a,this.commandOptions));dbsize=()=>this.chain(new dM(this.commandOptions));decr=(...a)=>this.chain(new dN(a,this.commandOptions));decrby=(...a)=>this.chain(new dO(a,this.commandOptions));del=(...a)=>this.chain(new dP(a,this.commandOptions));echo=(...a)=>this.chain(new dQ(a,this.commandOptions));evalRo=(...a)=>this.chain(new dR(a,this.commandOptions));eval=(...a)=>this.chain(new dS(a,this.commandOptions));evalshaRo=(...a)=>this.chain(new dT(a,this.commandOptions));evalsha=(...a)=>this.chain(new dU(a,this.commandOptions));exists=(...a)=>this.chain(new dW(a,this.commandOptions));expire=(...a)=>this.chain(new dX(a,this.commandOptions));expireat=(...a)=>this.chain(new dY(a,this.commandOptions));flushall=a=>this.chain(new dZ(a,this.commandOptions));flushdb=(...a)=>this.chain(new d$(a,this.commandOptions));geoadd=(...a)=>this.chain(new d_(a,this.commandOptions));geodist=(...a)=>this.chain(new d0(a,this.commandOptions));geopos=(...a)=>this.chain(new d2(a,this.commandOptions));geohash=(...a)=>this.chain(new d1(a,this.commandOptions));geosearch=(...a)=>this.chain(new d3(a,this.commandOptions));geosearchstore=(...a)=>this.chain(new d4(a,this.commandOptions));get=(...a)=>this.chain(new d5(a,this.commandOptions));getbit=(...a)=>this.chain(new d6(a,this.commandOptions));getdel=(...a)=>this.chain(new d7(a,this.commandOptions));getex=(...a)=>this.chain(new d8(a,this.commandOptions));getrange=(...a)=>this.chain(new d9(a,this.commandOptions));getset=(a,b)=>this.chain(new ea([a,b],this.commandOptions));hdel=(...a)=>this.chain(new eb(a,this.commandOptions));hexists=(...a)=>this.chain(new ec(a,this.commandOptions));hexpire=(...a)=>this.chain(new ed(a,this.commandOptions));hexpireat=(...a)=>this.chain(new ee(a,this.commandOptions));hexpiretime=(...a)=>this.chain(new ef(a,this.commandOptions));httl=(...a)=>this.chain(new ex(a,this.commandOptions));hpexpire=(...a)=>this.chain(new eh(a,this.commandOptions));hpexpireat=(...a)=>this.chain(new ei(a,this.commandOptions));hpexpiretime=(...a)=>this.chain(new ej(a,this.commandOptions));hpttl=(...a)=>this.chain(new ek(a,this.commandOptions));hpersist=(...a)=>this.chain(new eg(a,this.commandOptions));hget=(...a)=>this.chain(new el(a,this.commandOptions));hgetall=(...a)=>this.chain(new em(a,this.commandOptions));hincrby=(...a)=>this.chain(new en(a,this.commandOptions));hincrbyfloat=(...a)=>this.chain(new eo(a,this.commandOptions));hkeys=(...a)=>this.chain(new ep(a,this.commandOptions));hlen=(...a)=>this.chain(new eq(a,this.commandOptions));hmget=(...a)=>this.chain(new er(a,this.commandOptions));hmset=(a,b)=>this.chain(new es([a,b],this.commandOptions));hrandfield=(a,b,c)=>this.chain(new dF([a,b,c],this.commandOptions));hscan=(...a)=>this.chain(new et(a,this.commandOptions));hset=(a,b)=>this.chain(new eu([a,b],this.commandOptions));hsetnx=(a,b,c)=>this.chain(new ev([a,b,c],this.commandOptions));hstrlen=(...a)=>this.chain(new ew(a,this.commandOptions));hvals=(...a)=>this.chain(new ey(a,this.commandOptions));incr=(...a)=>this.chain(new ez(a,this.commandOptions));incrby=(...a)=>this.chain(new eA(a,this.commandOptions));incrbyfloat=(...a)=>this.chain(new eB(a,this.commandOptions));keys=(...a)=>this.chain(new eZ(a,this.commandOptions));lindex=(...a)=>this.chain(new e$(a,this.commandOptions));linsert=(a,b,c,d)=>this.chain(new e_([a,b,c,d],this.commandOptions));llen=(...a)=>this.chain(new e0(a,this.commandOptions));lmove=(...a)=>this.chain(new e1(a,this.commandOptions));lpop=(...a)=>this.chain(new e3(a,this.commandOptions));lmpop=(...a)=>this.chain(new e2(a,this.commandOptions));lpos=(...a)=>this.chain(new e4(a,this.commandOptions));lpush=(a,...b)=>this.chain(new e5([a,...b],this.commandOptions));lpushx=(a,...b)=>this.chain(new e6([a,...b],this.commandOptions));lrange=(...a)=>this.chain(new e7(a,this.commandOptions));lrem=(a,b,c)=>this.chain(new e8([a,b,c],this.commandOptions));lset=(a,b,c)=>this.chain(new e9([a,b,c],this.commandOptions));ltrim=(...a)=>this.chain(new fa(a,this.commandOptions));mget=(...a)=>this.chain(new fb(a,this.commandOptions));mset=a=>this.chain(new fc([a],this.commandOptions));msetnx=a=>this.chain(new fd([a],this.commandOptions));persist=(...a)=>this.chain(new fe(a,this.commandOptions));pexpire=(...a)=>this.chain(new ff(a,this.commandOptions));pexpireat=(...a)=>this.chain(new fg(a,this.commandOptions));pfadd=(...a)=>this.chain(new fh(a,this.commandOptions));pfcount=(...a)=>this.chain(new fi(a,this.commandOptions));pfmerge=(...a)=>this.chain(new fj(a,this.commandOptions));ping=a=>this.chain(new fk(a,this.commandOptions));psetex=(a,b,c)=>this.chain(new fl([a,b,c],this.commandOptions));pttl=(...a)=>this.chain(new fm(a,this.commandOptions));publish=(...a)=>this.chain(new fn(a,this.commandOptions));randomkey=()=>this.chain(new fo(this.commandOptions));rename=(...a)=>this.chain(new fp(a,this.commandOptions));renamenx=(...a)=>this.chain(new fq(a,this.commandOptions));rpop=(...a)=>this.chain(new fr(a,this.commandOptions));rpush=(a,...b)=>this.chain(new fs([a,...b],this.commandOptions));rpushx=(a,...b)=>this.chain(new ft([a,...b],this.commandOptions));sadd=(a,b,...c)=>this.chain(new fu([a,b,...c],this.commandOptions));scan=(...a)=>this.chain(new fv(a,this.commandOptions));scard=(...a)=>this.chain(new fw(a,this.commandOptions));scriptExists=(...a)=>this.chain(new fx(a,this.commandOptions));scriptFlush=(...a)=>this.chain(new fy(a,this.commandOptions));scriptLoad=(...a)=>this.chain(new fz(a,this.commandOptions));sdiff=(...a)=>this.chain(new fA(a,this.commandOptions));sdiffstore=(...a)=>this.chain(new fB(a,this.commandOptions));set=(a,b,c)=>this.chain(new fC([a,b,c],this.commandOptions));setbit=(...a)=>this.chain(new fD(a,this.commandOptions));setex=(a,b,c)=>this.chain(new fE([a,b,c],this.commandOptions));setnx=(a,b)=>this.chain(new fF([a,b],this.commandOptions));setrange=(...a)=>this.chain(new fG(a,this.commandOptions));sinter=(...a)=>this.chain(new fH(a,this.commandOptions));sinterstore=(...a)=>this.chain(new fI(a,this.commandOptions));sismember=(a,b)=>this.chain(new fJ([a,b],this.commandOptions));smembers=(...a)=>this.chain(new fK(a,this.commandOptions));smismember=(a,b)=>this.chain(new fL([a,b],this.commandOptions));smove=(a,b,c)=>this.chain(new fM([a,b,c],this.commandOptions));spop=(...a)=>this.chain(new fN(a,this.commandOptions));srandmember=(...a)=>this.chain(new fO(a,this.commandOptions));srem=(a,...b)=>this.chain(new fP([a,...b],this.commandOptions));sscan=(...a)=>this.chain(new fQ(a,this.commandOptions));strlen=(...a)=>this.chain(new fR(a,this.commandOptions));sunion=(...a)=>this.chain(new fS(a,this.commandOptions));sunionstore=(...a)=>this.chain(new fT(a,this.commandOptions));time=()=>this.chain(new fU(this.commandOptions));touch=(...a)=>this.chain(new fV(a,this.commandOptions));ttl=(...a)=>this.chain(new fW(a,this.commandOptions));type=(...a)=>this.chain(new fX(a,this.commandOptions));unlink=(...a)=>this.chain(new fY(a,this.commandOptions));zadd=(...a)=>("score"in a[1],this.chain(new gb([a[0],a[1],...a.slice(2)],this.commandOptions)));xadd=(...a)=>this.chain(new f$(a,this.commandOptions));xack=(...a)=>this.chain(new fZ(a,this.commandOptions));xdel=(...a)=>this.chain(new f1(a,this.commandOptions));xgroup=(...a)=>this.chain(new f2(a,this.commandOptions));xread=(...a)=>this.chain(new f7(a,this.commandOptions));xreadgroup=(...a)=>this.chain(new f8(a,this.commandOptions));xinfo=(...a)=>this.chain(new f3(a,this.commandOptions));xlen=(...a)=>this.chain(new f4(a,this.commandOptions));xpending=(...a)=>this.chain(new f5(a,this.commandOptions));xclaim=(...a)=>this.chain(new f0(a,this.commandOptions));xautoclaim=(...a)=>this.chain(new f_(a,this.commandOptions));xtrim=(...a)=>this.chain(new ga(a,this.commandOptions));xrange=(...a)=>this.chain(new f6(a,this.commandOptions));xrevrange=(...a)=>this.chain(new f9(a,this.commandOptions));zcard=(...a)=>this.chain(new gc(a,this.commandOptions));zcount=(...a)=>this.chain(new gd(a,this.commandOptions));zincrby=(a,b,c)=>this.chain(new ge([a,b,c],this.commandOptions));zinterstore=(...a)=>this.chain(new gf(a,this.commandOptions));zlexcount=(...a)=>this.chain(new gg(a,this.commandOptions));zmscore=(...a)=>this.chain(new gv(a,this.commandOptions));zpopmax=(...a)=>this.chain(new gh(a,this.commandOptions));zpopmin=(...a)=>this.chain(new gi(a,this.commandOptions));zrange=(...a)=>this.chain(new gj(a,this.commandOptions));zrank=(a,b)=>this.chain(new gk([a,b],this.commandOptions));zrem=(a,...b)=>this.chain(new gl([a,...b],this.commandOptions));zremrangebylex=(...a)=>this.chain(new gm(a,this.commandOptions));zremrangebyrank=(...a)=>this.chain(new gn(a,this.commandOptions));zremrangebyscore=(...a)=>this.chain(new go(a,this.commandOptions));zrevrank=(a,b)=>this.chain(new gp([a,b],this.commandOptions));zscan=(...a)=>this.chain(new gq(a,this.commandOptions));zscore=(a,b)=>this.chain(new gr([a,b],this.commandOptions));zunionstore=(...a)=>this.chain(new gt(a,this.commandOptions));zunion=(...a)=>this.chain(new gs(a,this.commandOptions));get json(){return{arrappend:(...a)=>this.chain(new eC(a,this.commandOptions)),arrindex:(...a)=>this.chain(new eD(a,this.commandOptions)),arrinsert:(...a)=>this.chain(new eE(a,this.commandOptions)),arrlen:(...a)=>this.chain(new eF(a,this.commandOptions)),arrpop:(...a)=>this.chain(new eG(a,this.commandOptions)),arrtrim:(...a)=>this.chain(new eH(a,this.commandOptions)),clear:(...a)=>this.chain(new eI(a,this.commandOptions)),del:(...a)=>this.chain(new eJ(a,this.commandOptions)),forget:(...a)=>this.chain(new eK(a,this.commandOptions)),get:(...a)=>this.chain(new eL(a,this.commandOptions)),merge:(...a)=>this.chain(new eM(a,this.commandOptions)),mget:(...a)=>this.chain(new eN(a,this.commandOptions)),mset:(...a)=>this.chain(new eO(a,this.commandOptions)),numincrby:(...a)=>this.chain(new eP(a,this.commandOptions)),nummultby:(...a)=>this.chain(new eQ(a,this.commandOptions)),objkeys:(...a)=>this.chain(new eR(a,this.commandOptions)),objlen:(...a)=>this.chain(new eS(a,this.commandOptions)),resp:(...a)=>this.chain(new eT(a,this.commandOptions)),set:(...a)=>this.chain(new eU(a,this.commandOptions)),strappend:(...a)=>this.chain(new eV(a,this.commandOptions)),strlen:(...a)=>this.chain(new eW(a,this.commandOptions)),toggle:(...a)=>this.chain(new eX(a,this.commandOptions)),type:(...a)=>this.chain(new eY(a,this.commandOptions))}}},gx=new Set(["scan","keys","flushdb","flushall","dbsize","hscan","hgetall","hkeys","lrange","sscan","smembers","xrange","xrevrange","zscan","zrange"]),gy=class{pipelinePromises=new WeakMap;activePipeline=null;indexInCurrentPipeline=0;redis;pipeline;pipelineCounter=0;constructor(a){this.redis=a,this.pipeline=a.pipeline()}async withAutoPipeline(a){let b=this.activePipeline??this.redis.pipeline();this.activePipeline||(this.activePipeline=b,this.indexInCurrentPipeline=0);let c=this.indexInCurrentPipeline++;a(b);let d=this.deferExecution().then(()=>{if(!this.pipelinePromises.has(b)){let a=b.exec({keepErrors:!0});this.pipelineCounter+=1,this.pipelinePromises.set(b,a),this.activePipeline=null}return this.pipelinePromises.get(b)}),e=(await d)[c];if(e.error)throw new du(`Command failed: ${e.error}`);return e.result}async deferExecution(){await Promise.resolve(),await Promise.resolve()}},gz=class extends dE{constructor(a,b){super([],{...b,headers:{Accept:"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"},path:["psubscribe",...a],streamOptions:{isStreaming:!0,onMessage:b?.streamOptions?.onMessage,signal:b?.streamOptions?.signal}})}},gA=class extends EventTarget{subscriptions;client;listeners;constructor(a,b,c=!1){for(let d of(super(),this.client=a,this.subscriptions=new Map,this.listeners=new Map,b))c?this.subscribeToPattern(d):this.subscribeToChannel(d)}subscribeToChannel(a){let b=new AbortController,c=new gB([a],{streamOptions:{signal:b.signal,onMessage:a=>this.handleMessage(a,!1)}});c.exec(this.client).catch(a=>{"AbortError"!==a.name&&this.dispatchToListeners("error",a)}),this.subscriptions.set(a,{command:c,controller:b,isPattern:!1})}subscribeToPattern(a){let b=new AbortController,c=new gz([a],{streamOptions:{signal:b.signal,onMessage:a=>this.handleMessage(a,!0)}});c.exec(this.client).catch(a=>{"AbortError"!==a.name&&this.dispatchToListeners("error",a)}),this.subscriptions.set(a,{command:c,controller:b,isPattern:!0})}handleMessage(a,b){let c=a.replace(/^data:\s*/,""),d=c.indexOf(","),e=c.indexOf(",",d+1),f=b?c.indexOf(",",e+1):-1;if(-1!==d&&-1!==e){let a=c.slice(0,d);if(b&&"pmessage"===a&&-1!==f){let a=c.slice(d+1,e),b=c.slice(e+1,f),g=c.slice(f+1);try{let c=JSON.parse(g);this.dispatchToListeners("pmessage",{pattern:a,channel:b,message:c}),this.dispatchToListeners(`pmessage:${a}`,{pattern:a,channel:b,message:c})}catch(a){this.dispatchToListeners("error",Error(`Failed to parse message: ${a}`))}}else{let b=c.slice(d+1,e),f=c.slice(e+1);try{if("subscribe"===a||"psubscribe"===a||"unsubscribe"===a||"punsubscribe"===a){let b=Number.parseInt(f);this.dispatchToListeners(a,b)}else{let c=JSON.parse(f);this.dispatchToListeners(a,{channel:b,message:c}),this.dispatchToListeners(`${a}:${b}`,{channel:b,message:c})}}catch(a){this.dispatchToListeners("error",Error(`Failed to parse message: ${a}`))}}}}dispatchToListeners(a,b){let c=this.listeners.get(a);if(c)for(let a of c)a(b)}on(a,b){this.listeners.has(a)||this.listeners.set(a,new Set),this.listeners.get(a)?.add(b)}removeAllListeners(){this.listeners.clear()}async unsubscribe(a){if(a)for(let b of a){let a=this.subscriptions.get(b);if(a){try{a.controller.abort()}catch{}this.subscriptions.delete(b)}}else{for(let a of this.subscriptions.values())try{a.controller.abort()}catch{}this.subscriptions.clear(),this.removeAllListeners()}}getSubscribedChannels(){return[...this.subscriptions.keys()]}},gB=class extends dE{constructor(a,b){super([],{...b,headers:{Accept:"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"},path:["subscribe",...a],streamOptions:{isStreaming:!0,onMessage:b?.streamOptions?.onMessage,signal:b?.streamOptions?.signal}})}},gC=class{script;sha1;redis;constructor(a,b){this.redis=a,this.script=b,this.sha1="",this.init(b)}async init(a){this.sha1||(this.sha1=await this.digest(a))}async eval(a,b){return await this.init(this.script),await this.redis.eval(this.script,a,b)}async evalsha(a,b){return await this.init(this.script),await this.redis.evalsha(this.sha1,a,b)}async exec(a,b){return await this.init(this.script),await this.redis.evalsha(this.sha1,a,b).catch(async c=>{if(c instanceof Error&&c.message.toLowerCase().includes("noscript"))return await this.redis.eval(this.script,a,b);throw c})}async digest(a){let b=new TextEncoder().encode(a);return[...new Uint8Array(await ds.digest("SHA-1",b))].map(a=>a.toString(16).padStart(2,"0")).join("")}},gD=class{script;sha1;redis;constructor(a,b){this.redis=a,this.sha1="",this.script=b,this.init(b)}async init(a){this.sha1||(this.sha1=await this.digest(a))}async evalRo(a,b){return await this.init(this.script),await this.redis.evalRo(this.script,a,b)}async evalshaRo(a,b){return await this.init(this.script),await this.redis.evalshaRo(this.sha1,a,b)}async exec(a,b){return await this.init(this.script),await this.redis.evalshaRo(this.sha1,a,b).catch(async c=>{if(c instanceof Error&&c.message.toLowerCase().includes("noscript"))return await this.redis.evalRo(this.script,a,b);throw c})}async digest(a){let b=new TextEncoder().encode(a);return[...new Uint8Array(await ds.digest("SHA-1",b))].map(a=>a.toString(16).padStart(2,"0")).join("")}},gE=class{client;opts;enableTelemetry;enableAutoPipelining;constructor(a,b){this.client=a,this.opts=b,this.enableTelemetry=b?.enableTelemetry??!0,b?.readYourWrites===!1&&(this.client.readYourWrites=!1),this.enableAutoPipelining=b?.enableAutoPipelining??!0}get readYourWritesSyncToken(){return this.client.upstashSyncToken}set readYourWritesSyncToken(a){this.client.upstashSyncToken=a}get json(){return{arrappend:(...a)=>new eC(a,this.opts).exec(this.client),arrindex:(...a)=>new eD(a,this.opts).exec(this.client),arrinsert:(...a)=>new eE(a,this.opts).exec(this.client),arrlen:(...a)=>new eF(a,this.opts).exec(this.client),arrpop:(...a)=>new eG(a,this.opts).exec(this.client),arrtrim:(...a)=>new eH(a,this.opts).exec(this.client),clear:(...a)=>new eI(a,this.opts).exec(this.client),del:(...a)=>new eJ(a,this.opts).exec(this.client),forget:(...a)=>new eK(a,this.opts).exec(this.client),get:(...a)=>new eL(a,this.opts).exec(this.client),merge:(...a)=>new eM(a,this.opts).exec(this.client),mget:(...a)=>new eN(a,this.opts).exec(this.client),mset:(...a)=>new eO(a,this.opts).exec(this.client),numincrby:(...a)=>new eP(a,this.opts).exec(this.client),nummultby:(...a)=>new eQ(a,this.opts).exec(this.client),objkeys:(...a)=>new eR(a,this.opts).exec(this.client),objlen:(...a)=>new eS(a,this.opts).exec(this.client),resp:(...a)=>new eT(a,this.opts).exec(this.client),set:(...a)=>new eU(a,this.opts).exec(this.client),strappend:(...a)=>new eV(a,this.opts).exec(this.client),strlen:(...a)=>new eW(a,this.opts).exec(this.client),toggle:(...a)=>new eX(a,this.opts).exec(this.client),type:(...a)=>new eY(a,this.opts).exec(this.client)}}use=a=>{let b=this.client.request.bind(this.client);this.client.request=c=>a(c,b)};addTelemetry=a=>{if(this.enableTelemetry)try{this.client.mergeTelemetry(a)}catch{}};createScript(a,b){return b?.readonly?new gD(this,a):new gC(this,a)}pipeline=()=>new gw({client:this.client,commandOptions:this.opts,multiExec:!1});autoPipeline=()=>(function a(b,c){return b.autoPipelineExecutor||(b.autoPipelineExecutor=new gy(b)),new Proxy(b,{get:(b,d)=>{if("pipelineCounter"===d)return b.autoPipelineExecutor.pipelineCounter;if("json"===d)return a(b,!0);let e=d in b&&!(d in b.autoPipelineExecutor.pipeline),f=gx.has(d);return e||f?b[d]:(c?"function"==typeof b.autoPipelineExecutor.pipeline.json[d]:"function"==typeof b.autoPipelineExecutor.pipeline[d])?(...a)=>b.autoPipelineExecutor.withAutoPipeline(b=>{c?b.json[d](...a):b[d](...a)}):b.autoPipelineExecutor.pipeline[d]}})})(this);multi=()=>new gw({client:this.client,commandOptions:this.opts,multiExec:!0});bitfield=(...a)=>new dI(a,this.client,this.opts);append=(...a)=>new dG(a,this.opts).exec(this.client);bitcount=(...a)=>new dH(a,this.opts).exec(this.client);bitop=(a,b,c,...d)=>new dJ([a,b,c,...d],this.opts).exec(this.client);bitpos=(...a)=>new dK(a,this.opts).exec(this.client);copy=(...a)=>new dL(a,this.opts).exec(this.client);dbsize=()=>new dM(this.opts).exec(this.client);decr=(...a)=>new dN(a,this.opts).exec(this.client);decrby=(...a)=>new dO(a,this.opts).exec(this.client);del=(...a)=>new dP(a,this.opts).exec(this.client);echo=(...a)=>new dQ(a,this.opts).exec(this.client);evalRo=(...a)=>new dR(a,this.opts).exec(this.client);eval=(...a)=>new dS(a,this.opts).exec(this.client);evalshaRo=(...a)=>new dT(a,this.opts).exec(this.client);evalsha=(...a)=>new dU(a,this.opts).exec(this.client);exec=a=>new dV(a,this.opts).exec(this.client);exists=(...a)=>new dW(a,this.opts).exec(this.client);expire=(...a)=>new dX(a,this.opts).exec(this.client);expireat=(...a)=>new dY(a,this.opts).exec(this.client);flushall=a=>new dZ(a,this.opts).exec(this.client);flushdb=(...a)=>new d$(a,this.opts).exec(this.client);geoadd=(...a)=>new d_(a,this.opts).exec(this.client);geopos=(...a)=>new d2(a,this.opts).exec(this.client);geodist=(...a)=>new d0(a,this.opts).exec(this.client);geohash=(...a)=>new d1(a,this.opts).exec(this.client);geosearch=(...a)=>new d3(a,this.opts).exec(this.client);geosearchstore=(...a)=>new d4(a,this.opts).exec(this.client);get=(...a)=>new d5(a,this.opts).exec(this.client);getbit=(...a)=>new d6(a,this.opts).exec(this.client);getdel=(...a)=>new d7(a,this.opts).exec(this.client);getex=(...a)=>new d8(a,this.opts).exec(this.client);getrange=(...a)=>new d9(a,this.opts).exec(this.client);getset=(a,b)=>new ea([a,b],this.opts).exec(this.client);hdel=(...a)=>new eb(a,this.opts).exec(this.client);hexists=(...a)=>new ec(a,this.opts).exec(this.client);hexpire=(...a)=>new ed(a,this.opts).exec(this.client);hexpireat=(...a)=>new ee(a,this.opts).exec(this.client);hexpiretime=(...a)=>new ef(a,this.opts).exec(this.client);httl=(...a)=>new ex(a,this.opts).exec(this.client);hpexpire=(...a)=>new eh(a,this.opts).exec(this.client);hpexpireat=(...a)=>new ei(a,this.opts).exec(this.client);hpexpiretime=(...a)=>new ej(a,this.opts).exec(this.client);hpttl=(...a)=>new ek(a,this.opts).exec(this.client);hpersist=(...a)=>new eg(a,this.opts).exec(this.client);hget=(...a)=>new el(a,this.opts).exec(this.client);hgetall=(...a)=>new em(a,this.opts).exec(this.client);hincrby=(...a)=>new en(a,this.opts).exec(this.client);hincrbyfloat=(...a)=>new eo(a,this.opts).exec(this.client);hkeys=(...a)=>new ep(a,this.opts).exec(this.client);hlen=(...a)=>new eq(a,this.opts).exec(this.client);hmget=(...a)=>new er(a,this.opts).exec(this.client);hmset=(a,b)=>new es([a,b],this.opts).exec(this.client);hrandfield=(a,b,c)=>new dF([a,b,c],this.opts).exec(this.client);hscan=(...a)=>new et(a,this.opts).exec(this.client);hset=(a,b)=>new eu([a,b],this.opts).exec(this.client);hsetnx=(a,b,c)=>new ev([a,b,c],this.opts).exec(this.client);hstrlen=(...a)=>new ew(a,this.opts).exec(this.client);hvals=(...a)=>new ey(a,this.opts).exec(this.client);incr=(...a)=>new ez(a,this.opts).exec(this.client);incrby=(...a)=>new eA(a,this.opts).exec(this.client);incrbyfloat=(...a)=>new eB(a,this.opts).exec(this.client);keys=(...a)=>new eZ(a,this.opts).exec(this.client);lindex=(...a)=>new e$(a,this.opts).exec(this.client);linsert=(a,b,c,d)=>new e_([a,b,c,d],this.opts).exec(this.client);llen=(...a)=>new e0(a,this.opts).exec(this.client);lmove=(...a)=>new e1(a,this.opts).exec(this.client);lpop=(...a)=>new e3(a,this.opts).exec(this.client);lmpop=(...a)=>new e2(a,this.opts).exec(this.client);lpos=(...a)=>new e4(a,this.opts).exec(this.client);lpush=(a,...b)=>new e5([a,...b],this.opts).exec(this.client);lpushx=(a,...b)=>new e6([a,...b],this.opts).exec(this.client);lrange=(...a)=>new e7(a,this.opts).exec(this.client);lrem=(a,b,c)=>new e8([a,b,c],this.opts).exec(this.client);lset=(a,b,c)=>new e9([a,b,c],this.opts).exec(this.client);ltrim=(...a)=>new fa(a,this.opts).exec(this.client);mget=(...a)=>new fb(a,this.opts).exec(this.client);mset=a=>new fc([a],this.opts).exec(this.client);msetnx=a=>new fd([a],this.opts).exec(this.client);persist=(...a)=>new fe(a,this.opts).exec(this.client);pexpire=(...a)=>new ff(a,this.opts).exec(this.client);pexpireat=(...a)=>new fg(a,this.opts).exec(this.client);pfadd=(...a)=>new fh(a,this.opts).exec(this.client);pfcount=(...a)=>new fi(a,this.opts).exec(this.client);pfmerge=(...a)=>new fj(a,this.opts).exec(this.client);ping=a=>new fk(a,this.opts).exec(this.client);psetex=(a,b,c)=>new fl([a,b,c],this.opts).exec(this.client);psubscribe=a=>{let b=Array.isArray(a)?a:[a];return new gA(this.client,b,!0)};pttl=(...a)=>new fm(a,this.opts).exec(this.client);publish=(...a)=>new fn(a,this.opts).exec(this.client);randomkey=()=>new fo().exec(this.client);rename=(...a)=>new fp(a,this.opts).exec(this.client);renamenx=(...a)=>new fq(a,this.opts).exec(this.client);rpop=(...a)=>new fr(a,this.opts).exec(this.client);rpush=(a,...b)=>new fs([a,...b],this.opts).exec(this.client);rpushx=(a,...b)=>new ft([a,...b],this.opts).exec(this.client);sadd=(a,b,...c)=>new fu([a,b,...c],this.opts).exec(this.client);scan(a,b){return new fv([a,b],this.opts).exec(this.client)}scard=(...a)=>new fw(a,this.opts).exec(this.client);scriptExists=(...a)=>new fx(a,this.opts).exec(this.client);scriptFlush=(...a)=>new fy(a,this.opts).exec(this.client);scriptLoad=(...a)=>new fz(a,this.opts).exec(this.client);sdiff=(...a)=>new fA(a,this.opts).exec(this.client);sdiffstore=(...a)=>new fB(a,this.opts).exec(this.client);set=(a,b,c)=>new fC([a,b,c],this.opts).exec(this.client);setbit=(...a)=>new fD(a,this.opts).exec(this.client);setex=(a,b,c)=>new fE([a,b,c],this.opts).exec(this.client);setnx=(a,b)=>new fF([a,b],this.opts).exec(this.client);setrange=(...a)=>new fG(a,this.opts).exec(this.client);sinter=(...a)=>new fH(a,this.opts).exec(this.client);sinterstore=(...a)=>new fI(a,this.opts).exec(this.client);sismember=(a,b)=>new fJ([a,b],this.opts).exec(this.client);smismember=(a,b)=>new fL([a,b],this.opts).exec(this.client);smembers=(...a)=>new fK(a,this.opts).exec(this.client);smove=(a,b,c)=>new fM([a,b,c],this.opts).exec(this.client);spop=(...a)=>new fN(a,this.opts).exec(this.client);srandmember=(...a)=>new fO(a,this.opts).exec(this.client);srem=(a,...b)=>new fP([a,...b],this.opts).exec(this.client);sscan=(...a)=>new fQ(a,this.opts).exec(this.client);strlen=(...a)=>new fR(a,this.opts).exec(this.client);subscribe=a=>{let b=Array.isArray(a)?a:[a];return new gA(this.client,b)};sunion=(...a)=>new fS(a,this.opts).exec(this.client);sunionstore=(...a)=>new fT(a,this.opts).exec(this.client);time=()=>new fU().exec(this.client);touch=(...a)=>new fV(a,this.opts).exec(this.client);ttl=(...a)=>new fW(a,this.opts).exec(this.client);type=(...a)=>new fX(a,this.opts).exec(this.client);unlink=(...a)=>new fY(a,this.opts).exec(this.client);xadd=(...a)=>new f$(a,this.opts).exec(this.client);xack=(...a)=>new fZ(a,this.opts).exec(this.client);xdel=(...a)=>new f1(a,this.opts).exec(this.client);xgroup=(...a)=>new f2(a,this.opts).exec(this.client);xread=(...a)=>new f7(a,this.opts).exec(this.client);xreadgroup=(...a)=>new f8(a,this.opts).exec(this.client);xinfo=(...a)=>new f3(a,this.opts).exec(this.client);xlen=(...a)=>new f4(a,this.opts).exec(this.client);xpending=(...a)=>new f5(a,this.opts).exec(this.client);xclaim=(...a)=>new f0(a,this.opts).exec(this.client);xautoclaim=(...a)=>new f_(a,this.opts).exec(this.client);xtrim=(...a)=>new ga(a,this.opts).exec(this.client);xrange=(...a)=>new f6(a,this.opts).exec(this.client);xrevrange=(...a)=>new f9(a,this.opts).exec(this.client);zadd=(...a)=>("score"in a[1],new gb([a[0],a[1],...a.slice(2)],this.opts).exec(this.client));zcard=(...a)=>new gc(a,this.opts).exec(this.client);zcount=(...a)=>new gd(a,this.opts).exec(this.client);zdiffstore=(...a)=>new gu(a,this.opts).exec(this.client);zincrby=(a,b,c)=>new ge([a,b,c],this.opts).exec(this.client);zinterstore=(...a)=>new gf(a,this.opts).exec(this.client);zlexcount=(...a)=>new gg(a,this.opts).exec(this.client);zmscore=(...a)=>new gv(a,this.opts).exec(this.client);zpopmax=(...a)=>new gh(a,this.opts).exec(this.client);zpopmin=(...a)=>new gi(a,this.opts).exec(this.client);zrange=(...a)=>new gj(a,this.opts).exec(this.client);zrank=(a,b)=>new gk([a,b],this.opts).exec(this.client);zrem=(a,...b)=>new gl([a,...b],this.opts).exec(this.client);zremrangebylex=(...a)=>new gm(a,this.opts).exec(this.client);zremrangebyrank=(...a)=>new gn(a,this.opts).exec(this.client);zremrangebyscore=(...a)=>new go(a,this.opts).exec(this.client);zrevrank=(a,b)=>new gp([a,b],this.opts).exec(this.client);zscan=(...a)=>new gq(a,this.opts).exec(this.client);zscore=(a,b)=>new gr([a,b],this.opts).exec(this.client);zunion=(...a)=>new gs(a,this.opts).exec(this.client);zunionstore=(...a)=>new gt(a,this.opts).exec(this.client)};"undefined"==typeof atob&&(global.atob=a=>Buffer.from(a,"base64").toString("utf8"));var gF=class a extends gE{constructor(a){if("request"in a)return void super(a);if(a.url?(a.url.startsWith(" ")||a.url.endsWith(" ")||/\r|\n/.test(a.url))&&console.warn("[Upstash Redis] The redis url contains whitespace or newline, which can cause errors!"):console.warn("[Upstash Redis] The 'url' property is missing or undefined in your Redis config."),a.token?(a.token.startsWith(" ")||a.token.endsWith(" ")||/\r|\n/.test(a.token))&&console.warn("[Upstash Redis] The redis token contains whitespace or newline, which can cause errors!"):console.warn("[Upstash Redis] The 'token' property is missing or undefined in your Redis config."),super(new dz({baseUrl:a.url,retry:a.retry,headers:{authorization:`Bearer ${a.token}`},agent:a.agent,responseEncoding:a.responseEncoding,cache:a.cache??"no-store",signal:a.signal,keepAlive:a.keepAlive,readYourWrites:a.readYourWrites}),{automaticDeserialization:a.automaticDeserialization,enableTelemetry:!process.env.UPSTASH_DISABLE_TELEMETRY,latencyLogging:a.latencyLogging,enableAutoPipelining:a.enableAutoPipelining}),this.addTelemetry({runtime:"string"==typeof EdgeRuntime?"edge-light":`node@${process.version}`,platform:process.env.VERCEL?"vercel":process.env.AWS_REGION?"aws":"unknown",sdk:"@upstash/redis@v1.35.1"}),this.enableAutoPipelining)return this.autoPipeline()}static fromEnv(b){if(void 0===process.env)throw TypeError('[Upstash Redis] Unable to get environment variables, `process.env` is undefined. If you are deploying to cloudflare, please import from "@upstash/redis/cloudflare" instead');let c=process.env.UPSTASH_REDIS_REST_URL||process.env.KV_REST_API_URL;c||console.warn("[Upstash Redis] Unable to find environment variable: `UPSTASH_REDIS_REST_URL`");let d=process.env.UPSTASH_REDIS_REST_TOKEN||process.env.KV_REST_API_TOKEN;return d||console.warn("[Upstash Redis] Unable to find environment variable: `UPSTASH_REDIS_REST_TOKEN`"),new a({...b,url:c,token:d})}};let gG=null;async function gH(a){if("development"===dl._.NODE_ENV)return{allowed:!0};let b=(gG||(gG=new gF({url:dl._.UPSTASH_REDIS_REST_URL,token:dl._.UPSTASH_REDIS_REST_TOKEN})),gG);try{let c=Math.floor(Date.now()/1e3),d=`ewa:user_rate_limit:${a}`,e="ewa:global_rate_limit",f=await b.incr(d);1===f&&await b.expire(d,3600);let g=await b.ttl(d),h=c+g;if(f>3)return{allowed:!1,reason:"user_limit",userCount:f,userResetTime:h};let i=await b.incr(e);1===i&&await b.expire(e,86400);let j=await b.ttl(e),k=c+j;if(i>100)return await b.decr(d),{allowed:!1,reason:"global_limit",globalCount:i,globalResetTime:k};return{allowed:!0,userCount:f,globalCount:i,userResetTime:h,globalResetTime:k}}catch(a){return console.error("Upstash Redis rate limit check failed:",a),{allowed:!0}}}c(6852);var gI=c(2166);async function gJ(){let a=await (0,gI.b)(),b=a.get("x-forwarded-for"),c=a.get("x-real-ip"),d=a.get("cf-connecting-ip"),e=a.get("x-client-ip");return b?b.split(",").map(a=>a.trim())[0]:d||c||e||"127.0.0.1"}c(8143);var gK=c(7858),gL=c(6838),gM=c(4914);function gN(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}var gO=function(a){let b=function(a){let b=gM.forwardRef((a,b)=>{let{children:c,...d}=a;if(gM.isValidElement(c)){var e;let a,f,g=(e=c,(f=(a=Object.getOwnPropertyDescriptor(e.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.ref:(f=(a=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?e.props.ref:e.props.ref||e.ref),h=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(d,c.props);return c.type!==gM.Fragment&&(h.ref=b?function(...a){return b=>{let c=!1,d=a.map(a=>{let d=gN(a,b);return c||"function"!=typeof d||(c=!0),d});if(c)return()=>{for(let b=0;b<d.length;b++){let c=d[b];"function"==typeof c?c():gN(a[b],null)}}}}(b,g):g),gM.cloneElement(c,h)}return gM.Children.count(c)>1?gM.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),c=gM.forwardRef((a,c)=>{let{children:d,...e}=a,f=gM.Children.toArray(d),g=f.find(gQ);if(g){let a=g.props.children,d=f.map(b=>b!==g?b:gM.Children.count(a)>1?gM.Children.only(null):gM.isValidElement(a)?a.props.children:null);return(0,A.jsx)(b,{...e,ref:c,children:gM.isValidElement(a)?gM.cloneElement(a,void 0,d):null})}return(0,A.jsx)(b,{...e,ref:c,children:d})});return c.displayName=`${a}.Slot`,c}("Slot"),gP=Symbol("radix.slottable");function gQ(a){return gM.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===gP}function gR(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}let gS=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,gT=(a,b)=>{if(0===a.length)return b.classGroupId;let c=a[0],d=b.nextPart.get(c),e=d?gT(a.slice(1),d):void 0;if(e)return e;if(0===b.validators.length)return;let f=a.join("-");return b.validators.find(({validator:a})=>a(f))?.classGroupId},gU=/^\[(.+)\]$/,gV=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:gW(b,a)).classGroupId=c;return}if("function"==typeof a)return gX(a)?void gV(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,e])=>{gV(e,gW(b,a),c,d)})})},gW=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},gX=a=>a.isThemeGetter,gY=/\s+/;function gZ(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=g$(a))&&(d&&(d+=" "),d+=b);return d}let g$=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=g$(a[d]))&&(c&&(c+=" "),c+=b);return c},g_=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},g0=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,g1=/^\((?:(\w[\w-]*):)?(.+)\)$/i,g2=/^\d+\/\d+$/,g3=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g4=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,g5=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,g6=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,g7=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,g8=a=>g2.test(a),g9=a=>!!a&&!Number.isNaN(Number(a)),ha=a=>!!a&&Number.isInteger(Number(a)),hb=a=>a.endsWith("%")&&g9(a.slice(0,-1)),hc=a=>g3.test(a),hd=()=>!0,he=a=>g4.test(a)&&!g5.test(a),hf=()=>!1,hg=a=>g6.test(a),hh=a=>g7.test(a),hi=a=>!hk(a)&&!hq(a),hj=a=>hx(a,hB,hf),hk=a=>g0.test(a),hl=a=>hx(a,hC,he),hm=a=>hx(a,hD,g9),hn=a=>hx(a,hz,hf),ho=a=>hx(a,hA,hh),hp=a=>hx(a,hF,hg),hq=a=>g1.test(a),hr=a=>hy(a,hC),hs=a=>hy(a,hE),ht=a=>hy(a,hz),hu=a=>hy(a,hB),hv=a=>hy(a,hA),hw=a=>hy(a,hF,!0),hx=(a,b,c)=>{let d=g0.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},hy=(a,b,c=!1)=>{let d=g1.exec(a);return!!d&&(d[1]?b(d[1]):c)},hz=a=>"position"===a||"percentage"===a,hA=a=>"image"===a||"url"===a,hB=a=>"length"===a||"size"===a||"bg-size"===a,hC=a=>"length"===a,hD=a=>"number"===a,hE=a=>"family-name"===a,hF=a=>"shadow"===a;Symbol.toStringTag;let hG=function(a,...b){let c,d,e,f=function(h){let i;return d=(c={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((i=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c,d=[],e=0,f=0,g=0;for(let c=0;c<a.length;c++){let h=a[c];if(0===e&&0===f){if(":"===h){d.push(a.slice(g,c)),g=c+1;continue}if("/"===h){b=c;continue}}"["===h?e++:"]"===h?e--:"("===h?f++:")"===h&&f--}let h=0===d.length?a:a.substring(g),i=(c=h).endsWith("!")?c.substring(0,c.length-1):c.startsWith("!")?c.substring(1):c;return{modifiers:d,hasImportantModifier:i!==h,baseClassName:i,maybePostfixModifierPosition:b&&b>g?b-g:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d})(i),sortModifiers:(a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}})(i),...(a=>{let b=(a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)gV(c[a],d,a,b);return d})(a),{conflictingClassGroups:c,conflictingClassGroupModifiers:d}=a;return{getClassGroupId:a=>{let c=a.split("-");return""===c[0]&&1!==c.length&&c.shift(),gT(c,b)||(a=>{if(gU.test(a)){let b=gU.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let e=c[a]||[];return b&&d[a]?[...e,...d[a]]:e}}})(i)}).cache.get,e=c.cache.set,f=g,g(h)};function g(a){let b=d(a);if(b)return b;let f=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],h=a.trim().split(gY),i="";for(let a=h.length-1;a>=0;a-=1){let b=h[a],{isExternal:j,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(j){i=b+(i.length>0?" "+i:i);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){i=b+(i.length>0?" "+i:i);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}i=b+(i.length>0?" "+i:i)}return i})(a,c);return e(a,f),f}return function(){return f(gZ.apply(null,arguments))}}(()=>{let a=g_("color"),b=g_("font"),c=g_("text"),d=g_("font-weight"),e=g_("tracking"),f=g_("leading"),g=g_("breakpoint"),h=g_("container"),i=g_("spacing"),j=g_("radius"),k=g_("shadow"),l=g_("inset-shadow"),m=g_("text-shadow"),n=g_("drop-shadow"),o=g_("blur"),p=g_("perspective"),q=g_("aspect"),r=g_("ease"),s=g_("animate"),t=()=>["auto","avoid","all","avoid-page","page","left","right","column"],u=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],v=()=>[...u(),hq,hk],w=()=>["auto","hidden","clip","visible","scroll"],x=()=>["auto","contain","none"],y=()=>[hq,hk,i],z=()=>[g8,"full","auto",...y()],A=()=>[ha,"none","subgrid",hq,hk],B=()=>["auto",{span:["full",ha,hq,hk]},ha,hq,hk],C=()=>[ha,"auto",hq,hk],D=()=>["auto","min","max","fr",hq,hk],E=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],F=()=>["start","end","center","stretch","center-safe","end-safe"],G=()=>["auto",...y()],H=()=>[g8,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...y()],I=()=>[a,hq,hk],J=()=>[...u(),ht,hn,{position:[hq,hk]}],K=()=>["no-repeat",{repeat:["","x","y","space","round"]}],L=()=>["auto","cover","contain",hu,hj,{size:[hq,hk]}],M=()=>[hb,hr,hl],N=()=>["","none","full",j,hq,hk],O=()=>["",g9,hr,hl],P=()=>["solid","dashed","dotted","double"],Q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],R=()=>[g9,hb,ht,hn],S=()=>["","none",o,hq,hk],T=()=>["none",g9,hq,hk],U=()=>["none",g9,hq,hk],V=()=>[g9,hq,hk],W=()=>[g8,"full",...y()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[hc],breakpoint:[hc],color:[hd],container:[hc],"drop-shadow":[hc],ease:["in","out","in-out"],font:[hi],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[hc],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[hc],shadow:[hc],spacing:["px",g9],text:[hc],"text-shadow":[hc],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",g8,hk,hq,q]}],container:["container"],columns:[{columns:[g9,hk,hq,h]}],"break-after":[{"break-after":t()}],"break-before":[{"break-before":t()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:v()}],overflow:[{overflow:w()}],"overflow-x":[{"overflow-x":w()}],"overflow-y":[{"overflow-y":w()}],overscroll:[{overscroll:x()}],"overscroll-x":[{"overscroll-x":x()}],"overscroll-y":[{"overscroll-y":x()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:z()}],"inset-x":[{"inset-x":z()}],"inset-y":[{"inset-y":z()}],start:[{start:z()}],end:[{end:z()}],top:[{top:z()}],right:[{right:z()}],bottom:[{bottom:z()}],left:[{left:z()}],visibility:["visible","invisible","collapse"],z:[{z:[ha,"auto",hq,hk]}],basis:[{basis:[g8,"full","auto",h,...y()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[g9,g8,"auto","initial","none",hk]}],grow:[{grow:["",g9,hq,hk]}],shrink:[{shrink:["",g9,hq,hk]}],order:[{order:[ha,"first","last","none",hq,hk]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:B()}],"col-start":[{"col-start":C()}],"col-end":[{"col-end":C()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:B()}],"row-start":[{"row-start":C()}],"row-end":[{"row-end":C()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":D()}],"auto-rows":[{"auto-rows":D()}],gap:[{gap:y()}],"gap-x":[{"gap-x":y()}],"gap-y":[{"gap-y":y()}],"justify-content":[{justify:[...E(),"normal"]}],"justify-items":[{"justify-items":[...F(),"normal"]}],"justify-self":[{"justify-self":["auto",...F()]}],"align-content":[{content:["normal",...E()]}],"align-items":[{items:[...F(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...F(),{baseline:["","last"]}]}],"place-content":[{"place-content":E()}],"place-items":[{"place-items":[...F(),"baseline"]}],"place-self":[{"place-self":["auto",...F()]}],p:[{p:y()}],px:[{px:y()}],py:[{py:y()}],ps:[{ps:y()}],pe:[{pe:y()}],pt:[{pt:y()}],pr:[{pr:y()}],pb:[{pb:y()}],pl:[{pl:y()}],m:[{m:G()}],mx:[{mx:G()}],my:[{my:G()}],ms:[{ms:G()}],me:[{me:G()}],mt:[{mt:G()}],mr:[{mr:G()}],mb:[{mb:G()}],ml:[{ml:G()}],"space-x":[{"space-x":y()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":y()}],"space-y-reverse":["space-y-reverse"],size:[{size:H()}],w:[{w:[h,"screen",...H()]}],"min-w":[{"min-w":[h,"screen","none",...H()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...H()]}],h:[{h:["screen","lh",...H()]}],"min-h":[{"min-h":["screen","lh","none",...H()]}],"max-h":[{"max-h":["screen","lh",...H()]}],"font-size":[{text:["base",c,hr,hl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,hq,hm]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",hb,hk]}],"font-family":[{font:[hs,hk,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,hq,hk]}],"line-clamp":[{"line-clamp":[g9,"none",hq,hm]}],leading:[{leading:[f,...y()]}],"list-image":[{"list-image":["none",hq,hk]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",hq,hk]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:I()}],"text-color":[{text:I()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...P(),"wavy"]}],"text-decoration-thickness":[{decoration:[g9,"from-font","auto",hq,hl]}],"text-decoration-color":[{decoration:I()}],"underline-offset":[{"underline-offset":[g9,"auto",hq,hk]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:y()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",hq,hk]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",hq,hk]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:J()}],"bg-repeat":[{bg:K()}],"bg-size":[{bg:L()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},ha,hq,hk],radial:["",hq,hk],conic:[ha,hq,hk]},hv,ho]}],"bg-color":[{bg:I()}],"gradient-from-pos":[{from:M()}],"gradient-via-pos":[{via:M()}],"gradient-to-pos":[{to:M()}],"gradient-from":[{from:I()}],"gradient-via":[{via:I()}],"gradient-to":[{to:I()}],rounded:[{rounded:N()}],"rounded-s":[{"rounded-s":N()}],"rounded-e":[{"rounded-e":N()}],"rounded-t":[{"rounded-t":N()}],"rounded-r":[{"rounded-r":N()}],"rounded-b":[{"rounded-b":N()}],"rounded-l":[{"rounded-l":N()}],"rounded-ss":[{"rounded-ss":N()}],"rounded-se":[{"rounded-se":N()}],"rounded-ee":[{"rounded-ee":N()}],"rounded-es":[{"rounded-es":N()}],"rounded-tl":[{"rounded-tl":N()}],"rounded-tr":[{"rounded-tr":N()}],"rounded-br":[{"rounded-br":N()}],"rounded-bl":[{"rounded-bl":N()}],"border-w":[{border:O()}],"border-w-x":[{"border-x":O()}],"border-w-y":[{"border-y":O()}],"border-w-s":[{"border-s":O()}],"border-w-e":[{"border-e":O()}],"border-w-t":[{"border-t":O()}],"border-w-r":[{"border-r":O()}],"border-w-b":[{"border-b":O()}],"border-w-l":[{"border-l":O()}],"divide-x":[{"divide-x":O()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":O()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...P(),"hidden","none"]}],"divide-style":[{divide:[...P(),"hidden","none"]}],"border-color":[{border:I()}],"border-color-x":[{"border-x":I()}],"border-color-y":[{"border-y":I()}],"border-color-s":[{"border-s":I()}],"border-color-e":[{"border-e":I()}],"border-color-t":[{"border-t":I()}],"border-color-r":[{"border-r":I()}],"border-color-b":[{"border-b":I()}],"border-color-l":[{"border-l":I()}],"divide-color":[{divide:I()}],"outline-style":[{outline:[...P(),"none","hidden"]}],"outline-offset":[{"outline-offset":[g9,hq,hk]}],"outline-w":[{outline:["",g9,hr,hl]}],"outline-color":[{outline:I()}],shadow:[{shadow:["","none",k,hw,hp]}],"shadow-color":[{shadow:I()}],"inset-shadow":[{"inset-shadow":["none",l,hw,hp]}],"inset-shadow-color":[{"inset-shadow":I()}],"ring-w":[{ring:O()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:I()}],"ring-offset-w":[{"ring-offset":[g9,hl]}],"ring-offset-color":[{"ring-offset":I()}],"inset-ring-w":[{"inset-ring":O()}],"inset-ring-color":[{"inset-ring":I()}],"text-shadow":[{"text-shadow":["none",m,hw,hp]}],"text-shadow-color":[{"text-shadow":I()}],opacity:[{opacity:[g9,hq,hk]}],"mix-blend":[{"mix-blend":[...Q(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Q()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[g9]}],"mask-image-linear-from-pos":[{"mask-linear-from":R()}],"mask-image-linear-to-pos":[{"mask-linear-to":R()}],"mask-image-linear-from-color":[{"mask-linear-from":I()}],"mask-image-linear-to-color":[{"mask-linear-to":I()}],"mask-image-t-from-pos":[{"mask-t-from":R()}],"mask-image-t-to-pos":[{"mask-t-to":R()}],"mask-image-t-from-color":[{"mask-t-from":I()}],"mask-image-t-to-color":[{"mask-t-to":I()}],"mask-image-r-from-pos":[{"mask-r-from":R()}],"mask-image-r-to-pos":[{"mask-r-to":R()}],"mask-image-r-from-color":[{"mask-r-from":I()}],"mask-image-r-to-color":[{"mask-r-to":I()}],"mask-image-b-from-pos":[{"mask-b-from":R()}],"mask-image-b-to-pos":[{"mask-b-to":R()}],"mask-image-b-from-color":[{"mask-b-from":I()}],"mask-image-b-to-color":[{"mask-b-to":I()}],"mask-image-l-from-pos":[{"mask-l-from":R()}],"mask-image-l-to-pos":[{"mask-l-to":R()}],"mask-image-l-from-color":[{"mask-l-from":I()}],"mask-image-l-to-color":[{"mask-l-to":I()}],"mask-image-x-from-pos":[{"mask-x-from":R()}],"mask-image-x-to-pos":[{"mask-x-to":R()}],"mask-image-x-from-color":[{"mask-x-from":I()}],"mask-image-x-to-color":[{"mask-x-to":I()}],"mask-image-y-from-pos":[{"mask-y-from":R()}],"mask-image-y-to-pos":[{"mask-y-to":R()}],"mask-image-y-from-color":[{"mask-y-from":I()}],"mask-image-y-to-color":[{"mask-y-to":I()}],"mask-image-radial":[{"mask-radial":[hq,hk]}],"mask-image-radial-from-pos":[{"mask-radial-from":R()}],"mask-image-radial-to-pos":[{"mask-radial-to":R()}],"mask-image-radial-from-color":[{"mask-radial-from":I()}],"mask-image-radial-to-color":[{"mask-radial-to":I()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":u()}],"mask-image-conic-pos":[{"mask-conic":[g9]}],"mask-image-conic-from-pos":[{"mask-conic-from":R()}],"mask-image-conic-to-pos":[{"mask-conic-to":R()}],"mask-image-conic-from-color":[{"mask-conic-from":I()}],"mask-image-conic-to-color":[{"mask-conic-to":I()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:J()}],"mask-repeat":[{mask:K()}],"mask-size":[{mask:L()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",hq,hk]}],filter:[{filter:["","none",hq,hk]}],blur:[{blur:S()}],brightness:[{brightness:[g9,hq,hk]}],contrast:[{contrast:[g9,hq,hk]}],"drop-shadow":[{"drop-shadow":["","none",n,hw,hp]}],"drop-shadow-color":[{"drop-shadow":I()}],grayscale:[{grayscale:["",g9,hq,hk]}],"hue-rotate":[{"hue-rotate":[g9,hq,hk]}],invert:[{invert:["",g9,hq,hk]}],saturate:[{saturate:[g9,hq,hk]}],sepia:[{sepia:["",g9,hq,hk]}],"backdrop-filter":[{"backdrop-filter":["","none",hq,hk]}],"backdrop-blur":[{"backdrop-blur":S()}],"backdrop-brightness":[{"backdrop-brightness":[g9,hq,hk]}],"backdrop-contrast":[{"backdrop-contrast":[g9,hq,hk]}],"backdrop-grayscale":[{"backdrop-grayscale":["",g9,hq,hk]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[g9,hq,hk]}],"backdrop-invert":[{"backdrop-invert":["",g9,hq,hk]}],"backdrop-opacity":[{"backdrop-opacity":[g9,hq,hk]}],"backdrop-saturate":[{"backdrop-saturate":[g9,hq,hk]}],"backdrop-sepia":[{"backdrop-sepia":["",g9,hq,hk]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":y()}],"border-spacing-x":[{"border-spacing-x":y()}],"border-spacing-y":[{"border-spacing-y":y()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",hq,hk]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[g9,"initial",hq,hk]}],ease:[{ease:["linear","initial",r,hq,hk]}],delay:[{delay:[g9,hq,hk]}],animate:[{animate:["none",s,hq,hk]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,hq,hk]}],"perspective-origin":[{"perspective-origin":v()}],rotate:[{rotate:T()}],"rotate-x":[{"rotate-x":T()}],"rotate-y":[{"rotate-y":T()}],"rotate-z":[{"rotate-z":T()}],scale:[{scale:U()}],"scale-x":[{"scale-x":U()}],"scale-y":[{"scale-y":U()}],"scale-z":[{"scale-z":U()}],"scale-3d":["scale-3d"],skew:[{skew:V()}],"skew-x":[{"skew-x":V()}],"skew-y":[{"skew-y":V()}],transform:[{transform:[hq,hk,"","none","gpu","cpu"]}],"transform-origin":[{origin:v()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:W()}],"translate-x":[{"translate-x":W()}],"translate-y":[{"translate-y":W()}],"translate-z":[{"translate-z":W()}],"translate-none":["translate-none"],accent:[{accent:I()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:I()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",hq,hk]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":y()}],"scroll-mx":[{"scroll-mx":y()}],"scroll-my":[{"scroll-my":y()}],"scroll-ms":[{"scroll-ms":y()}],"scroll-me":[{"scroll-me":y()}],"scroll-mt":[{"scroll-mt":y()}],"scroll-mr":[{"scroll-mr":y()}],"scroll-mb":[{"scroll-mb":y()}],"scroll-ml":[{"scroll-ml":y()}],"scroll-p":[{"scroll-p":y()}],"scroll-px":[{"scroll-px":y()}],"scroll-py":[{"scroll-py":y()}],"scroll-ps":[{"scroll-ps":y()}],"scroll-pe":[{"scroll-pe":y()}],"scroll-pt":[{"scroll-pt":y()}],"scroll-pr":[{"scroll-pr":y()}],"scroll-pb":[{"scroll-pb":y()}],"scroll-pl":[{"scroll-pl":y()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",hq,hk]}],fill:[{fill:["none",...I()]}],"stroke-w":[{stroke:[g9,hr,hl,hm]}],stroke:[{stroke:["none",...I()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}}),hH=((a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return gR(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:e,defaultVariants:f}=b,g=Object.keys(e).map(a=>{let b=null==c?void 0:c[a],d=null==f?void 0:f[a];if(null===b)return null;let g=gS(b)||gS(d);return e[a][g]}),h=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return gR(a,g,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...f,...h}[b]):({...f,...h})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)})("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function hI({className:a,variant:b,size:c,asChild:d=!1,...e}){return(0,A.jsx)(d?gO:"button",{"data-slot":"button",className:function(...a){return hG(gR(a))}(hH({variant:b,size:c,className:a})),...e})}var hJ=c(2427),hK=c(7040);let hL=(0,hK.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),hM=(0,hK.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),hN=(0,hK.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var hO=c(7910),hP=c.n(hO);function hQ({type:a,resetTime:b}){let c="user"===a;return(0,A.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 flex items-center justify-center p-4",children:(0,A.jsx)("div",{className:"max-w-2xl w-full",children:(0,A.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 border border-red-100",children:[(0,A.jsxs)("div",{className:"text-center mb-8",children:[(0,A.jsx)("div",{className:"mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4",children:c?(0,A.jsx)(hJ.A,{className:"w-8 h-8 text-red-600"}):(0,A.jsx)(hL,{className:"w-8 h-8 text-red-600"})}),(0,A.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:c?"Slow down there!":"Server busy!"}),(0,A.jsx)("p",{className:"text-lg text-gray-600",children:c?"You're generating pages too quickly. Take a breather!":"Too many people are generating pages right now."})]}),(0,A.jsx)("div",{className:"bg-red-50 rounded-lg p-6 mb-8 border border-red-200",children:(0,A.jsxs)("div",{className:"flex items-center justify-between",children:[(0,A.jsxs)("div",{children:[(0,A.jsx)("h3",{className:"font-semibold text-red-800 mb-1",children:c?"Personal Rate Limit":"Global Rate Limit"}),(0,A.jsx)("p",{className:"text-red-700 text-sm",children:c?"You can generate 3 pages per hour":"We allow 100 new pages per day across all users"})]}),(0,A.jsxs)("div",{className:"text-right",children:[(0,A.jsx)("p",{className:"text-red-800 font-semibold",children:"Reset in:"}),(0,A.jsx)("p",{className:"text-red-600 text-lg font-mono",children:(a=>{if(!a)return"soon";let b=a-Math.floor(Date.now()/1e3);if(b<=0)return"now";let c=Math.floor(b/3600),d=Math.floor(b%3600/60);return c>0?`${c}h ${d}m`:`${d}m`})(b)})]})]})}),(0,A.jsxs)("div",{className:"mb-8",children:[(0,A.jsx)("h3",{className:"font-semibold text-gray-800 mb-4",children:"What you can do:"}),(0,A.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,A.jsxs)("li",{className:"flex items-start",children:[(0,A.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"}),"Browse existing pages - no limits on viewing!"]}),(0,A.jsxs)("li",{className:"flex items-start",children:[(0,A.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"}),c?"Wait for your rate limit to reset":"Try again later when traffic is lower"]}),(0,A.jsxs)("li",{className:"flex items-start",children:[(0,A.jsx)("span",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"}),"Check out our other projects while you wait"]})]})]}),(0,A.jsxs)("div",{className:"space-y-4",children:[(0,A.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,A.jsx)(hI,{asChild:!0,className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:(0,A.jsxs)("a",{href:"https://dothistask.ai",target:"_blank",rel:"noopener noreferrer",className:"flex items-center justify-center gap-2",children:[(0,A.jsx)(hM,{className:"w-4 h-4"}),"Check out DoThisTaskAI"]})}),(0,A.jsx)(hI,{asChild:!0,variant:"outline",className:"w-full border-blue-200 text-blue-700 hover:bg-blue-50",children:(0,A.jsxs)("a",{href:"https://x.com/N3SOnline",target:"_blank",rel:"noopener noreferrer",className:"flex items-center justify-center gap-2",children:[(0,A.jsx)(hN,{className:"w-4 h-4"}),"Follow on X"]})})]}),(0,A.jsx)(hI,{asChild:!0,variant:"ghost",className:"w-full text-gray-600 hover:text-gray-800",children:(0,A.jsx)(hP(),{href:"/",className:"flex items-center justify-center gap-2",children:"← Back to Home"})})]}),(0,A.jsx)("div",{className:"mt-8 pt-6 border-t border-gray-200 text-center",children:(0,A.jsx)("p",{className:"text-sm text-gray-500",children:"Rate limits help us keep the service free and fast for everyone. Thanks for understanding! \uD83D\uDE80"})})]})})})}async function hR({params:a}){let b,{slug:c}=await a;c&&!(c.length>100)&&/^[a-zA-Z0-9\-_\s]+$/.test(c)||(0,gK.notFound)();try{let a=await (0,dq.E3)(c);if(a)b=a;else{let a=await gJ(),d=await gH(a);if(!d.allowed)return(0,A.jsx)(hQ,{type:"user_limit"===d.reason?"user":"global",resetTime:"user_limit"===d.reason?d.userResetTime:d.globalResetTime});console.log(`Generating new webpage for slug: ${c} (User: ${a})`),b=await dp(c);try{await (0,dq.OV)(c,b),console.log(`Successfully cached webpage for slug: ${c}`)}catch(a){console.error("Failed to cache webpage to S3:",a)}}}catch(a){throw console.error("Error processing webpage:",a),Error("Failed to generate webpage. Please try again later.")}return"INAPPROPRIATE_PROMPT_DETECTED"===b.trim()?(0,A.jsx)(gL.default,{}):(0,A.jsx)("div",{dangerouslySetInnerHTML:{__html:b},style:{width:"100%",height:"100vh"}})}async function hS({params:a}){let{slug:b}=await a;return{title:`${b.replace(/[-_]/g," ").replace(/\b\w/g,a=>a.toUpperCase())} | Every Website AI`,description:`AI-generated webpage for: ${b}`}}},5708:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(9861),e=c(9351);function f(){return(0,d.jsx)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-yellow-50 to-orange-100 p-8",children:(0,d.jsxs)("div",{className:"text-center space-y-6 max-w-md",children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto bg-yellow-100 rounded-full flex items-center justify-center",children:(0,d.jsx)("svg",{className:"w-8 h-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:"Content Not Available"}),(0,d.jsx)("p",{className:"text-gray-600",children:"This request cannot be processed as it may contain inappropriate content or violates our content policy."})]}),(0,d.jsxs)("div",{className:"bg-white/50 rounded-lg p-4 text-left",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:"Please try requests for:"}),(0,d.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,d.jsx)("li",{children:"• Business websites"}),(0,d.jsx)("li",{children:"• Portfolio pages"}),(0,d.jsx)("li",{children:"• Landing pages"}),(0,d.jsx)("li",{children:"• Educational content"}),(0,d.jsx)("li",{children:"• Creative projects"}),(0,d.jsx)("li",{children:"• Tools and utilities"})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,d.jsx)(e.$,{onClick:()=>window.location.href="/",className:"px-6",children:"Go Home"}),(0,d.jsx)(e.$,{variant:"outline",onClick:()=>window.history.back(),className:"px-6",children:"Go Back"})]}),(0,d.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,d.jsx)("p",{className:"text-sm text-gray-500 mb-3",children:"Questions about our content policy?"}),(0,d.jsxs)("a",{href:"https://twitter.com/n3sonline",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors",children:[(0,d.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})}),"Tweet @n3sonline"]})]})]}),(0,d.jsxs)("div",{className:"mt-8 text-xs text-gray-500",children:["Powered by"," ",(0,d.jsx)("a",{href:"https://dothistask.ai",className:"underline",children:"dothistask.ai"})]})]})})}},5872:(a,b,c)=>{"use strict";c.d(b,{_:()=>g});var d=c(9662),e=c(3844);let f=d.Ik({AWS_ACCESS_KEY_ID:d.Yj().min(1,"AWS_ACCESS_KEY_ID is required"),AWS_SECRET_ACCESS_KEY:d.Yj().min(1,"AWS_SECRET_ACCESS_KEY is required"),AWS_REGION:d.Yj().min(1,"AWS_REGION is required"),AWS_S3_BUCKET_NAME:d.Yj().min(1,"AWS_S3_BUCKET_NAME is required"),ANTHROPIC_API_KEY:d.Yj().min(1,"ANTHROPIC_API_KEY is required"),UPSTASH_REDIS_REST_URL:d.Yj().min(1,"UPSTASH_REDIS_REST_URL is required"),UPSTASH_REDIS_REST_TOKEN:d.Yj().min(1,"UPSTASH_REDIS_REST_TOKEN is required"),NODE_ENV:d.k5(["development","production","test"]).default("development")}),g=(()=>{if(!process.env.ANTHROPIC_API_KEY)return console.warn("Environment variables not available during build time"),{};try{return f.parse(process.env)}catch(a){if(a instanceof e.G){let b=a.errors.map(a=>a.path.join(".")).join(", ");throw Error(`Missing or invalid environment variables: ${b}`)}throw a}})()},5926:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,648,23)),Promise.resolve().then(c.bind(c,5708))},6347:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"notFound",{enumerable:!0,get:function(){return e}});let d=""+c(3974).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function e(){let a=Object.defineProperty(Error(d),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw a.digest=d,a}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6516:(a,b,c)=>{Promise.resolve().then(c.bind(c,8379))},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},6838:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(2004).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/inappropriate-content.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/inappropriate-content.tsx","default")},6852:(a,b,c)=>{"use strict";let d=c(9991),e=c(5296),f=c(9294),g=c(3033),h=c(4549),i=c(4813),j=c(8030),k=c(7064);c(8529);c(4497),c(8627);c(9853);new WeakMap;(0,k.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})})},6872:()=>{},7817:(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unauthorized",{enumerable:!0,get:function(){return d}}),c(3974).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},7858:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{ReadonlyURLSearchParams:function(){return k},RedirectType:function(){return e.RedirectType},forbidden:function(){return g.forbidden},notFound:function(){return f.notFound},permanentRedirect:function(){return d.permanentRedirect},redirect:function(){return d.redirect},unauthorized:function(){return h.unauthorized},unstable_rethrow:function(){return i.unstable_rethrow}});let d=c(9163),e=c(8508),f=c(6347),g=c(3390),h=c(7817),i=c(8141);class j extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class k extends URLSearchParams{append(){throw new j}delete(){throw new j}set(){throw new j}sort(){throw new j}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},7962:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(9140);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},8057:()=>{},8141:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return d}});let d=c(9180).unstable_rethrow;("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},8143:(a,b,c)=>{"use strict";let d=c(3033),e=c(9294),f=c(4549),g=c(7064),h=c(4813),i=c(137),j=c(8627);c(9853);new WeakMap;(0,g.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})})},8354:a=>{"use strict";a.exports=require("util")},8379:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(9861),e=c(9351);function f({reset:a}){return(0,d.jsx)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-red-50 to-orange-100 p-8",children:(0,d.jsxs)("div",{className:"text-center space-y-6 max-w-md",children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center",children:(0,d.jsx)("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800",children:"Oops! Something went wrong"}),(0,d.jsx)("p",{className:"text-gray-600",children:"We couldn't generate your webpage right now. This might be a temporary issue."})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,d.jsx)(e.$,{onClick:a,className:"px-6",children:"Try Again"}),(0,d.jsx)(e.$,{variant:"outline",onClick:()=>window.location.href="/",className:"px-6",children:"Go Home"})]}),(0,d.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,d.jsx)("p",{className:"text-sm text-gray-500 mb-3",children:"Still having issues? Come back later or reach out:"}),(0,d.jsxs)("a",{href:"https://twitter.com/n3sonline",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors",children:[(0,d.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})}),"Tweet @n3sonline"]})]})]}),(0,d.jsxs)("div",{className:"mt-8 text-xs text-gray-500",children:["Powered by"," ",(0,d.jsx)("a",{href:"https://dothistask.ai",className:"underline",children:"dothistask.ai"})]})]})})}},8426:(a,b,c)=>{Promise.resolve().then(c.bind(c,2967))},8594:(a,b,c)=>{Promise.resolve().then(c.bind(c,4105))},8600:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,8613,23)),Promise.resolve().then(c.t.bind(c,3852,23)),Promise.resolve().then(c.t.bind(c,5442,23)),Promise.resolve().then(c.t.bind(c,8981,23)),Promise.resolve().then(c.t.bind(c,9105,23)),Promise.resolve().then(c.t.bind(c,7673,23)),Promise.resolve().then(c.t.bind(c,2801,23)),Promise.resolve().then(c.t.bind(c,5971,23)),Promise.resolve().then(c.bind(c,3793))},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9163:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getRedirectError:function(){return g},getRedirectStatusCodeFromError:function(){return l},getRedirectTypeFromError:function(){return k},getURLFromRedirectError:function(){return j},permanentRedirect:function(){return i},redirect:function(){return h}});let d=c(1990),e=c(8508),f=c(9121).actionAsyncStorage;function g(a,b,c){void 0===c&&(c=d.RedirectStatusCode.TemporaryRedirect);let f=Object.defineProperty(Error(e.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.digest=e.REDIRECT_ERROR_CODE+";"+b+";"+a+";"+c+";",f}function h(a,b){var c;throw null!=b||(b=(null==f||null==(c=f.getStore())?void 0:c.isAction)?e.RedirectType.push:e.RedirectType.replace),g(a,b,d.RedirectStatusCode.TemporaryRedirect)}function i(a,b){throw void 0===b&&(b=e.RedirectType.replace),g(a,b,d.RedirectStatusCode.PermanentRedirect)}function j(a){return(0,e.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null}function k(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}function l(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(a.digest.split(";").at(-2))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},9180:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rethrow",{enumerable:!0,get:function(){return function a(b){if((0,g.isNextRouterError)(b)||(0,f.isBailoutToCSRError)(b)||(0,i.isDynamicServerError)(b)||(0,h.isDynamicPostpone)(b)||(0,e.isPostpone)(b)||(0,d.isHangingPromiseRejectionError)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}}});let d=c(8030),e=c(2667),f=c(1424),g=c(7956),h=c(4549),i=c(137);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9351:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(9861);c(3440);var e=c(1250),f=c(6153),g=c(574),h=c(2490);let i=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function j({className:a,variant:b,size:c,asChild:f=!1,...j}){let k=f?e.DX:"button";return(0,d.jsx)(k,{"data-slot":"button",className:function(...a){return(0,h.QP)((0,g.$)(a))}(i({variant:b,size:c,className:a})),...j})}},9423:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(4001),e=c(1206),f=c(918),g=c(9623),h=c(3549),i=c(3477),j=c(5756),k=c(1549),l=c(631),m=c(3059),n=c(9460),o=c(4482),p=c(4591),q=c(261),r=c(5734),s=c(1691),t=c(6713),u=c(8151),v=c(4068),w=c(1432),x=c(6905),y=c(9791),z=c(6933),A=c(6439),B=c(5243),C=c.n(B),D=c(9559),E=c(1990),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,5630)),"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,3837)),"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,9741)),"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/error.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,4539)),"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,2967)),"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/not-found.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,7962))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,1087)),"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,5243,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,3827,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,1574,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,2609,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,7962))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/[slug]/page",pathname:"/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/[slug]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},9741:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(2004).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/error.tsx","default")},9913:()=>{},9991:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{MutableRequestCookiesAdapter:function(){return m},ReadonlyRequestCookiesError:function(){return h},RequestCookiesAdapter:function(){return i},appendMutableCookies:function(){return l},areCookiesMutableInCurrentPhase:function(){return o},getModifiedCookieValues:function(){return k},responseCookiesToRequestCookies:function(){return q},wrapWithMutableAccessCheck:function(){return n}});let d=c(5296),e=c(9853),f=c(9294),g=c(3033);class h extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new h}}class i{static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"clear":case"delete":case"set":return h.callable;default:return e.ReflectAdapter.get(a,b,c)}}})}}let j=Symbol.for("next.mutated.cookies");function k(a){let b=a[j];return b&&Array.isArray(b)&&0!==b.length?b:[]}function l(a,b){let c=k(b);if(0===c.length)return!1;let e=new d.ResponseCookies(a),f=e.getAll();for(let a of c)e.set(a);for(let a of f)e.set(a);return!0}class m{static wrap(a,b){let c=new d.ResponseCookies(new Headers);for(let b of a.getAll())c.set(b);let g=[],h=new Set,i=()=>{let a=f.workAsyncStorage.getStore();if(a&&(a.pathWasRevalidated=!0),g=c.getAll().filter(a=>h.has(a.name)),b){let a=[];for(let b of g){let c=new d.ResponseCookies(new Headers);c.set(b),a.push(c.toString())}b(a)}},k=new Proxy(c,{get(a,b,c){switch(b){case j:return g;case"delete":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.delete(...b),k}finally{i()}};case"set":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.set(...b),k}finally{i()}};default:return e.ReflectAdapter.get(a,b,c)}}});return k}}function n(a){let b=new Proxy(a,{get(a,c,d){switch(c){case"delete":return function(...c){return p("cookies().delete"),a.delete(...c),b};case"set":return function(...c){return p("cookies().set"),a.set(...c),b};default:return e.ReflectAdapter.get(a,c,d)}}});return b}function o(a){return"action"===a.phase}function p(a){if(!o((0,g.getExpectedRequestStore)(a)))throw new h}function q(a){let b=new d.RequestCookies(new Headers);for(let c of a.getAll())b.set(c);return b}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[505,926,779],()=>b(b.s=9423));module.exports=c})();