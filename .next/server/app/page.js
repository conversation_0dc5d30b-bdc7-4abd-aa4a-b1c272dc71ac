(()=>{var a={};a.id=974,a.ids=[974],a.modules={225:()=>{},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},698:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,648,23)),Promise.resolve().then(c.bind(c,1673))},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1087:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,metadata:()=>e});var d=c(8487);c(225);let e={title:"Every Website AI",description:"AI-powered website generator"};function f({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:"antialiased",children:a})})}},1673:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(9861),e=c(9351),f=c(3440),g=c(3611);function h(){let[a,b]=(0,f.useState)(""),[c,h]=(0,f.useState)(!1),i=(0,g.useRouter)();return(0,d.jsxs)("div",{className:"max-w-2xl w-full space-y-6 sm:space-y-8",children:[(0,d.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8 border border-gray-100",children:(0,d.jsxs)("form",{onSubmit:b=>{if(b.preventDefault(),!a.trim())return;h(!0);let c=a.trim().replace(/[^a-zA-Z0-9\s\-_]/g,"").replace(/\s+/g,"-");i.push(`/${c}`)},className:"space-y-4 sm:space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"webpage-input",className:"block text-base sm:text-lg font-semibold text-gray-800 mb-3",children:"Try it now - enter your prompt:"}),(0,d.jsxs)("div",{className:"flex items-center border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent transition-all",children:[(0,d.jsx)("span",{className:"pl-3 pr-1 text-gray-500 font-mono text-sm sm:text-base whitespace-nowrap",children:"everywebsite.ai/"}),(0,d.jsx)("input",{id:"webpage-input",type:"text",value:a,onChange:a=>b(a.target.value),placeholder:"calculator",className:"flex-1 pr-4 py-3 text-sm sm:text-lg border-0 outline-none font-mono bg-transparent",disabled:c})]})]}),(0,d.jsx)(e.$,{type:"submit",className:"w-full py-3 text-base sm:text-lg font-semibold",disabled:!a.trim()||c,children:c?"Generating...":"Generate Webpage"})]})}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-gray-600 mb-4 text-sm sm:text-base",children:"Try these example URLs:"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2 justify-center px-2",children:["calculator","password-generator","color-picker","qr-code-generator","unit-converter","timer-stopwatch"].map(a=>(0,d.jsxs)("button",{onClick:()=>b(a),className:"px-2 sm:px-3 py-1 text-xs sm:text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors font-mono break-all",disabled:c,children:["/",a]},a))})]})]})}},1752:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5243,23)),Promise.resolve().then(c.t.bind(c,3274,23)),Promise.resolve().then(c.t.bind(c,4588,23)),Promise.resolve().then(c.t.bind(c,8963,23)),Promise.resolve().then(c.t.bind(c,2563,23)),Promise.resolve().then(c.t.bind(c,3947,23)),Promise.resolve().then(c.t.bind(c,7163,23)),Promise.resolve().then(c.t.bind(c,7101,23)),Promise.resolve().then(c.t.bind(c,7331,23))},2823:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(2004).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/homepage-form.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/homepage-form.tsx","default")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3320:()=>{},3873:a=>{"use strict";a.exports=require("path")},4445:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(4001),e=c(1206),f=c(918),g=c(9623),h=c(3549),i=c(3477),j=c(5756),k=c(1549),l=c(631),m=c(3059),n=c(9460),o=c(4482),p=c(4591),q=c(261),r=c(5734),s=c(1691),t=c(6713),u=c(8151),v=c(4068),w=c(1432),x=c(6905),y=c(9791),z=c(6933),A=c(6439),B=c(5243),C=c.n(B),D=c(9559),E=c(1990),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,5890)),"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,7962))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,1087)),"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,5243,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,3827,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,1574,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,2609,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,7962))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},5244:(a,b,c)=>{"use strict";c.d(b,{E3:()=>h,Yx:()=>i,OV:()=>g});let d=require("@aws-sdk/client-s3");var e=c(5872);let f=new d.S3Client({region:e._.AWS_REGION,credentials:{accessKeyId:e._.AWS_ACCESS_KEY_ID,secretAccessKey:e._.AWS_SECRET_ACCESS_KEY}});async function g(a,b){let c=`websites/${a}.html`,g="INAPPROPRIATE_PROMPT_DETECTED"===b.trim(),h=new d.PutObjectCommand({Bucket:e._.AWS_S3_BUCKET_NAME,Key:c,Body:b,ContentType:g?"text/plain":"text/html",CacheControl:"max-age=3600",Metadata:{inappropriate:g?"true":"false"}});try{return await f.send(h),c}catch(a){throw console.error("Error uploading to S3:",a),Error("Failed to upload HTML to S3")}}async function h(a){let b=`websites/${a}.html`,c=new d.GetObjectCommand({Bucket:e._.AWS_S3_BUCKET_NAME,Key:b});try{let a=await f.send(c);if(a.Body)return await a.Body.transformToString();return null}catch(a){if(a?.name==="NoSuchKey")return null;throw console.error("Error fetching from S3:",a),Error("Failed to fetch HTML from S3")}}async function i(a=5){let b=new d.ListObjectsV2Command({Bucket:e._.AWS_S3_BUCKET_NAME,Prefix:"websites/",MaxKeys:50});try{let c=await f.send(b);if(!c.Contents)return[];return c.Contents.filter(a=>a.Key&&a.Key.endsWith(".html")&&a.LastModified&&a.Size&&a.Size>100).sort((a,b)=>{let c=a.LastModified?.getTime()||0;return(b.LastModified?.getTime()||0)-c}).slice(0,a).map(a=>{let b=a.Key.replace("websites/","").replace(".html",""),c=b.replace(/[-_]/g," ").replace(/\b\w/g,a=>a.toUpperCase());return{slug:b,lastModified:a.LastModified,title:c}})}catch(a){return console.error("Error fetching recent pages from S3:",a),[]}}},5872:(a,b,c)=>{"use strict";c.d(b,{_:()=>g});var d=c(9662),e=c(3844);let f=d.Ik({AWS_ACCESS_KEY_ID:d.Yj().min(1,"AWS_ACCESS_KEY_ID is required"),AWS_SECRET_ACCESS_KEY:d.Yj().min(1,"AWS_SECRET_ACCESS_KEY is required"),AWS_REGION:d.Yj().min(1,"AWS_REGION is required"),AWS_S3_BUCKET_NAME:d.Yj().min(1,"AWS_S3_BUCKET_NAME is required"),ANTHROPIC_API_KEY:d.Yj().min(1,"ANTHROPIC_API_KEY is required"),UPSTASH_REDIS_REST_URL:d.Yj().min(1,"UPSTASH_REDIS_REST_URL is required"),UPSTASH_REDIS_REST_TOKEN:d.Yj().min(1,"UPSTASH_REDIS_REST_TOKEN is required"),NODE_ENV:d.k5(["development","production","test"]).default("development")}),g=(()=>{if(!process.env.ANTHROPIC_API_KEY)return console.warn("Environment variables not available during build time"),{};try{return f.parse(process.env)}catch(a){if(a instanceof e.G){let b=a.errors.map(a=>a.path.join(".")).join(", ");throw Error(`Missing or invalid environment variables: ${b}`)}throw a}})()},5890:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>q});var d=c(8487),e=c(5244),f=c(2823),g=c(7910),h=c.n(g),i=c(2427);function j({pages:a}){return 0===a.length?null:(0,d.jsx)("section",{className:"py-12 sm:py-16 bg-white/30",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6",children:[(0,d.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-center text-gray-900 mb-8 sm:mb-12",children:"Recently Generated Pages"}),(0,d.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:a.map(a=>(0,d.jsxs)(h(),{href:`/${a.slug}`,className:"group block bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-200 border border-gray-100 hover:border-blue-200 overflow-hidden flex flex-col h-full",children:[(0,d.jsxs)("div",{className:"p-4 sm:p-6 flex-1",children:[(0,d.jsx)("div",{className:"flex items-start justify-between mb-3",children:(0,d.jsx)("h3",{className:"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2 text-sm sm:text-base",children:a.title})}),(0,d.jsxs)("div",{className:"flex items-center text-gray-500 text-xs sm:text-sm",children:[(0,d.jsx)(i.A,{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0"}),(0,d.jsx)("span",{children:(a=>{let b=Math.floor((new Date().getTime()-a.getTime())/6e4);if(b<1)return"Just now";if(b<60)return`${b}m ago`;let c=Math.floor(b/60);if(c<24)return`${c}h ago`;let d=Math.floor(c/24);return d<7?`${d}d ago`:a.toLocaleDateString()})(a.lastModified)})]}),(0,d.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-100",children:(0,d.jsxs)("p",{className:"text-xs text-gray-600 font-mono break-all",children:["everywebsite.ai/",a.slug]})})]}),(0,d.jsx)("div",{className:"h-1 bg-gradient-to-r from-blue-500 to-indigo-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-200 origin-left"})]},a.slug))}),(0,d.jsx)("div",{className:"text-center mt-8",children:(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:"These pages were generated by other users. Click to view them!"})})]})})}var k=c(7040);let l=(0,k.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var m=c(2877);let n=(0,k.A)("coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]]);var o=c(9803);function p(){return(0,d.jsx)("section",{className:"py-12 sm:py-16 bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50",children:(0,d.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 text-center",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-6 sm:p-8 lg:p-10 border border-gray-100",children:[(0,d.jsx)("div",{className:"mx-auto w-16 h-16 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-6",children:(0,d.jsx)(l,{className:"w-8 h-8 text-purple-600"})}),(0,d.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-4",children:"Love Every Website AI?"}),(0,d.jsx)("p",{className:"text-lg text-gray-600 mb-6 max-w-2xl mx-auto",children:"This tool is completely free to use! If you're finding it helpful, consider supporting the creator by following on X (Twitter)."}),(0,d.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 mb-8",children:[(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:"100%"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Free to Use"})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-1",children:"∞"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Unlimited Pages*"})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:"AI"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Powered"})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(o.$,{asChild:!0,size:"lg",className:"bg-black hover:bg-gray-800 text-white px-8 py-3 text-lg font-semibold",children:(0,d.jsxs)("a",{href:"https://x.com/N3SOnline",target:"_blank",rel:"noopener noreferrer",className:"flex items-center justify-center gap-3",children:[(0,d.jsx)(m.A,{className:"w-5 h-5"}),"Follow @N3SOnline"]})}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Get updates on new features and other cool projects!"})]}),(0,d.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Want to support in other ways?"}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,d.jsx)(o.$,{asChild:!0,variant:"outline",className:"border-purple-200 text-purple-700 hover:bg-purple-50",children:(0,d.jsxs)("a",{href:"https://dothistask.ai",target:"_blank",rel:"noopener noreferrer",className:"flex items-center justify-center gap-2",children:[(0,d.jsx)(n,{className:"w-4 h-4"}),"Check out DoThisTask.ai"]})}),(0,d.jsx)(o.$,{asChild:!0,variant:"outline",className:"border-blue-200 text-blue-700 hover:bg-blue-50",children:(0,d.jsxs)("a",{href:"https://x.com/N3SOnline",target:"_blank",rel:"noopener noreferrer",className:"flex items-center justify-center gap-2",children:[(0,d.jsx)(l,{className:"w-4 h-4"}),"Share this tool"]})})]})]}),(0,d.jsx)("div",{className:"mt-6 text-xs text-gray-500",children:(0,d.jsx)("p",{children:"* Subject to fair usage limits to keep the service free for everyone"})})]})})})}async function q(){let a=await (0,e.Yx)(12);return(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",children:[(0,d.jsx)("header",{className:"pt-6 sm:pt-8 pb-4 text-center",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6",children:[(0,d.jsx)("h1",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4",children:"Every Website AI"}),(0,d.jsx)("p",{className:"text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-2",children:"Generate any webpage instantly with AI. Just change the URL to describe what you want."}),(0,d.jsx)("div",{className:"mt-6 p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-200 max-w-xl mx-auto",children:(0,d.jsxs)("p",{className:"text-blue-800 font-mono text-sm sm:text-base lg:text-lg break-all",children:["everywebsite.ai/",(0,d.jsx)("span",{className:"bg-blue-200 px-1 sm:px-2 py-1 rounded",children:"your-prompt-here"})]})})]})}),(0,d.jsx)("main",{className:"flex-1 flex items-center justify-center px-4 sm:px-6 py-8 sm:py-12",children:(0,d.jsx)(f.default,{})}),(0,d.jsx)(j,{pages:a}),(0,d.jsx)("section",{className:"py-12 sm:py-16 bg-white/50",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6",children:[(0,d.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-center text-gray-900 mb-8 sm:mb-12",children:"How it works"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)("span",{className:"text-blue-600 font-bold text-lg",children:"1"})}),(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-2 text-base sm:text-lg",children:"Change the URL"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm sm:text-base px-2",children:"Add your prompt to the URL: everywebsite.ai/your-idea"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)("span",{className:"text-blue-600 font-bold text-lg",children:"2"})}),(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-2 text-base sm:text-lg",children:"AI Generates"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm sm:text-base px-2",children:"Our AI instantly creates a complete, responsive webpage"})]}),(0,d.jsxs)("div",{className:"text-center sm:col-span-2 lg:col-span-1",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)("span",{className:"text-blue-600 font-bold text-lg",children:"3"})}),(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-2 text-base sm:text-lg",children:"That's it!"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm sm:text-base px-2",children:"Your custom webpage is ready to use and share"})]})]})]})}),(0,d.jsx)(p,{}),(0,d.jsx)("footer",{className:"py-6 sm:py-8 text-center text-gray-500 text-xs sm:text-sm px-4",children:(0,d.jsxs)("p",{className:"flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-0",children:[(0,d.jsxs)("span",{children:["Powered by"," ",(0,d.jsx)("a",{href:"https://dothistask.ai",className:"underline hover:text-gray-700",children:"dothistask.ai"})]}),(0,d.jsx)("span",{className:"hidden sm:inline",children:" • "}),(0,d.jsx)("a",{href:"https://twitter.com/n3sonline",className:"underline hover:text-gray-700",children:"@n3sonline"})]})})]})}},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},6872:()=>{},7490:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,7910,23)),Promise.resolve().then(c.bind(c,2823))},7962:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(9140);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},8354:a=>{"use strict";a.exports=require("util")},8600:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,8613,23)),Promise.resolve().then(c.t.bind(c,3852,23)),Promise.resolve().then(c.t.bind(c,5442,23)),Promise.resolve().then(c.t.bind(c,8981,23)),Promise.resolve().then(c.t.bind(c,9105,23)),Promise.resolve().then(c.t.bind(c,7673,23)),Promise.resolve().then(c.t.bind(c,2801,23)),Promise.resolve().then(c.t.bind(c,5971,23)),Promise.resolve().then(c.bind(c,3793))},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9351:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(9861);c(3440);var e=c(1250),f=c(6153),g=c(574),h=c(2490);let i=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function j({className:a,variant:b,size:c,asChild:f=!1,...j}){let k=f?e.DX:"button";return(0,d.jsx)(k,{"data-slot":"button",className:function(...a){return(0,h.QP)((0,g.$)(a))}(i({variant:b,size:c,className:a})),...j})}},9803:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(8487);c(4914);var e=c(6406),f=c(8783),g=c(8036),h=c(1252);let i=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function j({className:a,variant:b,size:c,asChild:f=!1,...j}){let k=f?e.DX:"button";return(0,d.jsx)(k,{"data-slot":"button",className:function(...a){return(0,h.QP)((0,g.$)(a))}(i({variant:b,size:c,className:a})),...j})}}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[505,926,232],()=>b(b.s=4445));module.exports=c})();