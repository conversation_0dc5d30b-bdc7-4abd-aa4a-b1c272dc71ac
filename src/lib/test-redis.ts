// Test file to verify Upstash Redis connection
// You can run this to test your Redis setup

import { getRedisClient, checkRateLimit } from "./redis";

export async function testRedisConnection() {
  try {
    const client = getRedisClient();
    
    // Test basic operations
    await client.set("test_key", "test_value");
    const value = await client.get("test_key");
    console.log("Redis test value:", value);
    
    // Clean up
    await client.del("test_key");
    
    console.log("✅ Upstash Redis connection successful!");
    return true;
  } catch (error) {
    console.error("❌ Upstash Redis connection failed:", error);
    return false;
  }
}

export async function testRateLimit() {
  try {
    const testIP = "*************";
    
    // Test rate limiting
    const result1 = await checkRateLimit(testIP);
    console.log("Rate limit test 1:", result1);
    
    const result2 = await checkRateLimit(testIP);
    console.log("Rate limit test 2:", result2);
    
    const result3 = await checkRateLimit(testIP);
    console.log("Rate limit test 3:", result3);
    
    // This should trigger rate limit
    const result4 = await checkRateLimit(testIP);
    console.log("Rate limit test 4 (should be blocked):", result4);
    
    console.log("✅ Rate limiting test completed!");
    return true;
  } catch (error) {
    console.error("❌ Rate limiting test failed:", error);
    return false;
  }
}

// Uncomment to run tests
// testRedisConnection();
// testRateLimit();
